// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-existing-docker-compose
{
	"name": "Core",

	// Update the 'dockerComposeFile' list if you have more compose files or use different names.
	// The .devcontainer/docker-compose.yml file contains any overrides you need/want to make.
	"dockerComposeFile": [
		"../docker-compose.yml",
	],

	// The 'service' property is the name of the service for the container that VS Code should
	// use. Update this value and .devcontainer/docker-compose.yml to the real service name.
	"service": "api_us",

	// The optional 'workspaceFolder' property is the path VS Code should open by default when
	// connected. This is typically a file mount in .devcontainer/docker-compose.yml
	"workspaceFolder": "/home/<USER>/code",
	"remoteUser": "booksy",

	"customizations": {
		"vscode": {
			"extensions": [
				"ms-python.python",
				"ms-python.vscode-pylance",
				"ms-python.debugpy",
				// "littlefoxteam.vscode-python-test-adapter",
				"ms-azuretools.vscode-docker",
				"eamodio.gitlens"
			],
			"settings": {
				"python.defaultInterpreterPath": "/usr/local/bin/python",
				"python.analysis.extraPaths": [
					"/home/<USER>/code",
				],
				"python.testing.pytestEnabled": true,
				"python.testing.unittestEnabled": false,
				"python.testing.pytestArgs": [
					"/home/<USER>/code",
				],
				"python.linting.enabled": true,
				"python.linting.pylintEnabled": true,
				"python.formatting.provider": "black"
			}
		}
	},

	"mounts": [
		"source=${localEnv:HOME}/.config/gcloud,target=/root/.config/gcloud,type=bind,consistency=cached"
	],

	"runArgs": ["--secret=id=gcp,src=${localEnv:HOME}/.config/gcloud/application_default_credentials.json"],

	// Custom build command - runs before container is created/started
	"overrideCommand": true,
	"initializeCommand": "./commands/build_container.sh api us",

	"shutdownAction": "stopCompose",

	// Features to add to the dev container. More info: https://containers.dev/features.
	// "features": {},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],

	// Only start these specific services (prevents building/starting others)
	"runServices": ["api_us"]

	// Uncomment the next line if you want to keep your containers running after VS Code shuts down.
	// "shutdownAction": "none",

	// Uncomment the next line to run commands after the container is created.
	// "postCreateCommand": "cat /etc/os-release",

	// Configure tool-specific properties.
	// "customizations": {},

	// Uncomment to connect as an existing user other than the container default. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "devcontainer"
}
