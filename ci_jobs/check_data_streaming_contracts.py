#!/usr/bin/env python
import base64
import os
import typing as t

import django
import requests

from dataclasses_avroschema import types
from data_streaming_contracts.adapters import (
    SchemaRegistryConfig,
    DjangoSchemaRepository,
    RedpandaSchemaRepository,
    RedpandaDataContractValidator,
)
from data_streaming_contracts.application.output import ReportFormatter
from data_streaming_contracts.application.services import DataStreamingContractService

from lib.interval.fields import relativedelta_tobigint

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()  # NOQA

# List of models the data_streaming_contracts should verify.
DATA_CONTRACTS_MODELS = [
    'business.Business',
    'business.Resource',
    'schedule.ResourceHours',
]

schema_registry_password = os.environ.get('SCHEMA_REGISTRY_EU_PASSWORD')
if schema_registry_password:
    schema_registry_password = base64.b64decode(schema_registry_password.encode('utf-8')).decode(
        'utf-8'
    )
schema_registry_host = os.environ.get('SCHEMA_REGISTRY_EU_HOST')

config = {
    'live': schema_registry_host is not None,
    'schema_registry': {
        'scheme': 'https' if schema_registry_host else 'http',
        'host': schema_registry_host or 'redpanda:8081',
        'username': os.environ.get('SCHEMA_REGISTRY_EU_USERNAME', 'admin'),
        'password': schema_registry_password or 'redpanda',
    },
}


def business_custom_converter(_, field):
    if field.name == 'booking_max_lead_time':
        return int, relativedelta_tobigint(field.get_default())
    if field.name == 'id':
        return types.Int32, 0


def resource_custom_converter(_, field):
    if field.name == 'id':
        return types.Int32, 0


def resource_hours_custom_converter(_, field):
    if field.name in {'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'}:
        return t.Optional[t.List[t.Optional[t.List[t.Optional[str]]]]], None


def main():
    schema_registry_config = SchemaRegistryConfig(
        config['schema_registry']['scheme'],
        config['schema_registry']['host'],
        config['schema_registry']['username'],
        config['schema_registry']['password'],
    )

    django_registry = DjangoSchemaRepository(
        subject_namespace='com.booksy.pl' if config['live'] else 'localhost.us',
        subject_classification='cdc',
        models=DATA_CONTRACTS_MODELS,
        custom_model_converters={
            'business.Business': business_custom_converter,
            'business.Resource': resource_custom_converter,
            'schedule.ResourceHours': resource_hours_custom_converter,
        },
    )

    service = DataStreamingContractService(
        verified_schema_repository=RedpandaSchemaRepository(schema_registry_config),
        unverified_schema_repository=django_registry,
        data_contract_validator=RedpandaDataContractValidator(schema_registry_config),
    )

    reports = service.call()
    ReportFormatter.print(reports)

    if not all(r.is_compatible for r in reports):
        maybe_notify_slack(reports)
        raise SystemExit('Not all data streaming contracts are compatible.')


def maybe_notify_slack(reports):
    if 'CI' not in os.environ:
        return

    message_lines = []
    message_lines.append('Detected incompatible CDC schema changes!')
    message_lines.append(
        'Your changes to django models introduced a migration that is'
        ' not compatible with schema registered in Redpanda.'
    )
    message_lines.append(
        'Please review your changes to ensure they are not breaching'
        ' the data contracts between database and schema registry.'
    )

    message_lines.append(f'\nJOB URL: {os.environ["CI_JOB_URL"]}')
    message_lines.append(f'Author: {os.environ["CI_COMMIT_AUTHOR"]}')
    message_lines.append(
        'Read more about schema evolution here: https://docs.confluent.io/'
        'platform/current/schema-registry/fundamentals/schema-evolution.html'
    )

    message_lines.append('\nSummary of data streaming contracts and expected compatibility level:')
    message_lines.append('```')

    row_format = '{:<60}|{:<40}|{:<20}'

    message_lines.append(row_format.format('Subject', 'Compatibility Level', 'Is Compatible'))
    message_lines.append('-' * 120)

    for report in reports:
        if report.is_compatible:
            continue

        message_lines.append(
            row_format.format(
                report.data_contract.schema_before.subject,
                report.data_contract.compatibility_level,
                'YES' if report.is_compatible else 'NO',
            )
        )
    message_lines.append('```')

    # Add diffs for incompatible subjects
    for report in reports:
        if report.is_compatible:
            break

        message_lines.append(f'Diff ({report.data_contract.schema_before.subject}):')
        message_lines.append('```')
        message_lines.append(report.data_contract.get_fields_diff(as_diff=True, sort_by_name=True))
        message_lines.append('```')

    data = {
        'token': os.environ['DATA_STREAMING_CONTRACTS_SLACK_TOKEN'],
        'channel': 'C087HDUEUKA',  # data_streaming_contracts_breaches channel
        'as_user': True,
        'text': '\n'.join(message_lines),
    }

    resp = requests.post(url='https://slack.com/api/chat.postMessage', data=data, timeout=7)
    resp_data = resp.json()
    if not resp_data['ok']:
        raise SystemExit(f'Error posting message to slack, got {resp_data}')


if __name__ == '__main__':
    main()
