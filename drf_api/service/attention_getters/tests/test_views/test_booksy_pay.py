# pylint: disable=too-many-positional-arguments
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import MagicMock, PropertyMock, patch
from urllib.parse import urlencode
from uuid import uuid4

import pytest
from django.urls import reverse
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status

from drf_api.service.attention_getters.consts.booksy_pay import ANY, NA
from drf_api.service.attention_getters.content.booksy_pay import (
    BooksyPayAttentionGetterContentFactory,
    BooksyPayAttentionGettersContentAfterPayment,
    BooksyPayAttentionGettersContentBeforePayment,
    BooksyPayAttentionGettersContentBookingConfirmation,
)
from drf_api.service.attention_getters.enums.base import AttentionGetterStep
from drf_api.service.attention_getters.enums.booksy_pay import BooksyPayAttentionGetterName
from drf_api.service.attention_getters.serializers.base import ContentSerializer
from drf_api.service.attention_getters.utils.booksy_pay import (
    get_booksy_pay_attention_getter,
    get_booksy_pay_attention_getter_analytics,
)
from drf_api.service.tests.base import AuthenticatedCustomerAPITestCase
from lib.feature_flag.experiment.booksy_pay import BooksyPayCashbackExperiment
from lib.feature_flag.feature.booksy_pay import BooksyPayCashbackPromoFlag, BooksyPayFlag
from lib.tests.utils import override_eppo_feature_flag, override_feature_flag
from webapps.booking.models import Appointment
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import business_recipe, service_recipe, service_variant_recipe
from webapps.business.enums import PriceType
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.family_and_friends.tests.utils import (
    TestFamilyAndFriendsMixin,
    create_appointment_for_member,
)
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models.stripe import StripeAccountHolderSettings
from webapps.pos.baker_recipes import posplan_recipe
from webapps.booksy_pay.cashback import (
    BooksyPayCashbackDefault,
    BooksyPayCashbackVariantA,
    BooksyPayCashbackVariantB,
    BooksyPayCashbackVariantC,
)
from webapps.booksy_pay.consts import (
    FIRST_CASHBACK_PROMO_END_DATE,
    SECOND_CASHBACK_PROMO_START_DATE,
)
from webapps.booksy_pay.enums import BooksyPayCashbackAmountType, BooksyPayCashbackType
from webapps.pos.enums import PaymentTypeEnum, POSPlanPaymentTypeEnum, receipt_status
from webapps.pos.models import PaymentMethod, PaymentRow, Receipt, Transaction
from webapps.pos.provider.fake import _CARDS
from webapps.pos.serializers import TransactionSerializer
from webapps.stripe_integration.enums import StripeAccountStatus
from webapps.stripe_integration.models import StripeAccount, StripeCustomer
from webapps.user.baker_recipes import customer_profile
from webapps.user.models import UserProfile

CASHBACK_VARIANTS = {
    BooksyPayCashbackType.DEFAULT: BooksyPayCashbackDefault(
        amount_type=BooksyPayCashbackAmountType.FIXED,
        amount=Decimal('10.00'),
        currency='PLN',
        min_booking_amount=Decimal('50.00'),
        required_num_of_txns=1,
    ),
    BooksyPayCashbackType.VARIANT_A: BooksyPayCashbackVariantA(
        amount_type=BooksyPayCashbackAmountType.FIXED,
        amount=Decimal('20.00'),
        currency='PLN',
        min_booking_amount=Decimal('50.00'),
        required_num_of_txns=1,
    ),
    BooksyPayCashbackType.VARIANT_B: BooksyPayCashbackVariantB(
        amount_type=BooksyPayCashbackAmountType.FIXED,
        amount=Decimal('30.00'),
        currency='PLN',
        min_booking_amount=None,
        max_cashback_amount=Decimal('20.00'),
        required_num_of_txns=1,
    ),
    BooksyPayCashbackType.VARIANT_C: BooksyPayCashbackVariantC(
        amount_type=BooksyPayCashbackAmountType.FIXED,
        amount=Decimal('40.00'),
        currency='PLN',
        min_booking_amount=Decimal('50.00'),
        required_num_of_txns=2,
    ),
}


@patch(
    'drf_api.service.attention_getters.serializers.booksy_pay.CASHBACK_VARIANTS',
    new=CASHBACK_VARIANTS,
)
@patch('webapps.booksy_pay.cashback.CASHBACK_VARIANTS', new=CASHBACK_VARIANTS)
@override_eppo_feature_flag({'Feature_BooksyPayCashbackPromo': True})
class BooksyPayAttentionGetterViewTestCase(AuthenticatedCustomerAPITestCase):

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        # Set up content
        BooksyPayAttentionGetterContentFactory._registry = {}  # pylint: disable=protected-access
        BooksyPayAttentionGettersContentBookingConfirmation.register(
            cashback_variants=CASHBACK_VARIANTS
        )
        BooksyPayAttentionGettersContentBeforePayment.register(cashback_variants=CASHBACK_VARIANTS)
        BooksyPayAttentionGettersContentAfterPayment.register(cashback_variants=CASHBACK_VARIANTS)

        # Add a user profile
        customer_profile.make(user=cls.user)

    def setUp(self):
        super().setUp()

        self.appointment = create_appointment(
            [{}], total_value=Decimal('50.00'), booked_for__user=self.user
        )

    def _call(
        self,
        step: AttentionGetterStep,
        language: str | None = None,
        headers_language: str | None = None,
        appointment: Appointment | None = None,
    ):
        appointment = appointment or self.appointment
        path = reverse('attention_getter_booksy_pay')
        query_params = {
            'appointment_id': appointment.id,
            'step': str(step),
        }
        extra_headers = {}

        if language:
            profile = self.user.profiles.get(profile_type=UserProfile.Type.CUSTOMER)
            profile.language = language
            profile.save()

        if headers_language:
            extra_headers = {'HTTP_ACCEPT_LANGUAGE': headers_language}

        url = f'{path}?{urlencode(query_params)}'

        return self.client.get(url, **self.headers, **extra_headers)

    @staticmethod
    def _get_attention_getter_analytics(
        attention_getter_name: BooksyPayAttentionGetterName, eppo_variant: str
    ) -> dict:
        try:
            cashback_type = BooksyPayCashbackType(eppo_variant.lower())
        except ValueError:
            cashback_type = ANY

        return get_booksy_pay_attention_getter_analytics(attention_getter_name, cashback_type)

    @parameterized.expand(
        [
            ('', False, True, NA, BooksyPayAttentionGetterName.CASHBACK_INITIAL),
            ('', False, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED_CASHBACK),
            ('', True, True, NA, BooksyPayAttentionGetterName.INTRO_BP_OPEN),
            ('', True, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED),
            ('control', NA, True, NA, BooksyPayAttentionGetterName.INTRO_BP_OPEN),
            ('control', NA, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED),
            ('variant_A', False, True, NA, BooksyPayAttentionGetterName.CASHBACK_INITIAL),
            ('variant_A', False, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED_CASHBACK),
            ('variant_A', True, True, NA, BooksyPayAttentionGetterName.INTRO_BP_OPEN),
            ('variant_A', True, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED),
            ('variant_B', False, True, NA, BooksyPayAttentionGetterName.CASHBACK_INITIAL),
            ('variant_B', False, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED_CASHBACK),
            ('variant_B', True, True, NA, BooksyPayAttentionGetterName.INTRO_BP_OPEN),
            ('variant_B', True, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED),
            ('variant_C', False, True, False, BooksyPayAttentionGetterName.CASHBACK_INITIAL),
            ('variant_C', False, True, True, BooksyPayAttentionGetterName.CASHBACK_INITIAL),
            (
                'variant_C',
                False,
                False,
                False,
                BooksyPayAttentionGetterName.INTRO_BP_CLOSED_CASHBACK,
            ),
            (
                'variant_C',
                False,
                False,
                True,
                BooksyPayAttentionGetterName.INTRO_BP_CLOSED_CASHBACK,
            ),
            ('variant_C', True, True, NA, BooksyPayAttentionGetterName.INTRO_BP_OPEN),
            ('variant_C', True, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED),
        ]
    )
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_available',
        new_callable=PropertyMock,
    )
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_payment_window_open',
        new_callable=PropertyMock,
    )
    @patch('webapps.booksy_pay.cashback.has_finished_booksy_pay_transaction')
    @patch('drf_api.service.attention_getters.views.booksy_pay.has_finished_booksy_pay_transaction')
    def test_retrieve_attention_getter_booking_confirmation(
        self,
        eppo_variant: str,
        has_received_cashback_already: bool,
        is_booksy_pay_payment_window_open: bool,
        has_first_bp_transaction: bool,
        expected_attention_getter_name: BooksyPayAttentionGetterName,
        mock_drf_api_has_finished_booksy_pay_transaction: MagicMock,
        mock_webapps_has_finished_booksy_pay_transaction: MagicMock,
        mock_is_booksy_pay_payment_window_open: MagicMock,
        mock_is_booksy_pay_available: MagicMock,
    ) -> None:
        """
        NOTE: at the current stage of development, `CASHBACK_INITIAL` is always expected
        as as cashback version, despite the experiment value.
        """
        mock_webapps_has_finished_booksy_pay_transaction.return_value = (
            has_received_cashback_already
        )
        mock_is_booksy_pay_payment_window_open.return_value = is_booksy_pay_payment_window_open
        mock_is_booksy_pay_available.return_value = True
        mock_drf_api_has_finished_booksy_pay_transaction.return_value = has_first_bp_transaction
        expected_analytics = self._get_attention_getter_analytics(
            expected_attention_getter_name, ''
        )
        expected_attention_getter = get_booksy_pay_attention_getter(
            AttentionGetterStep.BOOKING_CONFIRMATION, expected_attention_getter_name
        )

        with (
            override_eppo_feature_flag({BooksyPayCashbackExperiment.flag_name: eppo_variant}),
            override_eppo_feature_flag({BooksyPayCashbackPromoFlag.flag_name: True}),
        ):
            response = self._call(AttentionGetterStep.BOOKING_CONFIRMATION)

        response_json = response.json()

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(
            response_json['attention_getter'],
            {
                'name': expected_attention_getter_name,
                'step': 'booking_confirmation',
                'type': 'booksy_pay',
            },
        )
        self.assertDictEqual(
            response_json['metadata'],
            {
                'booksy_pay': {
                    'is_available': True,
                    'is_paid': False,
                    'is_payment_window_open': is_booksy_pay_payment_window_open,
                },
                'cashback': {
                    'experiment_variant': '',
                    'is_eligible': (
                        bool(
                            NA  # Applicable for the control group
                            if has_received_cashback_already is NA
                            else not has_received_cashback_already
                        )
                    ),
                },
                'analytics': expected_analytics,
            },
        )
        self.assertDictEqual(
            response_json['content'],
            ContentSerializer(expected_attention_getter.content_callable()).data,
        )

    @parameterized.expand(
        [
            ('', False, True, NA, BooksyPayAttentionGetterName.CASHBACK_INITIAL),
            ('', False, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED_CASHBACK),
            ('', True, True, NA, BooksyPayAttentionGetterName.INTRO_BP_OPEN),
            ('', True, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED),
            ('control', NA, True, NA, BooksyPayAttentionGetterName.INTRO_BP_OPEN),
            ('control', NA, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED),
            ('variant_A', False, True, NA, BooksyPayAttentionGetterName.CASHBACK_VA),
            ('variant_A', False, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED_CASHBACK),
            ('variant_A', True, True, NA, BooksyPayAttentionGetterName.INTRO_BP_OPEN),
            ('variant_A', True, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED),
            ('variant_B', False, True, NA, BooksyPayAttentionGetterName.CASHBACK_VB),
            ('variant_B', False, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED_CASHBACK),
            ('variant_B', True, True, NA, BooksyPayAttentionGetterName.INTRO_BP_OPEN),
            ('variant_B', True, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED),
            ('variant_C', False, True, False, BooksyPayAttentionGetterName.CASHBACK_VC_APPT_1),
            ('variant_C', False, True, True, BooksyPayAttentionGetterName.CASHBACK_VC_APPT_2),
            (
                'variant_C',
                False,
                False,
                False,
                BooksyPayAttentionGetterName.INTRO_BP_CLOSED_CASHBACK,
            ),
            (
                'variant_C',
                False,
                False,
                True,
                BooksyPayAttentionGetterName.INTRO_BP_CLOSED_CASHBACK,
            ),
            ('variant_C', True, True, NA, BooksyPayAttentionGetterName.INTRO_BP_OPEN),
            ('variant_C', True, False, NA, BooksyPayAttentionGetterName.INTRO_BP_CLOSED),
        ]
    )
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_available',
        new_callable=PropertyMock,
    )
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_payment_window_open',
        new_callable=PropertyMock,
    )
    @patch('webapps.booksy_pay.cashback.has_finished_booksy_pay_transaction')
    @patch('drf_api.service.attention_getters.views.booksy_pay.has_finished_booksy_pay_transaction')
    def test_retrieve_attention_getter_before_payment(
        self,
        eppo_variant: str,
        has_received_cashback_already: bool,
        is_booksy_pay_payment_window_open: bool,
        has_first_bp_transaction: bool,
        expected_attention_getter_name: BooksyPayAttentionGetterName,
        mock_drf_api_has_finished_booksy_pay_transaction: MagicMock,
        mock_webapps_has_finished_booksy_pay_transaction: MagicMock,
        mock_is_booksy_pay_payment_window_open: MagicMock,
        mock_is_booksy_pay_available: MagicMock,
    ) -> None:
        mock_webapps_has_finished_booksy_pay_transaction.return_value = (
            has_received_cashback_already
        )
        mock_is_booksy_pay_payment_window_open.return_value = is_booksy_pay_payment_window_open
        mock_is_booksy_pay_available.return_value = True
        mock_drf_api_has_finished_booksy_pay_transaction.return_value = has_first_bp_transaction
        expected_analytics = self._get_attention_getter_analytics(
            expected_attention_getter_name, eppo_variant
        )
        expected_attention_getter = get_booksy_pay_attention_getter(
            AttentionGetterStep.BEFORE_PAYMENT, expected_attention_getter_name
        )

        with override_eppo_feature_flag({BooksyPayCashbackExperiment.flag_name: eppo_variant}):
            response = self._call(AttentionGetterStep.BEFORE_PAYMENT)

        response_json = response.json()

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(
            response_json['attention_getter'],
            {
                'name': expected_attention_getter_name,
                'step': 'before_payment',
                'type': 'booksy_pay',
            },
        )
        self.assertDictEqual(
            response_json['metadata'],
            {
                'booksy_pay': {
                    'is_available': True,
                    'is_paid': False,
                    'is_payment_window_open': is_booksy_pay_payment_window_open,
                },
                'cashback': {
                    'experiment_variant': eppo_variant,
                    'is_eligible': (
                        bool(
                            NA  # Applicable for the control group
                            if has_received_cashback_already is NA
                            else not has_received_cashback_already
                        )
                    ),
                },
                'analytics': expected_analytics,
            },
        )
        self.assertDictEqual(
            response_json['content'],
            ContentSerializer(expected_attention_getter.content_callable()).data,
        )

    @parameterized.expand(
        [
            ('', False, NA, BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY),  # Initial cashback
            ('', True, NA, None),
            ('control', NA, NA, None),
            ('variant_A', False, NA, BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY),  # VA
            ('variant_A', True, NA, None),
            ('variant_B', False, NA, BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY),  # VB
            ('variant_B', True, NA, None),
            (
                'variant_C',
                False,
                False,
                BooksyPayAttentionGetterName.CASHBACK_VC_AFTER_FIRST_PAYMENT,
            ),  # VC_1
            ('variant_C', False, True, BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY),  # VC_2
            ('variant_C', True, NA, None),
        ]
    )
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_available',
        new_callable=PropertyMock,
    )
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_payment_window_open',
        new_callable=PropertyMock,
    )
    @patch('webapps.booksy_pay.cashback.has_finished_booksy_pay_transaction')
    @patch('drf_api.service.attention_getters.views.booksy_pay.has_finished_booksy_pay_transaction')
    def test_retrieve_attention_getter_after_payment(
        self,
        eppo_variant: str,
        has_received_cashback_already: bool,
        has_first_bp_transaction: bool,
        expected_attention_getter_name: BooksyPayAttentionGetterName,
        mock_drf_api_has_finished_booksy_pay_transaction: MagicMock,
        mock_webapps_has_finished_booksy_pay_transaction: MagicMock,
        mock_is_booksy_pay_payment_window_open: MagicMock,
        mock_is_booksy_pay_available: MagicMock,
    ) -> None:
        mock_webapps_has_finished_booksy_pay_transaction.return_value = (
            has_received_cashback_already
        )
        mock_is_booksy_pay_payment_window_open.return_value = False
        mock_is_booksy_pay_available.return_value = False
        mock_drf_api_has_finished_booksy_pay_transaction.return_value = has_first_bp_transaction
        expected_analytics = self._get_attention_getter_analytics(
            expected_attention_getter_name, eppo_variant
        )
        expected_attention_getter = get_booksy_pay_attention_getter(
            AttentionGetterStep.AFTER_PAYMENT, expected_attention_getter_name
        )

        with override_eppo_feature_flag({BooksyPayCashbackExperiment.flag_name: eppo_variant}):
            response = self._call(AttentionGetterStep.AFTER_PAYMENT)

        if expected_attention_getter_name is None:
            self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
            self.assertEqual(response.content, b'')
        else:
            response_json = response.json()
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertDictEqual(
                response_json['attention_getter'],
                {
                    'name': expected_attention_getter_name,
                    'step': 'after_payment',
                    'type': 'booksy_pay',
                },
            )
            self.assertDictEqual(
                response_json['metadata'],
                {
                    'booksy_pay': {
                        'is_available': False,
                        'is_paid': False,
                        'is_payment_window_open': False,
                    },
                    'cashback': {
                        'experiment_variant': eppo_variant,
                        'is_eligible': (
                            bool(
                                NA  # Applicable for the control group
                                if has_received_cashback_already is NA
                                else not has_received_cashback_already
                            )
                        ),
                    },
                    'analytics': expected_analytics,
                },
            )
            self.assertDictEqual(
                response_json['content'],
                ContentSerializer(expected_attention_getter.content_callable()).data,
            )

    @parameterized.expand(
        [
            (
                'en',
                'en_GB',
                '',
                'Complete payment with Booksy Pay and get 10 PLN cashback!',
            ),
            ('en', 'en_GB', 'control', 'Introducing Booksy Pay'),
            (
                'en_GB',
                'en_GB',
                'variant_A',
                'Complete payment with Booksy Pay and get 20 PLN cashback!',
            ),
            ('en', 'en_GB', 'variant_B', 'Complete payment with Booksy Pay and get 30% cashback!'),
            ('en', None, 'variant_C', 'Complete payment with Booksy Pay and get 40 PLN cashback!'),
            ('pl', 'pl_PL', '', 'Zgarnij 10 PLN cashbacku płacąc z Booksy Pay'),
            ('pl', 'pl_PL', 'control', 'Poznaj Booksy Pay'),
            (
                'pl',
                'pl_PL',
                'variant_A',
                'Zgarnij 20 PLN cashbacku płacąc z Booksy Pay',
            ),
            ('pl', 'pl_PL', 'variant_B', 'Zgarnij 30% cashbacku płacąc z Booksy Pay'),
            ('pl', 'pl_PL', 'variant_C', 'Zgarnij 40 PLN cashbacku płacąc z Booksy Pay'),
            (
                'pl',
                'en_PL',
                'variant_C',
                'Complete payment with Booksy Pay and get 40 PLN cashback!',
            ),
        ]
    )
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_available',
        new_callable=PropertyMock,
        return_value=True,
    )
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_payment_window_open',
        new_callable=PropertyMock,
        return_value=True,
    )
    @patch('webapps.booksy_pay.cashback.has_finished_booksy_pay_transaction', return_value=False)
    @patch(
        'drf_api.service.attention_getters.views.booksy_pay.has_finished_booksy_pay_transaction',
        return_value=False,
    )
    def test_retrieve_attention_getter_i18n(
        self,
        language: str,
        headers_language: str,
        eppo_variant: str,
        expected_title: str,
        mock_drf_api_has_finished_booksy_pay_transaction: MagicMock,
        mock_webapps_has_finished_booksy_pay_transaction: MagicMock,
        mock_is_booksy_pay_payment_window_open: MagicMock,
        mock_is_booksy_pay_available: MagicMock,
    ) -> None:
        with override_eppo_feature_flag({BooksyPayCashbackExperiment.flag_name: eppo_variant}):
            response = self._call(AttentionGetterStep.BEFORE_PAYMENT, language, headers_language)

        response_json = response.json()

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_json['content']['title'], expected_title)

    @parameterized.expand(['', 'control', 'variant_A', 'variant_B', 'variant_C'])
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_available',
        new_callable=PropertyMock,
    )
    def test_retrieve_no_attention_getter(
        self,
        eppo_variant: str,
        mock_is_booksy_pay_available: MagicMock,
    ) -> None:
        mock_is_booksy_pay_available.return_value = False

        with override_eppo_feature_flag({BooksyPayCashbackExperiment.flag_name: eppo_variant}):
            # Note - we can only check the `BEFORE_PAYMENT` case here. Why?
            # In the case of the after payment screen, we need to determine the attention getter
            # shown before payment. As we don't track (intentionally) attention getters shown
            # before, it's quite difficult to get the exact screen. Therefore, there's a heuristic
            # mechanism to determine the previously presented screen, which assumes the BP was
            # available (with window open) before payment, leveraging the fact that if someone
            # queries for the `after_payment` screen, it means the BP window must have been
            # opened before (otherwise the payment couldn't be processed).
            response = self._call(AttentionGetterStep.BEFORE_PAYMENT)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(response.content, b'')

    @parameterized.expand([False, True])
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_available',
        new_callable=PropertyMock,
    )
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_payment_window_open',
        new_callable=PropertyMock,
    )
    @patch('webapps.booksy_pay.cashback.has_finished_booksy_pay_transaction')
    @patch('drf_api.service.attention_getters.views.booksy_pay.has_finished_booksy_pay_transaction')
    def test_retrieve_attention_getter_for_family_and_friends(
        self,
        friend_has_booksy_account: bool,
        mock_drf_api_has_finished_booksy_pay_transaction: MagicMock,
        mock_webapps_has_finished_booksy_pay_transaction: MagicMock,
        mock_is_booksy_pay_payment_window_open: MagicMock,
        mock_is_booksy_pay_available: MagicMock,
    ) -> None:
        # Set up family and friends related data
        business = business_recipe.make()
        parent_bci, friend_bci = TestFamilyAndFriendsMixin.create_active_members_bcis(
            business=business
        )
        parent_bci.user = self.user
        parent_bci.save()
        friend_bci.user = friend_bci.user if friend_has_booksy_account else None
        friend_bci.save()
        appointment = create_appointment_for_member(
            parent_bci=parent_bci,
            member_bci=friend_bci,
        )

        # Initialize test scenario
        eppo_variant = 'variant_B'  # A variant doesn't matter to check the F&F logic
        expected_attention_getter_name = BooksyPayAttentionGetterName.CASHBACK_VB
        mock_webapps_has_finished_booksy_pay_transaction.return_value = False
        mock_is_booksy_pay_payment_window_open.return_value = True
        mock_is_booksy_pay_available.return_value = True
        mock_drf_api_has_finished_booksy_pay_transaction.return_value = False

        # Test
        with override_eppo_feature_flag({BooksyPayCashbackExperiment.flag_name: eppo_variant}):
            response = self._call(AttentionGetterStep.BEFORE_PAYMENT, appointment=appointment)

        response_json = response.json()

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response_json['attention_getter']['name'], expected_attention_getter_name)


@patch(
    'drf_api.service.attention_getters.serializers.booksy_pay.CASHBACK_VARIANTS',
    new=CASHBACK_VARIANTS,
)
@patch('webapps.booksy_pay.cashback.CASHBACK_VARIANTS', new=CASHBACK_VARIANTS)
@override_eppo_feature_flag({'Feature_BooksyPayCashbackPromo': True})
@pytest.mark.django_db
class BooksyPayAttentionGetterViewIntegrationTestCase(AuthenticatedCustomerAPITestCase):
    """
    Note:
        The Booksy Pay view is Tornado-based, while the Attention Getters endpoint utilizes DRF -
        hence the need of `BaseAsyncHTTPTest` & `APITestCase`.
    """

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        # Set up content
        BooksyPayAttentionGetterContentFactory._registry = {}  # pylint: disable=protected-access
        BooksyPayAttentionGettersContentBookingConfirmation.register(
            cashback_variants=CASHBACK_VARIANTS
        )
        BooksyPayAttentionGettersContentBeforePayment.register(cashback_variants=CASHBACK_VARIANTS)
        BooksyPayAttentionGettersContentAfterPayment.register(cashback_variants=CASHBACK_VARIANTS)

        # Enable Booksy Pay for the business
        cls.business = baker.make(Business)
        cls.pos = baker.make(
            'pos.POS',
            business=cls.business,
            pos_refactor_stage2_enabled=True,
            booksy_pay_enabled=True,
        )
        pos_plan = posplan_recipe.make(plan_type=POSPlanPaymentTypeEnum.BOOKSY_PAY)
        cls.pos.pos_plans.add(pos_plan)
        cls.payment_type = baker.make(
            'pos.PaymentType', pos=cls.pos, code=PaymentTypeEnum.BOOKSY_PAY, default=True
        )

        # Enable Stripe for the business and user
        business_wallet, _ = PaymentGatewayPort.get_or_create_business_wallet(
            business_id=cls.business.id, statement_name='statement name,'
        )
        PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=cls.user.id,
            statement_name='statement name,',
            email='<EMAIL>',
            phone='+**********',
        )
        baker.make(
            StripeAccountHolderSettings,
            account_holder_id=business_wallet.account_holder_id,
            pba_fees_accepted=False,
        )
        baker.make(
            StripeAccount,
            pos=cls.pos,
            external_id='test_external_id',
            status=StripeAccountStatus.VERIFIED,
            kyc_verified_at_least_once=True,
        )
        baker.make(StripeCustomer, user=cls.user)
        cls.card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=cls.user,
        )

        # Set up BCI
        cls.bci = baker.make(BusinessCustomerInfo, user=cls.user, business=cls.business)

    def _retrieve_attention_getter(self, appointment: Appointment, step: AttentionGetterStep):
        path = reverse('attention_getter_booksy_pay')
        query_params = {'appointment_id': appointment.id, 'step': str(step)}
        url = f'{path}?{urlencode(query_params)}'

        return self.client.get(url, **self.headers)

    def _pay_with_booksy_pay(self, appointment: Appointment):
        txn = baker.make(
            Transaction,
            appointment_id=appointment.id,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            total=appointment.total_value,
            customer=self.user,
        )
        TransactionSerializer.update_transaction_with_booking(appointment)
        receipt = baker.make(
            Receipt,
            payment_type=self.payment_type,
            status_code=receipt_status.BOOKSY_PAY_SUCCESS,
            transaction=txn,
            already_paid=0,
        )
        txn.latest_receipt = receipt
        txn.created = appointment.created  # Important in terms of promo expiration time
        txn.save()
        baker.make(
            PaymentRow,
            amount=10,
            receipt=receipt,
            payment_type=self.payment_type,
            status=receipt_status.BOOKSY_PAY_SUCCESS,
        )

    def _generate_appointment(self, price: Decimal = Decimal('100')) -> Appointment:
        """
        Generate appointments paid with Booksy Pay.
        It's needed to simulate `has_received_cashback_already` scenarios.
        """
        service_variant = service_variant_recipe.make(
            service=service_recipe.make(business=self.business, tax_rate=None),
            price=price,
            type=PriceType.FIXED,
        )
        appointment = create_appointment(
            [{'service_variant': service_variant}],
            total_type=PriceType.FIXED,
            total_value=price,
            status=Appointment.STATUS.ACCEPTED,
            business=self.business,
            booked_for=self.bci,
        )

        return appointment

    def _generate_finished_appointment_paid_with_booksy_pay(
        self, created: datetime | None = None, price: Decimal | None = None
    ) -> Appointment:
        """
        Generates an appointment paid with Booksy.
        """
        price = price or Decimal('100.00')
        appointment = self._generate_appointment(price)

        if created is not None:
            appointment.created = created
            appointment.save()

        self._pay_with_booksy_pay(appointment)

        appointment.status = Appointment.STATUS.FINISHED
        appointment.save()

        return appointment

    def _test_attention_getters(
        self,
        eppo_variant: str,
        attention_getter_booking_confirmation: BooksyPayAttentionGetterName,
        attention_getter_before_payment: BooksyPayAttentionGetterName,
        attention_getter_after_payment: BooksyPayAttentionGetterName | None,
        appointment: Appointment,
    ) -> None:
        # ################### BOOKING CONFIRMATION ###################
        # Note - the `booking_confirmation` attention getters will be used
        # in prod after the cashback experiment completes. Therefore, they
        # all use `BooksyPayAttentionGetterName.CASHBACK_INITIAL` for now,
        # just as an example promo to enable further development. This will
        # be changed soon.
        with (
            override_eppo_feature_flag({BooksyPayCashbackExperiment.flag_name: eppo_variant}),
            override_eppo_feature_flag({BooksyPayCashbackPromoFlag.flag_name: True}),
            patch('webapps.booksy_pay.cashback.FIRST_CASHBACK_PROMO_END_DATE', new=None),
        ):
            # The default/initial cashback is based on `FIRST_CASHBACK_PROMO_END_DATE`, and
            # the experiment one on `SECOND_CASHBACK_PROMO_START_DATE`. So when a test txn is
            # created, it would need to overlap these two ranges, while they are mutually exclusive
            # (i.e. cashback promo started, after the initial promo has completed). Hence, it's
            # impossible to test it correctly without mocking the `FIRST_CASHBACK_PROMO_END_DATE`.
            # Nonetheless, it's just for the duration of the `booking_confirmation` development,
            # where the `CASHBACK_INITIAL` is used just as an example (see the comment above).
            response_booking_confirmation = self._retrieve_attention_getter(
                appointment, AttentionGetterStep.BOOKING_CONFIRMATION
            )
        self.assertEqual(response_booking_confirmation.status_code, status.HTTP_200_OK)
        self.assertDictEqual(
            response_booking_confirmation.json()['attention_getter'],
            {
                'name': attention_getter_booking_confirmation,
                'step': 'booking_confirmation',
                'type': 'booksy_pay',
            },
        )

        # ################### BEFORE PAYMENT ###################
        with override_eppo_feature_flag({BooksyPayCashbackExperiment.flag_name: eppo_variant}):
            response_before_payment = self._retrieve_attention_getter(
                appointment, AttentionGetterStep.BEFORE_PAYMENT
            )
        self.assertEqual(response_before_payment.status_code, status.HTTP_200_OK)
        self.assertDictEqual(
            response_before_payment.json()['attention_getter'],
            {
                'name': attention_getter_before_payment,
                'step': 'before_payment',
                'type': 'booksy_pay',
            },
        )

        # ################### PAYMENT ###################
        self._pay_with_booksy_pay(appointment)
        self.assertEqual(
            appointment.transactions.last().latest_receipt.status_code,
            receipt_status.BOOKSY_PAY_SUCCESS,
        )

        # ################### AFTER PAYMENT ###################
        with override_eppo_feature_flag({BooksyPayCashbackExperiment.flag_name: eppo_variant}):
            response_after_payment = self._retrieve_attention_getter(
                appointment, AttentionGetterStep.AFTER_PAYMENT
            )
        if attention_getter_after_payment:
            self.assertEqual(response_after_payment.status_code, status.HTTP_200_OK)
            self.assertDictEqual(
                response_after_payment.json()['attention_getter'],
                {
                    'name': attention_getter_after_payment,
                    'step': 'after_payment',
                    'type': 'booksy_pay',
                },
            )
        else:
            self.assertEqual(response_after_payment.status_code, status.HTTP_204_NO_CONTENT)

    @parameterized.expand(
        [
            (
                # Scenario: Experiment disabled - initial/default cashback expected
                '',
                0,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY,
            ),
            (
                # Scenario: Experiment disabled and Cx has received cashback already
                '',
                1,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a control group - first txn
                'control',
                0,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a control group - another txn
                'control',
                1,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx has not received cashback yet
                'variant_A',
                0,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.CASHBACK_VA,
                BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY,
            ),
            (
                # Scenario: Cx has received cashback already
                'variant_A',
                1,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx has not received cashback yet
                'variant_B',
                0,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.CASHBACK_VB,
                BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY,
            ),
            (
                # Scenario: Cx has received cashback already
                'variant_B',
                1,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx has not received cashback yet
                'variant_C',
                0,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.CASHBACK_VC_APPT_1,
                BooksyPayAttentionGetterName.CASHBACK_VC_AFTER_FIRST_PAYMENT,
            ),
            (
                # Scenario: Cx has not received cashback yet, but has a first eligible txn
                'variant_C',
                1,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.CASHBACK_VC_APPT_2,
                BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY,
            ),
            (
                # Scenario: Cx has received cashback already
                'variant_C',
                2,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
        ]
    )
    @override_feature_flag({BooksyPayFlag.flag_name: True})
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch(
        'drf_api.service.booksy_pay.serializers.is_booksy_pay_payment_window_open',
        return_value=True,
    )
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_available',
        new_callable=PropertyMock,
    )
    def test_cashback_eligible_payment(
        self,
        eppo_variant: str,
        num_of_existing_bp_txns: int,
        attention_getter_booking_confirmation: BooksyPayAttentionGetterName,
        attention_getter_before_payment: BooksyPayAttentionGetterName,
        attention_getter_after_payment: BooksyPayAttentionGetterName,
        mock_is_booksy_pay_available: MagicMock,
        is_time_window_open_mock: MagicMock,
        create_payment_intent_mock: MagicMock,
    ):
        """
        Checks scenarios when a cashback-eligible transaction is generated during payment.
        """
        # Pre-conditions and setup
        mock_is_booksy_pay_available.return_value = True
        is_time_window_open_mock.return_value = True
        create_payment_intent_mock.return_value = MagicMock(id=str(uuid4().hex))
        price = Decimal('100.00')  # It shall meet `min_booking_amount` condition
        # The appointment creation datetime matters in terms of checking if a user received cashback
        # already, which strictly relates to the cashback promo version (initial vs experiment).
        # No cashback variant in Eppo indicates this is the initial cashback - hence
        # the first promo end date.
        appointment_created_datetime = (
            FIRST_CASHBACK_PROMO_END_DATE if not eppo_variant else SECOND_CASHBACK_PROMO_START_DATE
        )
        appointment = self._generate_appointment(price)
        appointment.created = appointment_created_datetime
        appointment.save()

        for _ in range(num_of_existing_bp_txns):
            # Generates a transaction that meets the cashback eligibility criteria
            self._generate_finished_appointment_paid_with_booksy_pay(
                created=appointment_created_datetime,
                price=price,
            )
            create_payment_intent_mock.return_value = MagicMock(id=str(uuid4().hex))

        self.assertEqual(
            Transaction.objects.filter(
                latest_receipt__status_code=receipt_status.BOOKSY_PAY_SUCCESS
            ).count(),
            num_of_existing_bp_txns,
        )

        self._test_attention_getters(
            eppo_variant,
            attention_getter_booking_confirmation,
            attention_getter_before_payment,
            attention_getter_after_payment,
            appointment,
        )

    @parameterized.expand(
        [
            (
                # Scenario: Experiment disabled - initial/default cashback expected
                '',
                0,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                None,  # as not eligible before payment
            ),
            (
                # Scenario: Experiment disabled, and Cx has a first NOT eligible txn
                '',
                1,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                None,  # as not eligible before payment
            ),
            (
                # Scenario: Cx assigned to a control group - first txn
                'control',
                0,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a control group - another txn
                'control',
                1,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx has not received cashback yet, first NOT eligible txn
                'variant_A',
                0,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.CASHBACK_VA,
                None,  # as not eligible before payment
            ),
            (
                # Scenario: Cx has not received cashback yet, and has a first NOT eligible txn
                'variant_A',
                1,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.CASHBACK_VA,
                None,  # as not eligible before payment
            ),
            (
                # Scenario: Cx has not received cashback yet (Cx always eligible in variant B)
                'variant_B',
                0,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.CASHBACK_VB,
                BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY,
            ),
            (
                # Scenario: Cx has received cashback already
                'variant_B',
                1,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx has not received cashback yet, first NOT eligible txn
                'variant_C',
                0,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.CASHBACK_VC_APPT_1,
                None,  # as not eligible before payment
            ),
            (
                # Scenario: Cx has not received cashback yet, but has a first NOT eligible txn
                'variant_C',
                1,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.CASHBACK_VC_APPT_1,
                None,  # as not eligible before payment
            ),
            (
                # Scenario: Cx has not received cashback yet, but has a two NOT eligible txn
                'variant_C',
                2,
                BooksyPayAttentionGetterName.CASHBACK_INITIAL,
                BooksyPayAttentionGetterName.CASHBACK_VC_APPT_1,
                None,  # as not eligible before payment
            ),
        ]
    )
    @override_feature_flag({BooksyPayFlag.flag_name: True})
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch(
        'drf_api.service.booksy_pay.serializers.is_booksy_pay_payment_window_open',
        return_value=True,
    )
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_available',
        new_callable=PropertyMock,
    )
    def test_not_cashback_eligible_payment(
        self,
        eppo_variant: str,
        num_of_existing_bp_txns: int,
        attention_getter_booking_confirmation: BooksyPayAttentionGetterName,
        attention_getter_before_payment: BooksyPayAttentionGetterName,
        attention_getter_after_payment: BooksyPayAttentionGetterName,
        mock_is_booksy_pay_available: MagicMock,
        is_time_window_open_mock: MagicMock,
        create_payment_intent_mock: MagicMock,
    ):
        """
        Checks scenarios when a NON-cashback-eligible transaction is generated during payment.

        Tests:
        * scenarios where a Cx has not received cashback yet and attempts to pay for
          an appointment that doesn't meet cashback requirements (i.e. min amount).
        * scenarios where a Cx has Booksy Pay transactions already, but they don't meet
          cashback eligibility criteria.
        In both cases, a before_payment/booking_confirmation cashback screen is expected,
        and an after payment screen is not expected (unless the appointment that a Cx
        is willing to pay for is eligible).

        A Cx should be shown a cashback screen even though (s)he's not cashback-eligible.
        """
        # Pre-conditions and setup
        mock_is_booksy_pay_available.return_value = True
        is_time_window_open_mock.return_value = True
        create_payment_intent_mock.return_value = MagicMock(id=str(uuid4().hex))
        price = Decimal(10)  # It's lower than `min_booking_amount` (variants: default, A, C)
        # It's needed as different promos have different duration (start & end dates)
        appointment_created_datetime = (
            FIRST_CASHBACK_PROMO_END_DATE if not eppo_variant else SECOND_CASHBACK_PROMO_START_DATE
        )
        appointment = self._generate_appointment(price)
        appointment.created = appointment_created_datetime
        appointment.save()

        for _ in range(num_of_existing_bp_txns):
            # Generates transaction that DO NOT meet cashback eligibility criteria
            self._generate_finished_appointment_paid_with_booksy_pay(
                created=appointment_created_datetime,
                price=price,
            )
            create_payment_intent_mock.return_value = MagicMock(id=str(uuid4().hex))

        self.assertEqual(
            Transaction.objects.filter(
                latest_receipt__status_code=receipt_status.BOOKSY_PAY_SUCCESS
            ).count(),
            num_of_existing_bp_txns,
        )

        self._test_attention_getters(
            eppo_variant,
            attention_getter_booking_confirmation,
            attention_getter_before_payment,
            attention_getter_after_payment,
            appointment,
        )

    @parameterized.expand(
        [
            (
                # Scenario: Cx assigned to a control group - first txn
                'control',
                0,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a control group - another txn
                'control',
                1,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a variant A - first txn
                'variant_A',
                0,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a variant A - another txn
                'variant_A',
                1,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a variant B - first txn
                'variant_B',
                0,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a variant B - another txn
                'variant_B',
                1,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a variant C - first txn
                'variant_C',
                0,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a variant C - second txn
                'variant_C',
                1,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a variant C - third txn
                'variant_C',
                2,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
        ]
    )
    @override_feature_flag({BooksyPayFlag.flag_name: True})
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch(
        'drf_api.service.booksy_pay.serializers.is_booksy_pay_payment_window_open',
        return_value=True,
    )
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_available',
        new_callable=PropertyMock,
    )
    def test_has_received_first_cashback_promo(
        self,
        eppo_variant: str,
        num_of_existing_bp_txns: int,
        attention_getter_booking_confirmation: BooksyPayAttentionGetterName,
        attention_getter_before_payment: BooksyPayAttentionGetterName,
        attention_getter_after_payment: BooksyPayAttentionGetterName,
        mock_is_booksy_pay_available: MagicMock,
        is_time_window_open_mock: MagicMock,
        create_payment_intent_mock: MagicMock,
    ):
        """
        Checks scenarios when a Cx has received the first promo cashback and
        is assigned to new variant by Eppo during the cashback experiment.

        Important: only the second promo cases are applicable in this scenario.
        """
        # Pre-conditions and setup
        mock_is_booksy_pay_available.return_value = True
        is_time_window_open_mock.return_value = True
        create_payment_intent_mock.return_value = MagicMock(id=str(uuid4().hex))
        price = Decimal('100.00')  # It shall meet `min_booking_amount` condition
        # In this case it should be an appointment eligible for the second promo - hence the date
        appointment = self._generate_appointment(price)
        appointment.created = SECOND_CASHBACK_PROMO_START_DATE
        appointment.save()

        # Simulate that the first cashback was paid to a Cx
        self._generate_finished_appointment_paid_with_booksy_pay(
            created=FIRST_CASHBACK_PROMO_END_DATE,
            price=price,
        )

        for _ in range(num_of_existing_bp_txns):
            # Generates a transaction that meets the cashback eligibility criteria
            self._generate_finished_appointment_paid_with_booksy_pay(
                created=SECOND_CASHBACK_PROMO_START_DATE, price=price
            )
            create_payment_intent_mock.return_value = MagicMock(id=str(uuid4().hex))

        self.assertEqual(
            Transaction.objects.filter(
                latest_receipt__status_code=receipt_status.BOOKSY_PAY_SUCCESS
            ).count(),
            num_of_existing_bp_txns + 1,  # `+ 1` is the first cashback promo txn
        )

        self._test_attention_getters(
            eppo_variant,
            attention_getter_booking_confirmation,
            attention_getter_before_payment,
            attention_getter_after_payment,
            appointment,
        )

    @parameterized.expand(
        [
            (
                # Scenario: Cx assigned to a control group - first txn
                'control',
                0,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a control group - another txn
                'control',
                1,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a variant A - first txn
                'variant_A',
                0,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.CASHBACK_VA,
                BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY,
            ),
            (
                # Scenario: Cx assigned to a variant A - another txn
                'variant_A',
                1,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a variant B - first txn
                'variant_B',
                0,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.CASHBACK_VB,
                BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY,
            ),
            (
                # Scenario: Cx assigned to a variant B - another txn
                'variant_B',
                1,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
            (
                # Scenario: Cx assigned to a variant C - first txn
                'variant_C',
                0,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.CASHBACK_VC_APPT_1,
                BooksyPayAttentionGetterName.CASHBACK_VC_AFTER_FIRST_PAYMENT,
            ),
            (
                # Scenario: Cx assigned to a variant C - second txn
                'variant_C',
                1,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.CASHBACK_VC_APPT_2,
                BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY,
            ),
            (
                # Scenario: Cx assigned to a variant C - third txn
                'variant_C',
                2,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                BooksyPayAttentionGetterName.INTRO_BP_OPEN,
                None,
            ),
        ]
    )
    @override_feature_flag({BooksyPayFlag.flag_name: True})
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent')
    @patch(
        'drf_api.service.booksy_pay.serializers.is_booksy_pay_payment_window_open',
        return_value=True,
    )
    @patch(
        'webapps.booking.models.Appointment.is_booksy_pay_available',
        new_callable=PropertyMock,
    )
    def test_has_txn_between_first_and_second_cashback_promo(
        self,
        eppo_variant: str,
        num_of_existing_bp_txns: int,
        attention_getter_booking_confirmation: BooksyPayAttentionGetterName,
        attention_getter_before_payment: BooksyPayAttentionGetterName,
        attention_getter_after_payment: BooksyPayAttentionGetterName,
        mock_is_booksy_pay_available: MagicMock,
        is_time_window_open_mock: MagicMock,
        create_payment_intent_mock: MagicMock,
    ):
        """
        Checks scenarios when a Cx paid for an appointment with Booksy Pay in the time window
        when no cashback promo was active. Such users shall be shown a promo (an exp variant).

        Note:
            The expected attention getter for the `booking_confirmation` step is always set
            to `INTRO_BP_OPEN` here as the `FIRST_CASHBACK_PROMO_END_DATE` is mocked to `None` (for
            the context, see the comment in the patching line). Long story short - it's impossible
            to generate a transaction that would fit into two promos as these have different
            duration, and therefore, in this particular case it's a matter of choosing to get:
            * either `CASHBACK_INITIAL`, if the end date isn't mocked, as none of the transactions
              generated during the test case would match the conditions of the initial promo;
            * or `INTRO_BP_OPEN`, if the end date is mocked to None, as all transactions would match
              the conditions of the initial promo.
            In the end, it doesn't matter much in the context of this test case, as it's supposed to
            be checking just the second promo. Also, the initial cashback is used just as an example
            promo for the `booking_confirmation` step as of today (to be updated based on the
            results of the cashback experiment).
        """
        # Pre-conditions and setup
        mock_is_booksy_pay_available.return_value = True
        is_time_window_open_mock.return_value = True
        create_payment_intent_mock.return_value = MagicMock(id=str(uuid4().hex))
        price = Decimal('100.00')  # It shall meet `min_booking_amount` condition
        # In this case it should be an appointment eligible for the second promo - hence the date
        appointment = self._generate_appointment(price)
        appointment.created = SECOND_CASHBACK_PROMO_START_DATE
        appointment.save()

        # Simulate that a Cx paid with Booksy Pay between the first and second promo
        self._generate_finished_appointment_paid_with_booksy_pay(
            created=FIRST_CASHBACK_PROMO_END_DATE + timedelta(days=1),
            price=price,
        )

        for _ in range(num_of_existing_bp_txns):
            # Generates a transaction that meets the cashback eligibility criteria
            self._generate_finished_appointment_paid_with_booksy_pay(
                created=SECOND_CASHBACK_PROMO_START_DATE, price=price
            )
            create_payment_intent_mock.return_value = MagicMock(id=str(uuid4().hex))

        self.assertEqual(
            Transaction.objects.filter(
                latest_receipt__status_code=receipt_status.BOOKSY_PAY_SUCCESS
            ).count(),
            num_of_existing_bp_txns + 1,  # `+ 1` is the txn created between promos
        )

        self._test_attention_getters(
            eppo_variant,
            attention_getter_booking_confirmation,
            attention_getter_before_payment,
            attention_getter_after_payment,
            appointment,
        )
