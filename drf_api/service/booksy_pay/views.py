from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.db.transaction import atomic
from django.utils.translation import gettext as _
from rest_framework import status
from rest_framework.mixins import RetrieveModelMixin, UpdateModelMixin
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.exceptions import PermissionDenied, NotFound

from drf_api.base_views import BaseBooksySessionGenericViewSet
from drf_api.mixins import MultiSerializerViewSetMixin
from drf_api.mixins import BusinessViewValidatorMixin
from drf_api.service.booksy_pay.serializers import (
    BooksyPayLateCancellationSerializer,
    BooksyPaySerializer,
    BooksyPayResponseSerializer,
)
from drf_api.service.booksy_pay.utils import get_appointment
from lib.db import READ_ONLY_DB, using_db_for_reads
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import CustomUserAttributes
from lib.feature_flag.feature.booksy_pay import (
    BooksyPayAvailabilityV2Flag,
    BooksyPayFlag,
)
from lib.locks import BooksyPayPaymentLock
from lib.x_version_compatibility import BooksyPayCompatibility
from service.booking.partner_payment import PartnerPaymentStatus
from service.booking.save_appointment_data import SaveAppointmentData
from service.booking.tools import (
    AppointmentData,
)
from service.mixins.throttling import get_django_user_ip
from service.mixins.validation import validate_serializer
from webapps.adyen.exceptions import ThreeDSecureAuthenticationRequired
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import AppointmentTypeSM as AT
from webapps.booking.models import Appointment
from webapps.booking.serializers.appointment import (
    CustomerAppointmentSerializer,
)
from webapps.booksy_pay.availability import get_booksy_pay_availability_info
from webapps.business.context import business_context
from webapps.business.enums import StaffAccessLevels
from webapps.family_and_friends.factory import (
    create_member_transaction_for_parent_for_family_and_friends_appointment,
)
from webapps.pos.exceptions import BlikActionRequired
from webapps.pos.utils import get_booksy_pay_payment_info


class BaseBooksyPayViewSet(
    MultiSerializerViewSetMixin,
    BaseBooksySessionGenericViewSet,
):
    # pylint: disable=duplicate-code, too-many-ancestors
    keep_trace = True
    keep_trace_status_codes = {'post': [500]}
    trace_percentage = 100  # Feature in tests - small traffic, hence 100%

    def get_object(self, prefetch_all=True) -> AppointmentWrapper:
        appointment_id = self.kwargs['appointment_id']
        appointment_wrapper = get_appointment(
            customer_user_id=self.user.id,
            appointment_id=appointment_id,
            appointment_type=AT.SINGLE,
            prefetch_all=prefetch_all,
        )
        if appointment_wrapper is None:
            raise NotFound(detail=f'Appointment not found {appointment_id}')

        return appointment_wrapper

    def do_payment(self, request, appointment_wrapper: AppointmentWrapper, dry_run: bool = False):
        if not BooksyPayFlag(
            UserData(custom={CustomUserAttributes.BUSINESS_ID: appointment_wrapper.business_id})
        ):
            raise PermissionDenied(
                detail=_('Disabled view by feature flag'),
            )

        if BooksyPayCompatibility(request) is False:
            raise PermissionDenied(
                detail='Wrong application version',
            )

        with business_context(appointment_wrapper.business), appointment_wrapper.booksy_pay_flow():
            save_data = self._process_booksy_pay(
                appointment=appointment_wrapper,
                dry_run=dry_run,
            )

        ret = {
            'appointment': save_data.appointment_data,
            'appointment_payment': save_data.payment_data,
            PartnerPaymentStatus.APPOINTMENT_RESPONSE_KEY: save_data.partner_payment_data,
            'three_d_data': save_data.payment_3d_error,
            'meta': {},
        }

        if save_data.blik_data:
            ret['blik_data'] = save_data.blik_data

        return ret

    def _process_booksy_pay(
        self, appointment: AppointmentWrapper, dry_run: bool = False
    ) -> SaveAppointmentData:
        business = appointment.business
        data = dict(self.request.data)
        data['dry_run'] = dry_run
        three_ds_action_required, blik_action_required = None, None
        appointment_serializer = CustomerAppointmentSerializer(
            instance=appointment,
            context={
                'business': business,
                'user': self.user,
                'single_category': business.is_single_category,
                'pos': business.pos,
                'type': Appointment.TYPE.CUSTOMER,
                'source': self.booking_source,
                'no_thumbs': data.pop('no_thumbs', False),
                'device_fingerprint': self.fingerprint,
                'cell_phone': self.user.cell_phone if self.user else '',
            },
        )
        payment_serializer = BooksyPaySerializer(
            data=data,
            context={
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'user': self.user,
                'business': business,
                'pos': business.pos,
                'appointment_data': AppointmentData.build(appointment),
                'appointment_checkout': appointment.checkout,
                'already_prepaid': appointment.is_prepaid,
                'is_booksy_pay_available': appointment.is_booksy_pay_available,
                'extra_data': {
                    'cardholder_ip': str(get_django_user_ip(self.request)),
                    'browser_language': self.language,
                    'user': self.user,
                },
                'compatibilities': {},
                'device_fingerprint': self.fingerprint,
                'cell_phone': self.user.cell_phone if self.user else '',
                'user_agent': self.user_agent,
                'request': self.request,  # TODO: consider avoiding passing the request
            },
        )
        validate_serializer(payment_serializer)

        if not dry_run:
            with atomic():
                try:
                    payment_serializer.save()
                except ThreeDSecureAuthenticationRequired as e:
                    three_ds_action_required = e
                except BlikActionRequired as e:
                    blik_action_required = e

                create_member_transaction_for_parent_for_family_and_friends_appointment(appointment)

        # We need to make sure a response contains the newest payment data
        appointment.clear_cache_payment_and_deposit()
        save_data = SaveAppointmentData(
            appointment=appointment,
            appointment_data=appointment_serializer.data,
            payment_data=payment_serializer.data,
        )
        if three_ds_action_required:
            save_data.payment_3d_error = three_ds_action_required.three_d_data
        if blik_action_required:
            save_data.blik_data = {
                'balance_transaction_id': str(blik_action_required.balance_transaction_id),
            }

        return save_data


class BooksyPayViewSet(BaseBooksyPayViewSet):
    # pylint: disable=too-many-ancestors
    permission_classes = (IsAuthenticated,)
    booksy_teams = (BooksyTeams.PAYMENT_NEXUS,)

    serializer_action_classes = {
        'retrieve': BooksyPayResponseSerializer,
        'pay': BooksyPaySerializer,
    }
    # TODO: consider adding response_serializer_action_classes

    def retrieve(self, request, *args, **kwargs):
        appointment_wrapper = self.get_object(prefetch_all=False)
        appointment_uid = appointment_wrapper.appointment_uid
        if BooksyPayAvailabilityV2Flag(UserData(subject_key=appointment_wrapper.business_id)):
            availability_info = get_booksy_pay_availability_info(appointment_uid)
            bp_availability = {
                'available': availability_info.available,
                'payment_window_open': availability_info.payment_window_open,
            }
        else:
            bp_availability = {
                'available': appointment_wrapper.is_booksy_pay_available,
                'payment_window_open': appointment_wrapper.is_booksy_pay_payment_window_open,
            }

        serializer_class = self.get_response_serializer_class()
        data = serializer_class(
            instance={
                'appointment_id': appointment_wrapper.appointment_id,
                'appointment_uid': appointment_uid,
                'availability_info': bp_availability,
                'payment_info': get_booksy_pay_payment_info(appointment_wrapper.appointment),
            }
        ).data
        return Response(data=data, status=status.HTTP_200_OK)

    def pay(self, request, appointment_id, *args, **kwargs):
        if not BooksyPayPaymentLock.try_to_lock(appointment_id):
            return Response(
                data={
                    'errors': [
                        {
                            'code': 'lock_error',
                            'description': _(
                                'Booksy Pay payment request is already being processed. '
                                'Please wait until it is finished or try again later.'
                            ),
                        },
                    ]
                },
                status=status.HTTP_409_CONFLICT,
            )
        appointment_wrapper = self.get_object()
        data = self.do_payment(request, appointment_wrapper)
        return Response(data=data, status=status.HTTP_201_CREATED)


class BooksyPayDryRunViewSet(BaseBooksyPayViewSet):
    # pylint: disable=too-many-ancestors
    permission_classes = (IsAuthenticated,)
    booksy_teams = (BooksyTeams.PAYMENT_NEXUS,)

    serializer_action_classes = {
        'dry_run': BooksyPaySerializer,
    }

    @using_db_for_reads(READ_ONLY_DB)
    def dry_run(self, request, *args, **kwargs):
        appointment_wrapper = self.get_object()
        data = self.do_payment(request, appointment_wrapper, dry_run=True)
        return Response(data=data, status=status.HTTP_200_OK)


# pylint: disable=R0901
class BooksyPayLateCancellationWindowViewSet(
    UpdateModelMixin,
    RetrieveModelMixin,
    BusinessViewValidatorMixin,
    BaseBooksySessionGenericViewSet,
):
    permission_classes = (IsAuthenticated,)
    booksy_teams = (BooksyTeams.PAYMENT_NEXUS,)
    required_minimum_access_level = StaffAccessLevels.OWNER
    serializer_class = BooksyPayLateCancellationSerializer

    def get_object(self):
        business = self.get_business()
        return business.pos
