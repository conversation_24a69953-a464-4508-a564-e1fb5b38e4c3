"""Flags related to bug fixes."""

from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag


class AddChannelsParametersToDeeplink(BooleanFlag):
    flag_name = 'Bug_AddChannelsParametersToDeeplink'
    adapter = FeatureFlagAdapter.EPPO


class DAC7PeselValidationFixFlag(BooleanFlag):
    flag_name = 'Fix_DAC7PeselValidationFlag'
    adapter = FeatureFlagAdapter.EPPO


class DoNotRemoveEmptyTaxGroupInInvoiceDetailsForm(BooleanFlag):
    flag_name = 'Bug_DoNotRemoveEmptyTaxGroupInInvoiceDetailsForm'
    adapter = FeatureFlagAdapter.EPPO


class FilterAppointmentsAndSubbookingsByTime(BooleanFlag):
    flag_name = 'Bug_FilterAppointmentsAndSubbookingsByTime'
    adapter = FeatureFlagAdapter.EPPO


class FixSlowAppointmentAdminQuery(BooleanFlag):
    flag_name = 'Bug_FixSlowAppointmentAdminQuery'
    adapter = FeatureFlagAdapter.EPPO


class FixTotalPriceInAppointmentListReport(BooleanFlag):
    flag_name = 'Bug_FixTotalPriceInAppointmentListReport'
    adapter = FeatureFlagAdapter.EPPO


class FrenchCertificationYearPeriodsRefactor(BooleanFlag):
    flag_name = 'Bug_FrenchCertificationYearPeriodsRefactor'
    adapter = FeatureFlagAdapter.EPPO


class InvitesValidationNotificationScheduleCount(BooleanFlag):
    flag_name = 'Bug_InvitesValidationNotificationScheduleCount'
    adapter = FeatureFlagAdapter.LD


class InvokeBCIPostSaveInUpdateUserBCITask(BooleanFlag):
    flag_name = 'Bug_InvokeBCIPostSaveInUpdateUserBCITask'
    adapter = FeatureFlagAdapter.EPPO


class MissingStyleseatAppHeaderFlag(BooleanFlag):
    flag_name = 'Bug_MissingStyleseatAppHeaderFlag'
    adapter = FeatureFlagAdapter.LD


class OverloadFakeBookAgainNoValidSlotFlag(BooleanFlag):
    flag_name = 'Bug_OverloadFakeBookAgainNoValidSlotFlag'
    adapter = FeatureFlagAdapter.EPPO


class PartnerOnlineBookingPreventMaskingCustomerNameFlag(BooleanFlag):
    flag_name = 'Bug_PartnerOnlineBookingPreventMaskingCustomerName'
    adapter = FeatureFlagAdapter.LD


class ReindexResourcesOnBatchResourceUpdateToolFlag(BooleanFlag):
    flag_name = 'Bug_ReindexResourcesOnBatchUpdateToolFlag'
    adapter = FeatureFlagAdapter.LD


class RemoveApplianceDataInCustomerAPIFlag(BooleanFlag):
    flag_name = 'Bug_RemoveApplianceDataInCustomerAPI'
    adapter = FeatureFlagAdapter.LD


class UseTodayDateInBusinessTZForSimpleStatsChart(BooleanFlag):
    flag_name = 'Bug_UseTodayDateInBusinessTZForSimpleStatsChart'
    adapter = FeatureFlagAdapter.EPPO
