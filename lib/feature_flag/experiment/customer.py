from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import StringFlag


class SelectedForYouPortfolioImagesExperiment(StringFlag):
    flag_name = 'Experiment_SelectedForYouPortfolioImages'
    adapter = FeatureFlagAdapter.EPPO


class VistedLikedSelected4UExperiment(StringFlag):
    flag_name = 'Experiment_VistedLikedSelected4You'
    adapter = FeatureFlagAdapter.EPPO


class S4UGalleriesOrderExperiment(StringFlag):
    flag_name = 'Experiment_SelectedForYouGalleriesOrder'
    adapter = FeatureFlagAdapter.EPPO


class UseSearchServiceForBusinessCategoryHintsExperiment(StringFlag):
    flag_name = 'Experiment_UseSearchServiceForBusinessCategoryHints'
    adapter = FeatureFlagAdapter.EPPO


class VisitedLikedS4URandomizationExperiment(StringFlag):
    flag_name = 'Experiment_VisitedLikedS4URandomization'
    adapter = FeatureFlagAdapter.EPPO
