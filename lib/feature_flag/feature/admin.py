from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag


class ShowInvoiceAddressInBuyerAdminFlag(BooleanFlag):
    flag_name = 'Feature_ShowInvoiceAddressInBuyerAdmin'
    adapter = FeatureFlagAdapter.EPPO


class UseSetAdminPermissionsFromWorkspace(BooleanFlag):
    flag_name = 'Feature_UseSetAdminPermissionsFromWorkspace'
    adapter = FeatureFlagAdapter.EPPO


class ShowPaidDuplicatedAccountCancellationReason(BooleanFlag):
    flag_name = 'Feature_ShowPaidDuplicatedAccountCancellationReason'
    adapter = FeatureFlagAdapter.EPPO


class ShowEnforcePasswordResetFlag(BooleanFlag):
    flag_name = 'Feature_ShowEnforcePasswordReset'
    adapter = FeatureFlagAdapter.EPPO
