from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag


class FifthCustomerBookingInFourteenDaysSegment(BooleanFlag):
    flag_name = 'Feature_FifthCustomerBookingInFourteenDaysSegment'
    adapter = FeatureFlagAdapter.EPPO


class UnrestrictCustomerEventsFlag(BooleanFlag):
    flag_name = 'Feature_UnrestrictCustomerEvents'
    adapter = FeatureFlagAdapter.EPPO


class UnrestrictCustomerEventsForUSFlag(BooleanFlag):
    flag_name = 'Feature_UnrestrictCustomerEventsForUS'
    adapter = FeatureFlagAdapter.EPPO


class FacebookAnalyticsCustomerBookingTrackingFlag(BooleanFlag):
    flag_name = 'Feature_FacebookAnalyticsCustomerBookingTracking'
    adapter = FeatureFlagAdapter.EPPO


class BranchIOCustomerBookingTrackingFlag(BooleanFlag):
    flag_name = 'Feature_BranchIOCustomerBookingTracking'
    adapter = FeatureFlagAdapter.EPPO
