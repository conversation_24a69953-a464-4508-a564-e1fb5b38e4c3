from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag, StringFlag, FloatFlag


class CheckOriginIPInVonageStatusWebhookFlag(BooleanFlag):
    flag_name = 'Feature_CheckOriginIPInVonageStatusWebhook'
    adapter = FeatureFlagAdapter.LD


class LimitEmailInvitationFlag(FloatFlag):
    flag_name = 'Feature_LimitEmailInvitation'
    adapter = FeatureFlagAdapter.EPPO

    def __new__(cls, user: UserData = None, defaults: float = 1000.0) -> float:
        return super().__new__(cls=cls, user=user, defaults=defaults)


class RemoveFRSuffixFromSMSBodyFlag(BooleanFlag):
    flag_name = 'Feature_RemoveFRSuffixFromSMSBodyFlag'
    adapter = FeatureFlagAdapter.LD


class SaveSmsNotificationHistoryViaCeleryFlag(BooleanFlag):
    flag_name = 'Feature_SaveSmsNotificationHistoryViaCelery'
    adapter = FeatureFlagAdapter.LD


class OTPSimpleThrottleRateFlag(StringFlag):
    flag_name = 'Feature_OTPSimpleThrottleRate'
    adapter = FeatureFlagAdapter.EPPO


class OTPPhoneThrottleRateFlag(StringFlag):
    flag_name = 'Feature_OTPPhoneThrottleRate'
    adapter = FeatureFlagAdapter.EPPO


class AdditionalBookingReminderExperiment(StringFlag):
    flag_name = 'Experiment_AdditionalBookingReminder'
    adapter = FeatureFlagAdapter.EPPO


class IncludeBookingIdInAddReviewPushTargetFlag(BooleanFlag):
    flag_name = 'Feature_IncludeBookingIdInAddReviewPushTarget'
    adapter = FeatureFlagAdapter.EPPO


class EnableSendingAdditionalBookingReminderFlag(BooleanFlag):
    flag_name = 'Feature_EnableSendingAdditionalBookingReminder'
    adapter = FeatureFlagAdapter.EPPO


class BCIsActiveMemberFlag(BooleanFlag):
    flag_name = 'Fix_BCIsActiveMemberFlag'
    adapter = FeatureFlagAdapter.EPPO


class LogTelnyxRetryAttemptFlag(BooleanFlag):
    flag_name = 'Feature_LogTelnyxRetryAttempt'
    adapter = FeatureFlagAdapter.EPPO


class UseRequestClientRetryTelnyxFlag(BooleanFlag):
    flag_name = 'Feature_UseRequestClientRetryTelnyxFlag'
    adapter = FeatureFlagAdapter.EPPO


class SMSTelnyxWebhookUrlFlag(BooleanFlag):
    flag_name = 'Feature_SMSTelnyxWebhookUrlFlag'
    adapter = FeatureFlagAdapter.EPPO


class SendTippingAppetiteExperimentPushFlag(BooleanFlag):
    flag_name = 'Feature_SendTippingAppetiteExperimentPush'
    adapter = FeatureFlagAdapter.EPPO


class DontSendCustomerConfirmationSMSForBBsWithUserFlag(BooleanFlag):
    flag_name = 'Feature_DontSendCustomerConfirmationSMSForBBsBCIsWithUser'
    adapter = FeatureFlagAdapter.EPPO


class RemoveOnboardingNotificationsFlag(BooleanFlag):
    flag_name = 'Feature_RemoveOnboardingNotifications'
    adapter = FeatureFlagAdapter.EPPO


class WarnSmsStatusUpdateFailureFlag(BooleanFlag):
    flag_name = 'Feature_WarnSmsStatusUpdateFailure'
    adapter = FeatureFlagAdapter.EPPO


class MoveToSettingsSMSVonageUseRotatingCredentialsFlag(BooleanFlag):
    flag_name = 'Feature_MoveToSettingsSMSVonageUseRotatingCredentials'
    adapter = FeatureFlagAdapter.EPPO


class MoveToSettingsSMSProfilesPerCountryFlag(BooleanFlag):
    flag_name = 'Feature_MoveToSettingsSMSProfilesPerCountryFlag'
    adapter = FeatureFlagAdapter.EPPO


class VonageWebhookWithMessageTypeFlag(BooleanFlag):
    flag_name = 'Feature_VonageWebhookWithMessageTypeFlag'
    adapter = FeatureFlagAdapter.EPPO


class MoveToEppoSMSServiceNameFlag(BooleanFlag):
    flag_name = 'Feature_MoveToEppoSMSServiceName'
    adapter = FeatureFlagAdapter.EPPO


class SMSServiceProviderFlag(StringFlag):
    flag_name = 'Feature_SMSServiceProvider'
    adapter = FeatureFlagAdapter.EPPO


class NotifyOwnerThroughEmailAboutNewBookingFlag(BooleanFlag):
    """
    Send email notification about a new booking to the owner.
    """

    flag_name = 'Feature_NotifyOwnerThroughEmailAboutNewBooking'
    adapter = FeatureFlagAdapter.EPPO
