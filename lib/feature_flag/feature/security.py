from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag, DictFlag, FloatFlag


class BlockCrossBorderInviteSMSFlag(BooleanFlag):
    flag_name = 'Feature_BlockCrossBorderInviteSMS'
    adapter = FeatureFlagAdapter.EPPO


class BlockSendingAppointmentSmsToRecipients(DictFlag):
    flag_name = 'Feature_BlockSendingAppointmentSmsToRecipients'
    adapter = FeatureFlagAdapter.EPPO


class DisableCaptchaForAccountExistEndpointFlag(BooleanFlag):
    flag_name = 'Feature_DisableCaptchaForAccountExistEndpoint'
    adapter = FeatureFlagAdapter.LD


class DisableDisposableEmailBlacklistBusinessFlag(BooleanFlag):
    flag_name = 'Feature_DisableDisposableEmailBlacklistBusiness'
    adapter = FeatureFlagAdapter.EPPO


class ExcludeFromOTPSMSFlag(BooleanFlag):
    flag_name = 'Feature_ExcludeFromOTPSMS'
    adapter = FeatureFlagAdapter.LD


class ExcludeFromPasswordResetSMSFlag(BooleanFlag):
    flag_name = 'Feature_ExcludeFromPasswordResetSMSFlag'
    adapter = FeatureFlagAdapter.EPPO


class HCaptchaAndroidFlag(BooleanFlag):
    flag_name = 'Feature_HCaptchaAndroid'
    adapter = FeatureFlagAdapter.LD


class HCaptchaBusinessAndroidFlag(BooleanFlag):
    flag_name = 'Feature_HCaptchaBusinessAndroid'
    adapter = FeatureFlagAdapter.LD


class HCaptchaBusinessFlag(BooleanFlag):
    flag_name = 'Feature_HCaptchaBusiness'
    adapter = FeatureFlagAdapter.LD


class HCaptchaBusinessForceFlag(BooleanFlag):
    flag_name = 'Feature_HCaptchaBusinessForce'
    adapter = FeatureFlagAdapter.LD


class HCaptchaBusinessFrontdeskFlag(BooleanFlag):
    flag_name = 'Feature_HCaptchaBusinessFrontdesk'
    adapter = FeatureFlagAdapter.LD


class HCaptchaBusinessiOSFlag(BooleanFlag):
    flag_name = 'Feature_HCaptchaBusinessiOS'
    adapter = FeatureFlagAdapter.LD


class HCaptchaBusinessMaxScore(FloatFlag):
    flag_name = 'Feature_HCaptchaBusinessMaxScore'
    adapter = FeatureFlagAdapter.LD

    def __new__(cls, user: UserData = None, defaults: float = 0.7) -> float:
        return super().__new__(cls=cls, user=user, defaults=defaults)


class HCaptchaCustomerEmailChangeFlag(BooleanFlag):
    flag_name = 'Feature_HCaptchaCustomerEmailChangeFlag'
    adapter = FeatureFlagAdapter.EPPO


class HCaptchaCustomerInviteFlag(BooleanFlag):
    flag_name = 'Feature_HCaptchaCustomerInvite'
    adapter = FeatureFlagAdapter.LD


class HCaptchaFlag(BooleanFlag):
    flag_name = 'Feature_HCaptcha'
    adapter = FeatureFlagAdapter.LD


class HCaptchaForceFlag(BooleanFlag):
    flag_name = 'Feature_HCaptchaForce'
    adapter = FeatureFlagAdapter.LD


class HCaptchaiOSFlag(BooleanFlag):
    flag_name = 'Feature_HCaptchaiOS'
    adapter = FeatureFlagAdapter.LD


class HCaptchaMaxScore(FloatFlag):
    flag_name = 'Feature_HCaptchaMaxScore'
    adapter = FeatureFlagAdapter.LD

    def __new__(cls, user: UserData = None, defaults: float = 0.7) -> float:
        return super().__new__(cls=cls, user=user, defaults=defaults)


class PasswordResetTokenInvalidationFlag(BooleanFlag):
    flag_name = 'Feature_PasswordResetTokenInvalidation'
    adapter = FeatureFlagAdapter.EPPO


class ReCaptchaCustomerFlag(BooleanFlag):
    flag_name = 'Feature_ReCaptchaCustomer'
    adapter = FeatureFlagAdapter.LD


class ReCaptchaMinScoreFlag(FloatFlag):
    flag_name = 'Feature_ReCaptchaMinScore'
    adapter = FeatureFlagAdapter.LD

    def __new__(cls, user: UserData = None, defaults: float = 0.39) -> float:
        return super().__new__(cls=cls, user=user, defaults=defaults)


class SecurityBlacklistFlag(BooleanFlag):
    flag_name = 'Feature_SecurityBlacklist'
    adapter = FeatureFlagAdapter.LD
