import dataclasses
import datetime
import uuid
from dataclasses import dataclass
from typing import Any, Optional, TypedDict

from lib.enums import StrEnum
from lib.payment_gateway.enums import PaymentMethodType
from lib.payment_providers.enums import (
    AccountHolderNotCreatedReason,
    ExternalPaymentMethodType,
    PaymentOperationStatus,
    PaymentOperationType,
    PaymentStatus,
    PayoutMethodType,
    ProviderAccountHolderStatus,
    SetupIntentStatus,
    StripeAccountBusinessType,
    StripeAccountType,
    TransferFundStatus,
)
from lib.payment_providers.enums import (
    PayoutMethodStatus,
    PayoutMethodErrorCode,
)
from lib.payment_providers.errors import PortError
from lib.payments.enums import (
    PaymentError,
    PaymentProviderCode,
    PayoutError,
    PayoutStatus,
    PayoutType,
    TokenizedPaymentMethodInternalStatus,
)
from webapps.business_consents.enums import ConsentCode

# for "id" fields:
# pylint: disable=invalid-name
from webapps.stripe_integration.enums import FastPayoutStatus


@dataclasses.dataclass(frozen=True)
class PortResponse:
    entity_type: str
    entity: Optional[Any] = None
    errors: list[PortError] = dataclasses.field(default_factory=list)


@dataclasses.dataclass(frozen=True)
class AuthResponse:
    success: bool


@dataclasses.dataclass(frozen=False)
class AccountHolderEntity:
    id: uuid.UUID
    statement_name: str
    metadata: dict
    kyc_link: Optional[str] = None
    providers: Optional[dict] = None


@dataclasses.dataclass(frozen=True)
class ConcreteAccountHolderEntity: ...  # pylint: disable=multiple-statements


@dataclasses.dataclass(frozen=True)
class ConcreteAccountHolderSettingsEntity: ...  # pylint: disable=multiple-statements


@dataclasses.dataclass(frozen=True)
class StripeAccountHolderEntity(ConcreteAccountHolderEntity):
    payouts_enabled: bool
    charges_enabled: bool
    blocked: bool
    charge_fee_for_tips: bool
    created: datetime.datetime


@dataclasses.dataclass(frozen=True)
class AdyenAccountHolderEntity(ConcreteAccountHolderEntity):
    external_id: str
    account_code: str
    payouts_enabled: bool
    kyc_verified_at_least_once: bool


@dataclasses.dataclass(frozen=True)
class StripeAccountHolderSettingsEntity(ConcreteAccountHolderSettingsEntity):
    pba_fees_accepted: bool | None = None
    bcr_fees_accepted: bool | None = None
    tap_to_pay_fees_accepted: bool | None = None
    tap_to_pay_fees_accepted_at: datetime.datetime | None = None
    tap_to_pay_celebration_screen_shown: bool | None = None


@dataclasses.dataclass(frozen=True)
class PaymentClientTokenEntity:
    token: str


@dataclasses.dataclass(frozen=True)
class SetupIntentEntity:
    id: str
    client_secret: str
    status: SetupIntentStatus


@dataclasses.dataclass(frozen=True)
class CustomerEntity:
    id: uuid.UUID
    name: str
    email: str
    phone: str
    metadata: dict


@dataclasses.dataclass(frozen=False)
class PaymentEntity:  # pylint: disable=too-many-instance-attributes
    id: uuid.UUID
    status: PaymentStatus
    amount: int
    fee_amount: int
    auto_capture: bool
    additional_data: dict = dataclasses.field(default_factory=dict)
    error_code: Optional[PaymentError] = None
    action_required_details: Optional[dict] = None
    metadata: dict = dataclasses.field(default_factory=dict)
    provider_external_id: Optional[str] = None
    tokenized_pm_id: uuid.UUID | None = None


@dataclasses.dataclass(frozen=True)
class PaymentOperationEntity:
    id: uuid.UUID
    payment_id: uuid.UUID
    type: PaymentOperationType
    amount: int
    status: PaymentOperationStatus


@dataclasses.dataclass(frozen=False)
class TransferFundEntity:
    id: uuid.UUID
    amount: int
    status: TransferFundStatus
    provider_external_id: Optional[str] = None
    created: Optional[datetime.datetime] = None


@dataclasses.dataclass(frozen=True)
class PayoutEntity:
    id: uuid.UUID
    payment_provider_code: PaymentProviderCode
    account_holder_id: uuid.UUID
    status: PayoutStatus
    payout_type: PayoutType
    amount: int
    error_code: Optional[PayoutError] = None
    expected_arrival_date: Optional[datetime.datetime] = None


@dataclasses.dataclass(frozen=True)
class AdditionalDataEntity:
    """
    additional data for example required from Adyen to pass
    """


class DeviceDataDict(TypedDict):
    """
    Typed dict structure holding information about device used for performing
    Adyen operations.

    Providing device fingerprint and phone number is a great way of detecting
    fraudulent operations, not authorized by cardholder.
    """

    device_fingerprint: str
    phone_number: str
    user_agent: str
    ip: str


@dataclasses.dataclass(frozen=True)
class AdyenBasketItemEntity:  # webapps.pos.provider.adyen_ee._basket_item
    item_id: int
    product_title: str
    amount_per_item: int
    quantity: int
    manufacturer: int
    product_type: str
    currency: str
    total_amount: int
    unit_of_measure: str


@dataclasses.dataclass(frozen=True)
class DeviceDataEntity:
    device_fingerprint: str | None
    phone_number: str | None
    user_agent: str | None
    ip: str | None


class RecurringModel(StrEnum):
    UNSCHEDULED_CARD_ON_FILE = 'UnscheduledCardOnFile'
    CARD_ON_FILE = 'CardOnFile'


@dataclasses.dataclass(frozen=True)
class AdyenFindPaymentEntity:
    auth_psp_reference: str


@dataclasses.dataclass(frozen=True)
class AuthorizePaymentMethodDataEntity:
    tokenized_pm_id: Optional[uuid.UUID] = None
    external_token_type: Optional[ExternalPaymentMethodType] = None
    payment_token: Optional[str] = None
    gift_cards_ids: Optional[list[str]] = None

    def is_external_payment_method(self) -> bool:
        return self.external_token_type and self.payment_token


@dataclasses.dataclass(frozen=True)
class AdyenAuthAdditionalDataEntity:
    device_data: DeviceDataEntity
    basket_items: list[AdyenBasketItemEntity]
    total_tax_amount: int
    order_date: str
    recurring_model: RecurringModel
    user_v1_id: Optional[int] = None


@dataclasses.dataclass(frozen=True)
class StripeAuthAdditionalDataEntity:
    pass


@dataclasses.dataclass(frozen=True)
class BooksyGiftCardsAuthAdditionalDataEntity:
    user_id: int


@dataclasses.dataclass(frozen=True)
class FraudPreventionAuthAdditionalDataEntity:
    nethone_attempt_reference: str


@dataclasses.dataclass(frozen=True)
class AuthAdditionalDataEntity:
    stripe: Optional[StripeAuthAdditionalDataEntity] = None
    adyen: Optional[AdyenAuthAdditionalDataEntity] = None
    booksy_gift_cards: Optional[BooksyGiftCardsAuthAdditionalDataEntity] = None
    fraud_prevention: Optional[FraudPreventionAuthAdditionalDataEntity] = None

    @property
    def device_data(self):
        if self.adyen:
            return self.adyen.device_data
        return None


@dataclasses.dataclass(frozen=True)
class ConnectionTokenEntity:
    token: str


@dataclasses.dataclass(frozen=True)
class PayoutDetailsEntity:
    """
    The set payments, refunds, disputes, transfers etc.
    that were paid out in the payout
    """

    payments: list[PaymentEntity]
    payment_operations: list[PaymentOperationEntity]
    transfer_funds: list[TransferFundEntity]


@dataclasses.dataclass(frozen=True)
class TokenizedPaymentMethodEntity:
    id: uuid.UUID
    customer: uuid.UUID
    provider_code: PaymentProviderCode
    method_type: PaymentMethodType
    default: bool
    details: dict
    internal_status: TokenizedPaymentMethodInternalStatus


@dataclasses.dataclass(frozen=True)
class StripeTokenizedPMExternalData:
    external_id: str


@dataclasses.dataclass(frozen=True)
class CardData:
    last_digits: str
    expiry_year: int
    expiry_month: int
    brand: str
    cardholder_name: str
    alias: Optional[str] = None
    bin: Optional[str] = None


@dataclasses.dataclass(frozen=True)
class TokenizedPMExternalData:
    stripe: Optional[StripeTokenizedPMExternalData] = None
    card_data: Optional[CardData] = None


@dataclasses.dataclass(frozen=True)
class StripeApplePayPaymentTokenExternalData:
    pk_token: str
    pk_token_instrument_name: str
    pk_token_payment_network: str
    pk_token_transaction_id: str


@dataclasses.dataclass(frozen=True)
class PaymentTokenExternalData:
    stripe: Optional[StripeApplePayPaymentTokenExternalData] = None


@dataclasses.dataclass(frozen=True)
class PaymentTokenEntity:
    id: str


@dataclasses.dataclass(frozen=True)
class CreateStripeAccountHolderAdditionalData:
    business_id: int


@dataclasses.dataclass(frozen=True)
class CreateProviderAccountHolderAdditionalData:
    stripe: CreateStripeAccountHolderAdditionalData | None = None


# ==== Create AccountHolder from external data ====
@dataclasses.dataclass(frozen=True)
class FRExternalSourceAccountHolderData:
    SIREN: str


@dataclasses.dataclass(frozen=True)
class PLExternalSourceAccountHolderData:
    NIP: Optional[str]
    date_of_birth: Optional[datetime.date] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None


@dataclasses.dataclass(frozen=True)
class ExternalSourceAccountHolderData:
    fr: FRExternalSourceAccountHolderData | None = None
    pl: PLExternalSourceAccountHolderData | None = None


@dataclasses.dataclass(frozen=True)
class StripeAccountHolderExternalSourceAdditionalData:
    provider_data: CreateStripeAccountHolderAdditionalData
    external_source_data: ExternalSourceAccountHolderData


@dataclasses.dataclass(frozen=True)
class AccountHolderExternalSourceAdditionalData:
    stripe: StripeAccountHolderExternalSourceAdditionalData


@dataclasses.dataclass(frozen=True)
class USUserInputKYCAccountHolderData:
    type: str
    filling_date: datetime.datetime
    filling_ip: str
    first_name: str | None = None
    last_name: str | None = None
    company_name: str | None = None


@dataclasses.dataclass(frozen=True)
class UserInputKYCAccountHolderData:
    us: USUserInputKYCAccountHolderData


@dataclasses.dataclass(frozen=True)
class StripeAccountHolderUserInputKYCAdditionalData:
    provider_data: CreateStripeAccountHolderAdditionalData
    user_input_kyc_data: UserInputKYCAccountHolderData


@dataclasses.dataclass(frozen=True)
class AccountHolderUserInputKYCAdditionalData:
    stripe: StripeAccountHolderUserInputKYCAdditionalData


@dataclasses.dataclass(frozen=True)
class AccountHolderNotCreatedEventEntity:
    provider_code: PaymentProviderCode
    account_holder: AccountHolderEntity
    reason: AccountHolderNotCreatedReason


# ==== Create AccountHolder from external data ====


@dataclasses.dataclass(frozen=True)
class StripeAccountHolderModifyData:
    account_link_first_time_created: datetime.datetime | None = None
    payout_method_first_time_attached: datetime.datetime | None = None


@dataclasses.dataclass(frozen=True)
class AccountHolderModifyData:
    stripe: StripeAccountHolderModifyData | None = None


@dataclasses.dataclass(frozen=True)
class AccountHolderSettingsData:
    stripe: StripeAccountHolderSettingsEntity | None = None


#### Account Details ####


@dataclasses.dataclass(frozen=True)
class BankAccount:
    last_digits: str
    account_holder_name: str
    bank_name: str
    routing_number: str
    country: str


@dataclasses.dataclass(frozen=True)
class PersonalDetails:
    first_name: str
    last_name: str
    date_of_birth: datetime.date
    ssn_provided: bool | None


@dataclasses.dataclass(frozen=True)
class CompanyDetails:
    name: str
    is_tax_id_provided: bool


@dataclasses.dataclass(frozen=True)
class PayoutMethodDetails:
    method_type: PayoutMethodType
    # either card or bank_account is not None depending on method_type
    card: CardData | None
    bank_account: BankAccount | None
    token: str
    is_default_for_regular_payouts: bool
    is_default_for_fast_payouts: bool
    has_fast_payout_capability: bool
    status: PayoutMethodStatus
    error_code: PayoutMethodErrorCode | None


@dataclasses.dataclass(frozen=True)
class StripeAccountDetails:
    account_type: StripeAccountType
    business_type: StripeAccountBusinessType
    kyc_required_threshold: int  # amount of processed money after which KYC will be required


@dataclasses.dataclass(frozen=True)
class UserInputKYC:
    show: bool
    consent_code: ConsentCode | None


# pylint: disable=too-many-instance-attributes
@dataclasses.dataclass(frozen=True)
class ProviderAccountDetails:
    payment_provider_code: PaymentProviderCode
    status: ProviderAccountHolderStatus
    fast_payouts_status: FastPayoutStatus
    payments_enabled: bool
    payouts_enabled: bool
    kyc_errors: list[str]
    payout_methods: list[PayoutMethodDetails]
    personal_details: PersonalDetails | None
    company_details: CompanyDetails | None
    provider_specific: StripeAccountDetails  # add other provider entities here (with '|')
    available_payout_method_types: list[PayoutMethodType]
    available_fast_payout_method_types: list[PayoutMethodType]
    bank_account_number_form: dict | None
    kyc_verified_at_least_once: bool
    external_id: str
    user_input_kyc: UserInputKYC


@dataclasses.dataclass(frozen=True)
class ProviderAccountOnlineBalance:
    available: int
    pending: int
    total: int
    available_for_fast_payout: int


@dataclasses.dataclass(frozen=True)
class ProviderAccountStatus:
    payment_provider_code: PaymentProviderCode
    status: ProviderAccountHolderStatus
    payouts_enabled: bool
    kyc_verified_at_least_once: bool


@dataclass(frozen=True)
class AccountHolderSirenEntity:
    business_id: int
    siren: str


@dataclass(frozen=True)
class AccountHolderNIPEntity:
    business_id: int
    nip: str
    date_of_birth: datetime.date | None


@dataclass(frozen=True)
class AccountHolderPESELEntity:
    business_id: int
    date_of_birth: datetime.date
    first_name: str
    last_name: str


@dataclasses.dataclass(frozen=True)
class AccountStatusResponse:
    is_kyced_and_active: bool


@dataclass(frozen=True)
class USUserInputKYCEntity:
    business_id: int
    type: str
    filling_date: datetime.datetime
    filling_ip: str
    first_name: str | None
    last_name: str | None
    company_name: str | None


@dataclass(frozen=True)
class UserRequestData:
    user_id: int
    ip_address: str
