import uuid
from dataclasses import dataclass

# for "id" fields:
# pylint: disable=invalid-name


@dataclass(frozen=True)
class POSEntity:
    id: str
    receipt_footer_line_1: str | None
    receipt_footer_line_2: str | None


@dataclass(frozen=True)
class PaymentRowEntity:
    id: int
    payment_link: bool
    receipt_id: int


@dataclass(frozen=True)
class ReceiptDetailsEntity:
    id: int
    receipt_number: str
    assigned_number: str
    transaction_id: int
    customer_data: str | None


@dataclass
class HandleGiftCardTransactionEntity:
    appointment_status: str
    charge_bgc: bool


@dataclass(frozen=True)
class TransactionEntity:
    id: uuid.UUID
