import os
import signal
import sys
from datetime import datetime
from pathlib import Path
from time import sleep

import redis
import yaml
from bo_obs.datadog import enable_datadog

from lib.tasks.consts import ETA_PASS_TO_QUEUE_SCRIPT
from lib.utils import str_to_bool

MOVE_SCRIPT_BATCH = 500


def signal_handler(_sig, _frame):
    """
    Signal handler function for SIGTERM.
    """
    sys.exit(0)  # Exit cleanly


def get_broker_url():
    with open("/opt/deploy/conf/api.yaml", 'rt', encoding='utf-8') as stream:
        try:
            url = yaml.safe_load(stream)
        except yaml.YAMLError as exc:
            print(exc)
            raise exc
        return url['celery_broker_url']


if str_to_bool(os.getenv("CELERY_ETA", "False")):
    # We don't want django setup, because it is heavy and have a lot points of failure.
    # We want to omit in this script.
    def get_broker():
        """Get a connection to default redis used as celery broker."""
        host_port, db = get_broker_url().split('/')[2:]
        host, port = host_port.split(':')
        return redis.StrictRedis(
            host=host,
            port=port,
            db=db,
            max_connections=100,
            health_check_interval=50,  # should be less than timeout on server
            socket_timeout=4,
            socket_connect_timeout=4,
            retry_on_timeout=True,
        )

else:
    from lib.celery_tools import get_broker


with get_broker() as broker_connection:
    eta_pass_to_queue_script = broker_connection.register_script(ETA_PASS_TO_QUEUE_SCRIPT)


def move_eta_jobs(script_terations=4):
    now_timestamp = datetime.now().timestamp()
    for _iteration in range(script_terations):
        if eta_pass_to_queue_script(args=(now_timestamp, MOVE_SCRIPT_BATCH)) != MOVE_SCRIPT_BATCH:
            return


if __name__ == '__main__':
    from lib.monkeypatching import patch_get_redis_connection

    SCRIPT_LIVENESS_FILE = Path('/tmp/celery_eta_health')

    # Register the signal handler for SIGTERM
    # signal.SIGTERM is the standard termination signal
    signal.signal(signal.SIGTERM, signal_handler)

    # You can also handle SIGINT (Ctrl+C) for local testing
    signal.signal(signal.SIGINT, signal_handler)

    patch_get_redis_connection()
    enable_datadog([])

    while True:
        move_eta_jobs()
        sleep(2)
        SCRIPT_LIVENESS_FILE.touch()
