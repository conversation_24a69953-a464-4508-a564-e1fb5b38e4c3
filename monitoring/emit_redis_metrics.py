import json
import os
from time import sleep
from pathlib import Path

import redis
from datadog import initialize
from datadog import statsd
from redis import Redis

from lib.utils import str_to_bool


INSTRUCTION_PATH = '/tmp/shared/queues_check_instruction.json'


def load_metric_instructions():
    for _ in range(10):
        if Path(INSTRUCTION_PATH).exists():
            break
        sleep(10)
    with open(INSTRUCTION_PATH, 'r', encoding="utf-8") as file:  # nosemgrep
        return json.load(file)


def get_client(url):
    return Redis.from_url(
        url,
        socket_connect_timeout=5,
        socket_timeout=10,
        retry_on_timeout=True,
    )


def check_celery(celery_data):
    data = {}
    client = get_client(celery_data['server'])
    for queue in celery_data['queues']:
        try:
            queue_length = client.llen(queue)
        except redis.ResponseError:
            queue_length = 0
        finally:
            data[queue] = queue_length

    by_priority = {
        priority: sum(data.get(queue, 0) for queue in queues)
        for priority, queues in celery_data['priority_mapping'].items()
    }

    by_app = {
        app_name: sum(data.get(queue, 0) for queue in queues)
        for app_name, queues in celery_data['app_mapping'].items()
    }

    try:
        pipeline = client.pipeline()
        pipeline.hlen(celery_data['eta']['id_body'])
        pipeline.hlen(celery_data['eta']['id_queue'])
        pipeline.zcard(celery_data['eta']['id_by_time'])
        eta_task_structures_len = pipeline.execute()
    except redis.ResponseError:
        eta_task_structures_len = [0, 0, 0]

    try:
        unacked = client.hlen('unacked')
    except redis.ResponseError:
        unacked = 0

    emit_celery_metrics(
        by_app, by_priority, eta_task_structures_len, unacked, celery_data['api_country']
    )


def emit_celery_metrics(by_app, by_priority, eta_tasks, unacked, api_country):
    statsd.histogram(
        'booksy.queue.by_app.celery_priority_worker',
        by_app['celery-priority-worker'],
        tags=[
            'app:celery_priority_worker',
            f'horizontalpodautoscaler:core-{api_country}-celery-priority-worker',
            f'country:{api_country}',
        ],
    )
    statsd.gauge(
        'booksy.queue.by_app.celery_priority_worker.gauge',
        by_app['celery-priority-worker'],
        tags=[
            'app:celery_priority_worker',
            f'horizontalpodautoscaler:core-{api_country}-celery-priority-worker',
            f'country:{api_country}',
        ],
    )
    statsd.histogram(
        'booksy.queue.by_app.celery_regular_worker',
        by_app['celery-regular-worker'],
        tags=[
            'app:celery_regular_worker',
            f'horizontalpodautoscaler:core-{api_country}-celery-regular-worker',
            f'country:{api_country}',
        ],
    )
    statsd.gauge(
        'booksy.queue.by_app.celery_regular_worker.gauge',
        by_app['celery-regular-worker'],
        tags=[
            'app:celery_regular_worker',
            f'horizontalpodautoscaler:core-{api_country}-celery-regular-worker',
            f'country:{api_country}',
        ],
    )
    statsd.histogram(
        'booksy.queue.by_app.celery_push_worker',
        by_app['celery-push-worker'],
        tags=[
            'app:celery_push_worker',
            f'horizontalpodautoscaler:core-{api_country}-celery-push-worker',
            f'country:{api_country}',
        ],
    )
    statsd.gauge(
        'booksy.queue.by_app.celery_push_worker.gauge',
        by_app['celery-push-worker'],
        tags=[
            'app:celery_push_worker',
            f'horizontalpodautoscaler:core-{api_country}-celery-push-worker',
            f'country:{api_country}',
        ],
    )
    statsd.histogram(
        'booksy.queue.by_app.celery_segment_worker',
        by_app['celery-segment-worker'],
        tags=[
            'app:celery_segment_worker',
            f'horizontalpodautoscaler:core-{api_country}-celery-segment-worker',
            f'country:{api_country}',
        ],
    )
    statsd.gauge(
        'booksy.queue.by_app.celery_segment_worker.gauge',
        by_app['celery-segment-worker'],
        tags=[
            'app:celery_segment_worker',
            f'horizontalpodautoscaler:core-{api_country}-celery-segment-worker',
            f'country:{api_country}',
        ],
    )
    statsd.histogram(
        'booksy.queue.by_app.celery_index_worker',
        by_app['celery-index-worker'],
        tags=[
            'app:celery_index_worker',
            f'horizontalpodautoscaler:core-{api_country}-celery-index-worker',
            f'country:{api_country}',
        ],
    )
    statsd.gauge(
        'booksy.queue.by_app.celery_index_worker.gauge',
        by_app['celery-index-worker'],
        tags=[
            'app:celery_index_worker',
            f'horizontalpodautoscaler:core-{api_country}-celery-index-worker',
            f'country:{api_country}',
        ],
    )

    statsd.gauge(
        'booksy.queue.by_app.celery_all_queue_worker',
        by_app['celery-all-queue-worker'],
        tags=[
            'app:celery-all-queue-worker',
            f'horizontalpodautoscaler:core-{api_country}-celery-all-queue-worker',
            f'country:{api_country}',
        ],
    )

    statsd.gauge(
        'booksy.queue.eta_tasks',
        eta_tasks[0],
        tags=[
            f'country:{api_country}',
        ],
    )
    if len(set(eta_tasks)) != 1:
        statsd.event(
            'Eta tasks not consistent',
            f'Unconsistent eta tasks: {eta_tasks}',
            alert_type='error',
            tags=[
                f'country:{api_country}',
            ],
        )

    statsd.histogram('booksy.queue.unacked', unacked)

    for priority, task_quantity in by_priority.items():
        statsd.histogram(f'booksy.queue.by_priority.{priority}', task_quantity)
        statsd.gauge(f'booksy.queue.by_priority.{priority}.gauge', task_quantity)


def check_river(river_data):
    river_queues = {}
    client = get_client(river_data['server'])
    for queue in river_data['queues']:
        try:
            queue_length = client.scard(queue)
        except redis.ResponseError:
            queue_length = 0
        finally:
            river_queues[queue] = queue_length

    for river_queue_name, river_queue_size in river_queues.items():
        statsd.gauge(
            'booksy.river_queue.gauge',
            river_queue_size,
            tags=[f'river_type:{river_queue_name}'],
        )


def check_email(email_data):
    client = get_client(email_data['server'])
    try:
        emails_count = client.llen(email_data['queue'])
    except redis.ResponseError:
        emails_count = 0

    statsd.gauge(
        'booksy.email_queue',
        emails_count,
    )


def emit_queue_metric():
    queue_data = load_metric_instructions()
    if str_to_bool(os.getenv('CUSTOM_METRICS_ENABLED', 'False')):
        while True:
            check_celery(queue_data['celery'])
            check_river(queue_data['river'])
            check_email(queue_data['email'])
            sleep(40)
    else:

        class ConfigException(Exception):
            pass

        raise ConfigException('I should not be runned')


if __name__ == '__main__':
    initialize()
    emit_queue_metric()
