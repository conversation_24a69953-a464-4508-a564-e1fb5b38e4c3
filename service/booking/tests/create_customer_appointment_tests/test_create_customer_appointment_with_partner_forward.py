import datetime
from datetime import time

import pytest
from django.utils.translation import gettext as _
from mock import (
    Mock,
    patch,
)
from model_bakery import baker
from pytz import UTC
from rest_framework import status

from lib.feature_flag.bug import PartnerOnlineBookingPreventMaskingCustomerNameFlag
from lib.tests.utils import override_feature_flag
from merger_grpc.proto.appointment_pb2 import (
    AppointmentDetails as MergerGrpcAppointmentDetails,
    TimeSlotsQueryResponse as MergerGrpcTimeSlotsQueryResponse,
    AppointmentRequiredPayment as MergerGrpcAppointmentRequiredPayment,
    SubBooking as MergerGrpcSubBooking,
)
from service.booking.tests.create_customer_appointment_tests import (
    AppointmentTestCaseMixin,
    make_staffer_notifications,
)
from service.partner_forward import PartnerForward
from service.tests import (
    BaseAsyncHTTPTest,
)
from webapps.booking.merger.appointment_client import (
    AppointmentClient as MergerGrpcAppointmentClient,
)
from webapps.booking.models import Appointment
from webapps.booking.notifications.appointment_created import (
    AppointmentCreatedNotification,
    SubBookingCreatedNotification,
)
from webapps.booking.tests.test_appointment_base import (
    BaseTestAppointment,
    build_custappt_data,
)
from webapps.business.models import (
    Business,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.notification.models import (
    NotificationSchedule,
    UserNotification,
)
from webapps.notification.recipients import Recipient
from webapps.user.enums import AuthOriginEnum

TEST_DATETIME = datetime.datetime(2018, 1, 1, tzinfo=UTC)


class AppointmentPartnerForwardTestCase(
    AppointmentTestCaseMixin,
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

    def _patch_merger_grpc_client(
        self,
        booked_from_timestamp=None,
        booked_till_timestamp=None,
        customer_note='Customer note',
        with_prepayment=False,
    ):
        booked_from_timestamp = booked_from_timestamp or int(
            datetime.datetime.timestamp(self._dt_from_hour(time(10, 0)))
        )
        booked_till_timestamp = booked_till_timestamp or int(
            datetime.datetime.timestamp(self._dt_from_hour(time(11, 0)))
        )

        def new_save_appointment(appointment_details, *_, **__):
            appointment_params = self._prepare_appointment_params(
                booked_from_timestamp,
                booked_till_timestamp,
                customer_note,
                with_prepayment,
                booked_for_id=appointment_details.booked_for_id,
            )
            return MergerGrpcAppointmentDetails(**appointment_params)

        def new_dry_run_appointment(appointment_details, *_, **__):
            appointment_params = self._prepare_appointment_params(
                booked_from_timestamp,
                booked_till_timestamp,
                customer_note,
                with_prepayment,
                booked_for_id=appointment_details.booked_for_id or None,
            )
            return MergerGrpcAppointmentDetails(**appointment_params)

        def new_time_slots(*_, **__):
            return MergerGrpcTimeSlotsQueryResponse(**self._prepare_time_slots_params())

        mocks = {
            'save_appointment_mock': Mock(side_effect=new_save_appointment),
            'dry_run_appointment_mock': Mock(side_effect=new_dry_run_appointment),
            'time_slots_mock': Mock(side_effect=new_time_slots),
        }
        old_init = MergerGrpcAppointmentClient.__init__

        def new_init(instance: MergerGrpcAppointmentClient, *args, **kwargs):
            old_init(instance, *args, **kwargs)
            instance.SaveAppointment = mocks['save_appointment_mock']
            instance.DryRunAppointment = mocks['dry_run_appointment_mock']
            instance.TimeSlotsQuery = mocks['time_slots_mock']

        merger_grpc_client_patcher = patch.object(MergerGrpcAppointmentClient, '__init__', new_init)

        return merger_grpc_client_patcher, mocks

    def _prepare_appointment_params(
        self,
        booked_from_timestamp,
        booked_till_timestamp,
        customer_note,
        with_prepayment,
        booked_for_id=None,
    ):
        appointment_params = {
            'business_id': self.business.id,
            'id': 200,
            'import_id': '123456',
            'status': 'A',
            'customer_name': '',
            'customer_phone': '',
            'customer_email': '',
            'customer_note': customer_note,
            'booked_for_id': booked_for_id,
            '_from_subdomain': False,
            'subbookings': [
                MergerGrpcSubBooking(
                    booked_from=booked_from_timestamp,
                    booked_till=booked_till_timestamp,
                    service_variant_id=self.variant.id,
                    id=500,
                    staffer_id=self.staffer.id,
                    appliance_id=None,
                )
            ],
        }

        if with_prepayment:
            appointment_params['_required_payment'] = MergerGrpcAppointmentRequiredPayment(
                prepayment="10.0",
                payment_url="https://some-payment-domain.com/payment",
            )

        return appointment_params

    @staticmethod
    def _prepare_time_slots_params():
        return {
            'business_id': 123,
            'staffers_availability': [{'staffer_id': 321, 'slots': [123123123, 123123246]}],
        }


@pytest.mark.django_db
class CreateAppointmentTestCase(AppointmentPartnerForwardTestCase):
    @patch.object(PartnerForward, 'should_forward', return_value=True)
    def test_handler_custappt_create_single(self, _forward_mock):
        booked_from = self._dt_from_hour(time(10, 0))
        booked_from_timestamp = int(datetime.datetime.timestamp(booked_from))
        booked_till = self._dt_from_hour(time(11, 0))
        booked_till_timestamp = int(datetime.datetime.timestamp(booked_till))
        body = build_custappt_data(
            self.variant,
            booked_from=booked_from,
            staffer=self.staffer,
            recurring=False,
        )
        customer_note = body['customer_note']

        merger_gprc_client_patcher, mocks = self._patch_merger_grpc_client(
            customer_note=customer_note,
            booked_from_timestamp=booked_from_timestamp,
            booked_till_timestamp=booked_till_timestamp,
        )

        with merger_gprc_client_patcher:
            NotificationHistoryDocument.tasks_clear()
            make_staffer_notifications(business=self.business, staffer=self.staffer)
            url = self.appointments_url(self.business.id)

            resp = self.fetch(url, method='POST', body=body)
            self.assertEqual(resp.code, status.HTTP_201_CREATED)

            self.assertEqual(resp.json['appointment']['business']['id'], self.business.id)
            self.assertEqual(resp.json['appointment']['appointment_id'], 500)
            self.assertEqual(resp.json['appointment']['appointment_uid'], 200)
            self.assertEqual(resp.json['appointment']['customer_note'], customer_note)
            self.assertEqual(len(resp.json['appointment']['subbookings']), 1)

            # Merger GRPC client checks
            save_appointment_mock = mocks['save_appointment_mock']
            save_appointment_mock.assert_called_once()
            save_appointment_args = save_appointment_mock.call_args.args
            appointment_details_arg = save_appointment_args[0]

            self.assertEqual(appointment_details_arg.business_id, self.business.id)
            self.assertEqual(
                appointment_details_arg.subbookings[0].booked_from,
                booked_from_timestamp,
            )
            self.assertEqual(appointment_details_arg.customer_note, customer_note)

            # CreateAppointmentMetadata
            self.assertIs(resp.json['meta']['first'], True)
            self.assertIs(resp.json['meta']['first_cross'], False)
            self.assertIs(resp.json['meta']['cross'], False)

            # GTM Energy booking mixin
            self.assertTrue(
                set(resp.json['appointment']['meta'].keys()).issuperset(
                    {
                        'energy_booking',
                        'booking_score',
                        'first',
                        'cross',
                        'first_cross',
                    }
                ),
            )

            # NotificationSchedules
            appointment_id = resp.json['appointment']['appointment_uid']
            appointment = Appointment.objects.get(pk=appointment_id)

            appt_notif = AppointmentCreatedNotification(appointment)
            appt_notif.schedule_record.assert_skipped()
            staffer_recipient = Recipient.from_staffer(self.staffer)
            self.assertNotIn(staffer_recipient, appt_notif.resolved_recipients)

            booking_notif = SubBookingCreatedNotification(appointment.subbookings[0])
            booking_notif.schedule_record.assert_success()

            self.assertTrue(
                NotificationSchedule.objects.filter(
                    task_id=(
                        f'booking_changed:customer_booking_confirmation:'
                        f'appointment_id={appointment_id}'
                    ),
                ).exists(),
            )
            self.assertTrue(
                NotificationSchedule.objects.filter(
                    task_id=(
                        f'booking_changed:business_booking_confirmation:'
                        f'appointment_id={appointment_id}'
                    ),
                ).exists(),
            )

            notification = NotificationHistoryDocument.task_get(
                task_id=(
                    f'booking_changed:business_booking_confirmation:'
                    f'appointment_id={appointment_id}'
                ),
                type=UserNotification.PUSH_NOTIFICATION,
            )
            self.assertIsNotNone(notification)

    @patch.object(PartnerForward, 'should_forward', return_value=True)
    def test_handler_custappt_create_single_with_prepayment(self, _forward_mock):
        booked_from = self._dt_from_hour(time(10, 0))
        booked_from_timestamp = int(datetime.datetime.timestamp(booked_from))
        booked_till = self._dt_from_hour(time(11, 0))
        booked_till_timestamp = int(datetime.datetime.timestamp(booked_till))
        body = build_custappt_data(
            self.variant,
            booked_from=booked_from,
            staffer=self.staffer,
            recurring=False,
        )
        customer_note = body['customer_note']

        merger_gprc_client_patcher, _mocks = self._patch_merger_grpc_client(
            customer_note=customer_note,
            booked_from_timestamp=booked_from_timestamp,
            booked_till_timestamp=booked_till_timestamp,
            with_prepayment=True,
        )

        with merger_gprc_client_patcher:
            NotificationHistoryDocument.tasks_clear()
            make_staffer_notifications(business=self.business, staffer=self.staffer)
            url = self.appointments_url(self.business.id)

            resp = self.fetch(url, method='POST', body=body)
            self.assertEqual(resp.code, status.HTTP_201_CREATED)
            self.assertDictEqual(
                resp.json,
                {
                    'appointment_partner_payment': {
                        'prepayment': '10.00',
                        'payment_url': 'https://some-payment-domain.com/payment',
                    },
                    'appointment': {'subbookings': []},
                },
            )


class UserVisitsBusinessCardFirstTimeMixin(BaseTestAppointment):
    def setUp(self):
        super().setUp()
        self._simulate_user_visiting_first_time_partner_business_card()

    def _simulate_user_visiting_first_time_partner_business_card(self):
        baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            user=self.user,
            visible_in_business=False,
        )


@pytest.mark.django_db
class CreateAppointmentForNewCustomerAsAuthenticatedUserTestCase(
    AppointmentPartnerForwardTestCase, UserVisitsBusinessCardFirstTimeMixin
):
    @override_feature_flag(
        {
            PartnerOnlineBookingPreventMaskingCustomerNameFlag: True,
        }
    )
    @patch.object(PartnerForward, 'should_forward', return_value=True)
    def test_customer_name(self, _forward_mock):
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        merger_gprc_client_patcher, mocks = self._patch_merger_grpc_client()

        with merger_gprc_client_patcher:
            url = self.appointments_url(self.business.id)
            resp = self.fetch(url, method='POST', body=body)
            self.assertEqual(resp.code, status.HTTP_201_CREATED)

        save_appointment_mock = mocks['save_appointment_mock']
        save_appointment_mock.assert_called_once()
        save_appointment_args = save_appointment_mock.call_args.args
        appointment_details_arg = save_appointment_args[0]
        self.assertNotIn(_("Deleted user"), appointment_details_arg.customer_name)


@pytest.mark.django_db
class CreateAppointmentForUnauthenticatedUserTestCase(AppointmentPartnerForwardTestCase):
    def get_headers(self, path):
        headers = super().get_headers(path)
        headers.pop('X-ACCESS-TOKEN')
        return headers

    @override_feature_flag(
        {
            PartnerOnlineBookingPreventMaskingCustomerNameFlag: True,
        }
    )
    @patch.object(PartnerForward, 'should_forward', return_value=True)
    def test_dry_run(self, _forward_mock):
        body = build_custappt_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body['dry_run'] = True
        merger_gprc_client_patcher, mocks = self._patch_merger_grpc_client()

        with merger_gprc_client_patcher:
            url = self.appointments_url(self.business.id)
            resp = self.fetch(url, method='POST', body=body)

        self.assertEqual(resp.code, status.HTTP_201_CREATED)

        mocks['dry_run_appointment_mock'].assert_called_once()
        mocks['time_slots_mock'].assert_called_once()
