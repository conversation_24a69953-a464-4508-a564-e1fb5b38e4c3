import datetime
from datetime import time
from decimal import Decimal
import uuid

import pytest
from mock import patch
from django.test.utils import override_settings
from freezegun import freeze_time
from model_bakery import baker
from rest_framework import status

from lib.feature_flag.feature.payment import (
    ThreeDSecureInAppointmentEditionFlag,
)
from lib.tests.utils import override_eppo_feature_flag
from service.tests import (
    BaseAsyncHTTPTest,
    dict_assert,
)
from webapps import consts
from webapps.adyen.exceptions import ThreeDSecureAuthenticationRequired
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import PaymentType
from webapps.booking.enums import (
    SubbookingServiceVariantMode,
)
from webapps.booking.models import (
    Appointment,
    BookingSources,
)
from webapps.booking.tests.test_appointment_base import (
    BaseTestAppointment,
)
from webapps.booking.tests.utils import create_appointment
from webapps.business.enums import (
    PriceType,
)
from webapps.business.models import (
    Business,
    Service,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.pos.baker_recipes import pos_recipe
from webapps.pos.exceptions import BlikActionRequired
from webapps.pos.models import PaymentMethod, POS
from webapps.pos.provider.fake import _CARDS
from webapps.user.enums import AuthOriginEnum


@freeze_time(datetime.datetime(2022, 6, 1, 10))
@pytest.mark.django_db
class CustomerAppointmentHandlerMixin(
    BaseAsyncHTTPTest,
    BaseTestAppointment,
):
    url = '/customer_api/me/appointments/{}/'.format

    def setUp(self):
        super().setUp()
        self.pos = pos_recipe.make(business=self.business)

        self.customer = baker.make(
            BusinessCustomerInfo,
            user=self.session.get_user(),
            business=self.business,
        )

    @override_settings(
        POS__APPLE_PAY=True,
        POS__GOOGLE_PAY=True,
        POS__BLIK=True,
    )
    def test_handler_custappt_create_multi_bci_with_discount_and_prepayment_info(self):
        resp = self._get_test_common_part_with_discount(
            appointment_status=Appointment.STATUS.ACCEPTED,
            with_prepyament=False,
        )
        dict_assert(
            resp.json['appointment_payment'],
            {
                'cancellation_fee': '0.00',
                'cancellation_fee_tax': '0.00',
                'cancellation_fee_total': '0.00',
                'prepayment': '0.00',
                'prepayment_tax': '0.00',
                'prepayment_total': '0.00',
                'external_partners': {
                    'apple_pay': True,
                    'google_pay': True,
                    'blik': False,
                },
                'force_stripe_pba': False,
            },
        )

    @override_settings(
        POS__APPLE_PAY=True,
        POS__GOOGLE_PAY=True,
        POS__BLIK=True,
    )
    def test_handler_custappt_create_multi_bci_with_discount_and_prepayment(self):
        resp = self._get_test_common_part_with_discount(
            appointment_status=Appointment.STATUS.PENDING_PAYMENT,
            with_prepyament=True,
        )
        dict_assert(
            resp.json['appointment_payment'],
            {
                'cancellation_fee': '0.00',
                'cancellation_fee_tax': '0.00',
                'cancellation_fee_total': '0.00',
                'prepayment': '18.00',
                'prepayment_tax': '0.00',
                'prepayment_total': '18.00',
                'external_partners': {
                    'apple_pay': True,
                    'google_pay': True,
                    'blik': False,
                },
                'force_stripe_pba': False,
            },
        )

    def _get_test_common_part_with_discount(
        self,
        *,
        appointment_status: Appointment.STATUS,
        with_prepyament: bool,
        with_discount: bool = True,
        total_discount_amount: float = 20,
        total_value: float = 180,
    ):
        source = baker.make_recipe(
            'webapps.booking.booking_source_recipe',
            app_type=BookingSources.CUSTOMER_APP,
            name=consts.WEB,
        )

        bci = BusinessCustomerInfo.get_or_create_for_user(
            user=self.user,
            business=self.business,
            recurring=False,
            source=source,
        )
        if with_discount:
            bci.discount = 10
            bci.save()
        self.service.tax_rate = 23
        self.service.save()
        self.variant.type = PriceType.FIXED
        self.variant.price = 100
        self.variant.save()
        if with_prepyament:
            baker.make(
                ServiceVariantPayment,
                service_variant=self.variant,
                payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
                payment_amount=Decimal('10.00'),
                saving_type=ServiceVariantPayment.AMOUNT,
            )
        appointment = create_appointment(
            [
                {'service_variant': self.variant},
                {'service_variant': self.variant},
            ],
            business=self.business,
            booked_for=bci,
            status=appointment_status,
            type=Appointment.TYPE.BUSINESS,
        )
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        resp = self.fetch(self.url(appointment.id), method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['appointment']['total_discount_amount'] == total_discount_amount
        assert resp.json['appointment']['total_value'] == total_value
        return resp

    def _test_modify_appointment_with_external_partners(self):
        service = self.business.services.first()
        service_variant = service.service_variants.first()
        service.resources.add(self.staffer)
        tz = self.business.get_timezone()

        booked_from = datetime.datetime.combine(
            date=self.business.tznow.date(),
            time=time(13, 00),
            tzinfo=tz,
        )
        booked_till = booked_from + datetime.timedelta(hours=1)
        appointment = create_appointment(
            [
                {
                    'staffer': self.staffer,
                    'booked_from': booked_from,
                    'booked_till': booked_till,
                    'service_variant': service_variant,
                },
            ],
            status=Appointment.STATUS.ACCEPTED,
            business=self.business,
            booked_for=self.customer,
        )
        request = {
            'dry_run': False,
            'subbookings': [
                {
                    'booked_from': '2022-06-26T13:15',
                    'service_variant': {
                        'mode': SubbookingServiceVariantMode.VARIANT,
                        'id': service_variant.id,
                    },
                    'staffer_id': self.staffer.id,
                }
            ],
            'recurring': False,
        }

        resp = self.fetch(self.url(appointment.id), method='PUT', body=request)
        return resp


@freeze_time(datetime.datetime(2022, 6, 1, 10))
@pytest.mark.django_db
class CustomerAppointmentHandlerAutoTestCase(CustomerAppointmentHandlerMixin):
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()

    @override_settings(
        POS__APPLE_PAY=False,
        POS__GOOGLE_PAY=False,
        POS__BLIK=False,
    )
    def test_modify_appointment_no_external_partners(self):
        resp = self._test_modify_appointment_with_external_partners()
        assert resp.code == status.HTTP_200_OK
        assert resp.json['appointment']['status'] == Appointment.STATUS.ACCEPTED
        dict_assert(
            resp.json['appointment_payment']['external_partners'],
            {'google_pay': False, 'apple_pay': False, 'blik': False},
        )

    @override_settings(
        POS__APPLE_PAY=True,
        POS__GOOGLE_PAY=True,
        POS__BLIK=True,
    )
    def test_modify_appointment_with_external_partners(self):
        resp = self._test_modify_appointment_with_external_partners()
        assert resp.code == status.HTTP_200_OK
        assert resp.json['appointment']['status'] == Appointment.STATUS.ACCEPTED
        dict_assert(
            resp.json['appointment_payment']['external_partners'],
            {
                'google_pay': True,
                'apple_pay': True,
                'blik': False,
            },
        )

    @override_eppo_feature_flag({ThreeDSecureInAppointmentEditionFlag.flag_name: True})
    @patch('webapps.pos.provider.base.PaymentProvider.make_payment')
    def test_modify_appointment_with_3ds(self, make_payment_mock):
        service = self.business.services.first()
        service_variant = service.service_variants.first()
        service.resources.add(self.staffer)
        tz = self.business.get_timezone()

        booked_from = datetime.datetime.combine(
            date=self.business.tznow.date(),
            time=time(13, 00),
            tzinfo=tz,
        )
        booked_till = booked_from + datetime.timedelta(hours=1)
        # create appt without prepayment
        appointment = create_appointment(
            [
                {
                    'staffer': self.staffer,
                    'booked_from': booked_from,
                    'booked_till': booked_till,
                    'service_variant': service_variant,
                },
            ],
            status=Appointment.STATUS.ACCEPTED,
            business=self.business,
            booked_for=self.customer,
        )

        service_pp = baker.make(
            Service,
            business=self.business,
            active=True,
        )
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service_pp,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service_pp.add_staffers([self.staffer])
        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('21.37'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )
        baker.make(PaymentType, code=PaymentTypeEnum.PREPAYMENT, pos=self.pos)
        baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=self.pos)
        self.pos.pay_by_app_status = POS.PAY_BY_APP_ENABLED
        self.pos.pos_refactor_stage2_enabled = True
        self.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.pos.save()

        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.customer.user,
        )

        request = {
            'dry_run': False,
            'subbookings': [
                {
                    'booked_from': '2022-06-26T13:15',
                    'service_variant': {
                        'mode': SubbookingServiceVariantMode.VARIANT,
                        'id': with_prepayment.id,
                    },
                    'staffer_id': self.staffer.id,
                }
            ],
            'compatibilities': {
                'prepayment': True,
                'new_checkout': True,
            },
            'recurring': False,
            'payment_method': card.id,
        }

        with patch(
            'webapps.pos.services.TransactionService._check_auth_success',
            side_effect=ThreeDSecureAuthenticationRequired(
                '3DSecure', three_d_data={'test': '123'}, auth_reference=''
            ),
        ):
            resp = self.fetch(self.url(appointment.id), method='PUT', body=request)
        assert resp.code == status.HTTP_200_OK
        assert resp.json['three_d_data'] == {'test': '123'}

    @override_settings(POS__BLIK=True)
    @patch('webapps.pos.provider.proxy.ProxyProvider.make_payment')
    def test_modify_appointment_with_blik(self, make_payment_mock):
        service = self.business.services.first()
        service_variant = service.service_variants.first()
        service.resources.add(self.staffer)
        tz = self.business.get_timezone()

        booked_from = datetime.datetime.combine(
            date=self.business.tznow.date(),
            time=time(13, 00),
            tzinfo=tz,
        )
        booked_till = booked_from + datetime.timedelta(hours=1)
        # create appt without prepayment
        appointment = create_appointment(
            [
                {
                    'staffer': self.staffer,
                    'booked_from': booked_from,
                    'booked_till': booked_till,
                    'service_variant': service_variant,
                },
            ],
            status=Appointment.STATUS.ACCEPTED,
            business=self.business,
            booked_for=self.customer,
        )

        service_pp = baker.make(
            Service,
            business=self.business,
            active=True,
        )
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service_pp,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service_pp.add_staffers([self.staffer])
        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('21.37'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )
        baker.make(PaymentType, code=PaymentTypeEnum.PREPAYMENT, pos=self.pos)
        baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=self.pos)
        baker.make(PaymentType, code=PaymentTypeEnum.BLIK, pos=self.pos)
        self.pos.pay_by_app_status = POS.PAY_BY_APP_ENABLED
        self.pos.pos_refactor_stage2_enabled = True
        self.pos._force_stripe_pba = True  # pylint: disable=protected-access
        self.pos.save()

        request = {
            'dry_run': False,
            'subbookings': [
                {
                    'booked_from': '2022-06-26T13:15',
                    'service_variant': {
                        'mode': SubbookingServiceVariantMode.VARIANT,
                        'id': with_prepayment.id,
                    },
                    'staffer_id': self.staffer.id,
                }
            ],
            'compatibilities': {
                'prepayment': True,
                'new_checkout': True,
            },
            'recurring': False,
            'payment_method': None,
            'external_payment_method': {'partner': 'blik', 'token': '123456'},
        }
        bt_id = uuid.uuid4()
        with patch(
            'webapps.pos.services.TransactionService._check_auth_success',
            side_effect=BlikActionRequired(balance_transaction_id=bt_id),
        ):
            resp = self.fetch(self.url(appointment.id), method='PUT', body=request)
        assert resp.code == status.HTTP_200_OK
        assert resp.json['blik_data'] == {'balance_transaction_id': str(bt_id)}


@freeze_time(datetime.datetime(2022, 6, 1, 10))
@pytest.mark.django_db
class CustomerAppointmentHandlerSemiAutoTestCase(CustomerAppointmentHandlerMixin):
    def setUp(self):
        super().setUp()
        self.business.booking_mode = Business.BookingMode.SEMIAUTO
        self.business.save()

    @override_settings(
        POS__APPLE_PAY=True,
        POS__GOOGLE_PAY=True,
        POS__BLIK=False,
    )
    def test_handler_custappt_create_multi_bci_prepayment_info_semi_auto_unconfirmed(self):
        resp = self._get_test_common_part_with_discount(
            appointment_status=Appointment.STATUS.UNCONFIRMED,
            with_prepyament=True,
            total_discount_amount=0,
            total_value=200,
            with_discount=False,
        )
        dict_assert(
            resp.json['appointment_payment'],
            {
                'cancellation_fee': '0.00',
                'cancellation_fee_tax': '0.00',
                'cancellation_fee_total': '0.00',
                'prepayment': '20.00',
                'prepayment_tax': '0.00',
                'prepayment_total': '20.00',
                'external_partners': {
                    'apple_pay': False,
                    'google_pay': False,
                    'blik': False,
                },
                'force_stripe_pba': False,
            },
        )

    @override_settings(
        POS__APPLE_PAY=False,
        POS__GOOGLE_PAY=False,
        POS__BLIK=False,
    )
    def test_modify_appointment_no_external_partners(self):
        resp = self._test_modify_appointment_with_external_partners()
        assert resp.code == status.HTTP_200_OK
        assert resp.json['appointment']['status'] == Appointment.STATUS.MODIFIED
        dict_assert(
            resp.json['appointment_payment']['external_partners'],
            {
                'google_pay': False,
                'apple_pay': False,
                'blik': False,
            },
        )

    @override_settings(
        POS__APPLE_PAY=True,
        POS__GOOGLE_PAY=True,
        POS__BLIK=True,
    )
    def test_modify_appointment_with_external_partners(self):
        resp = self._test_modify_appointment_with_external_partners()
        assert resp.code == status.HTTP_200_OK
        assert resp.json['appointment']['status'] == Appointment.STATUS.MODIFIED
        dict_assert(
            resp.json['appointment_payment']['external_partners'],
            {
                'google_pay': False,
                'apple_pay': False,
                'blik': False,
            },
        )
