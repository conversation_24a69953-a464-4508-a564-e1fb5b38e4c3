import json
from datetime import (
    datetime,
    timedelta,
)

import pytest
from freezegun import freeze_time
from model_bakery import baker
from pytz import UTC

from lib.test_utils import (
    create_subbooking,
    increase_appointment_next_id,
)
from lib.tools import l_b, tznow
from service.booking.tests import get_before_and_after
from service.tests import BaseAsyncHTTPTest
from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
    AppointmentTypeSM as AT,
    SubbookingServiceVariantMode as SVMode,
)
from webapps.booking.models import Appointment
from webapps.booking.models import SubBooking
from webapps.business.models import Resource, Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.notification.enums import ScheduleState
from webapps.notification.models import NotificationSchedule
from webapps.notification.tasks import execute_task
from webapps.pop_up_notification.models import ShortReviewNotification
from webapps.user.models import User


@pytest.mark.django_db
class EditBookingTestCase(BaseAsyncHTTPTest):

    def setUp(self):
        increase_appointment_next_id(40)
        return super().setUp()

    @staticmethod
    def generate_url(booking, booking_type, business_id):
        id_ = booking.appointment_id if booking_type == AT.MULTI else booking.id
        url = f'/business_api/me/businesses/{business_id}' f'/appointments/{booking_type}/{id_}/'
        return url

    def run_test(
        self,
        status_before,
        status_after,
        from_before,
        from_after,
        booking_type=Appointment.TYPE.BUSINESS,
        notify=False,
        booked_for=None,
    ) -> dict:
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )

        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                updated_by=self.user,
                source=self.customer_booking_src,
                booked_from=from_before,
                booked_till=from_before + timedelta(hours=1),
                type=booking_type,
                status=status_before,
                autoassign=True,
                booked_for=booked_for,
            ),
        )

        booked_from = from_after
        booked_till = from_after + timedelta(hours=1)

        if booking.appointment.booked_for_id:
            customer_body = {
                'mode': ACMode.CUSTOMER_CARD,
                'id': booking.appointment.booked_for_id,
            }
        else:
            customer_body = {
                'mode': ACMode.WALK_IN,
            }

        body = {
            'customer': customer_body,
            'subbookings': [
                {
                    'booked_from': booked_from.isoformat(),
                    'booked_till': booked_till.isoformat(),
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.NO_VARIANT,
                        'service_name': 'a',
                    },
                    'type': booking_type,
                    'booked_for': booking.appointment.booked_for_id,
                }
            ],
            '_version': booking.appointment._version,  # pylint: disable=protected-access
            'dry_run': False,
            'overbooking': True,  # ignore working hours
            '_notify_about_reschedule': notify,
        }

        url = self.generate_url(booking, AT.MULTI, self.business.id)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body

        booking = SubBooking.objects.get()
        assert booking.booked_from == booked_from.replace(tzinfo=UTC)
        assert booking.booked_till == booked_till.replace(tzinfo=UTC)
        assert booking.appointment.status == status_after
        assert booking.appointment.booked_from == booking.booked_from
        assert booking.appointment.booked_till == booking.booked_till

        return resp.json

    def test_move_booking_from_future_to_future(self):
        self.run_test(
            Appointment.STATUS.ACCEPTED, Appointment.STATUS.ACCEPTED, *get_before_and_after()
        )

    def test_move_booking_from_future_to_past(self):
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            Appointment.STATUS.FINISHED,
            *get_before_and_after(after_future=False),
        )

    def test_move_proposed_customer_booking_from_future_to_past(self):
        from_before, from_after = get_before_and_after(after_future=False)
        self.run_test(
            Appointment.STATUS.PROPOSED,
            Appointment.STATUS.FINISHED,
            from_before,
            from_after,
            booking_type=Appointment.TYPE.CUSTOMER,
        )

    def test_move_proposed_customer_booking_from_future_to_past_notify(self):
        from_before, from_after = get_before_and_after(after_future=False)
        self.run_test(
            Appointment.STATUS.PROPOSED,
            Appointment.STATUS.FINISHED,
            from_before,
            from_after,
            booking_type=Appointment.TYPE.CUSTOMER,
            notify=True,
        )

    def test_move_unconfirmed_customer_booking_from_future_to_past(self):
        from_before, from_after = get_before_and_after(after_future=False)
        self.run_test(
            Appointment.STATUS.UNCONFIRMED,
            Appointment.STATUS.FINISHED,
            from_before,
            from_after,
            booking_type=Appointment.TYPE.CUSTOMER,
        )

    def test_move_proposed_customer_booking_from_future_to_future(self):
        from_before, from_after = get_before_and_after()
        self.run_test(
            Appointment.STATUS.PROPOSED,
            Appointment.STATUS.PROPOSED,
            from_before,
            from_after,
            booking_type=Appointment.TYPE.CUSTOMER,
        )

    def test_move_proposed_customer_booking_from_future_to_future_notify(self):
        from_before, from_after = get_before_and_after()
        self.run_test(
            Appointment.STATUS.PROPOSED,
            Appointment.STATUS.PROPOSED,
            from_before,
            from_after,
            booking_type=Appointment.TYPE.CUSTOMER,
            notify=True,
        )

    def test_move_accepted_customer_booking_from_future_to_future_notify(self):
        from_before, from_after = get_before_and_after()
        self.run_test(
            Appointment.STATUS.ACCEPTED,
            Appointment.STATUS.PROPOSED,
            from_before,
            from_after,
            booking_type=Appointment.TYPE.CUSTOMER,
            notify=True,
        )

    def test_move_proposed_customer_booking_from_past_to_future(self):
        from_before, from_after = get_before_and_after(before_future=False)
        self.run_test(
            Appointment.STATUS.FINISHED,
            Appointment.STATUS.ACCEPTED,
            from_before,
            from_after,
            booking_type=Appointment.TYPE.CUSTOMER,
        )

    def test_move_customer_booking_from_past_to_future_notify(self):
        from_before, from_after = get_before_and_after(before_future=False)
        self.run_test(
            Appointment.STATUS.FINISHED,
            Appointment.STATUS.ACCEPTED,
            from_before,
            from_after,
            booking_type=Appointment.TYPE.CUSTOMER,
            notify=True,
        )

    def test_move_booking_from_past_to_future(self):
        self.run_test(
            Appointment.STATUS.FINISHED,
            Appointment.STATUS.ACCEPTED,
            *get_before_and_after(before_future=False),
        )

    def test_move_booking_from_past_to_future_no_show(self):
        self.run_test(
            Appointment.STATUS.NOSHOW,
            Appointment.STATUS.ACCEPTED,
            *get_before_and_after(before_future=False),
        )

    def test_move_booking_from_past_to_past(self):
        self.run_test(
            Appointment.STATUS.FINISHED,
            Appointment.STATUS.FINISHED,
            *get_before_and_after(before_future=False, after_future=False),
        )

    def test_move_booking_from_past_to_past_no_show(self):
        self.run_test(
            Appointment.STATUS.NOSHOW,
            Appointment.STATUS.NOSHOW,
            *get_before_and_after(before_future=False, after_future=False),
        )

    def test_move_booking_from_past_to_future_cancelled(self):
        self.run_test(
            Appointment.STATUS.CANCELED,
            Appointment.STATUS.CANCELED,
            *get_before_and_after(before_future=False),
        )

    @freeze_time('2023-01-12T10:00')
    def test_status_edit_ongoing_no_show(self):
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )
        now = tznow()

        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                updated_by=self.user,
                source=self.customer_booking_src,
                booked_from=now - timedelta(minutes=5),
                booked_till=now + timedelta(minutes=5),
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.NOSHOW,
                autoassign=True,
            ),
        )

        body = {
            'customer': {'mode': ACMode.WALK_IN},
            'subbookings': [
                {
                    'booked_from': booking.booked_from.isoformat(),
                    'booked_till': booking.booked_till.isoformat(),
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.NO_VARIANT,
                        'service_name': 'b',
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            '_version': booking.appointment._version,  # pylint: disable=protected-access
            'dry_run': False,
            'overbooking': True,  # ignore working hours
            '_notify_about_reschedule': False,
        }

        url = self.generate_url(booking, AT.SINGLE, self.business.id)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200

        booking = SubBooking.objects.get()
        assert booking.appointment.status == Appointment.STATUS.NOSHOW

    @freeze_time('2023-01-12T10:00')
    def test_status_edit_ongoing_no_show_dry_run(self):
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )
        now = tznow()

        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                updated_by=self.user,
                source=self.customer_booking_src,
                booked_from=now - timedelta(minutes=5),
                booked_till=now + timedelta(minutes=5),
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.NOSHOW,
                autoassign=True,
            ),
        )

        body = {
            'customer': {'mode': ACMode.WALK_IN},
            'subbookings': [
                {
                    'booked_from': booking.booked_from.isoformat(),
                    'booked_till': booking.booked_till.isoformat(),
                    'appliance_id': None,
                    'staffer_id': staffer.id,
                    'service_variant': {
                        'mode': SVMode.NO_VARIANT,
                        'service_name': 'b',
                    },
                    'type': Appointment.TYPE.BUSINESS,
                }
            ],
            '_version': booking.appointment._version,  # pylint: disable=protected-access
            'dry_run': True,
            'overbooking': False,
            '_notify_about_reschedule': False,
        }

        url = self.generate_url(booking, AT.SINGLE, self.business.id)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200
        status = json.loads(l_b(resp.body))['appointment']['status']
        assert status == Appointment.STATUS.NOSHOW

    def test_move_from_future_to_past_customer_booking(self):
        self.business.status = Business.Status.TRIAL
        self.business.save()
        booking_type = Appointment.TYPE.CUSTOMER
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=baker.make(User),
        )

        self.run_test(
            Appointment.STATUS.ACCEPTED,
            Appointment.STATUS.FINISHED,
            booking_type=booking_type,
            booked_for=bci,
            *get_before_and_after(after_future=False),
        )
        assert NotificationSchedule.objects.filter(
            task_id__contains=f'BusinessFirstCustomerBookingFinished,{self.business.id}',
            state=ScheduleState.SUCCESS,
        ).exists()

    def test_move_from_future_to_past_customer_booking_30_minutes_ago(self):
        self.business.status = Business.Status.TRIAL
        self.business.save()
        booking_type = Appointment.TYPE.CUSTOMER
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=baker.make(User),
        )

        with freeze_time(datetime(2018, 1, 1, 12, tzinfo=UTC)):
            from_before, _ = get_before_and_after(after_future=False)
            last_response = self.run_test(
                Appointment.STATUS.ACCEPTED,
                Appointment.STATUS.FINISHED,
                booking_type=booking_type,
                booked_for=bci,
                *(from_before, tznow() - timedelta(minutes=65)),
            )
            assert NotificationSchedule.objects.filter(
                task_id__contains=f'BusinessFirstCustomerBookingFinished,{self.business.id}',
                state=ScheduleState.SUCCESS,
            ).exists()
            appointment_uid = last_response['appointment']['appointment_uid']
            after30minutes_schedule = NotificationSchedule.objects.filter(
                task_id=f"booking_finished:after30minutes:appointment_id={appointment_uid}",
            ).first()
            assert after30minutes_schedule
            execute_task(after30minutes_schedule)
            assert ShortReviewNotification.objects.filter(
                subbooking_id=last_response['appointment']['subbookings'][0]['id'],
            ).exists()


class EditBookingUrlUidTestCase(EditBookingTestCase):

    @staticmethod
    def generate_url(booking, booking_type, business_id):
        url = (
            f'/business_api/me/businesses/{business_id}' f'/appointments/{booking.appointment_id}/'
        )
        return url
