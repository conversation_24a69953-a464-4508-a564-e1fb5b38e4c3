import logging

import tornado.web
from django.conf import settings
from django.utils.translation import gettext as _
from elasticsearch_dsl import AttrDict, AttrList
from rest_framework import serializers

from lib.db import READ_ONLY_DB, on_operational_error, using_db_for_reads
from lib.elasticsearch.consts import ESDocType
from lib.feature_flag.feature.booking import SimplifiedBookingTargeting
from lib.feature_flag.feature.monetisation import EnablePeakHoursFlag
from lib.feature_flag.killswitch import ReadOnlyDBBusinessCustomerInfoFlag
from lib.fields.geo_location import GeoLocationSerializerField
from lib.serializers import CommaSeparatedListField
from service.search.mixins import BusinessESMixin
from service.tools import HTTPErrorWithCode, RequestHandler, session
from webapps.booking.my_booksy import BookingBox
from webapps.booking.v2.targeting.domain.service.targeting import (
    TargetingService as SimplifiedBookingTargetingService,
)
from webapps.boost.tools import get_promoted_labels
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.searchables.business import BusinessByIdSearchable
from webapps.business.searchables.serializers.business import (
    BusinessDetailsHitSerializer,
)
from webapps.business.service_promotions import (
    ServicePromotionListing,
)
from webapps.business.services.service_variants import VariantsAdapter
from webapps.business.tools import clear_markdown_from_service_description
from webapps.business_customer_info.serializers.serializers import (
    ShortAppointmentCustomerInfoSerializer,
)
from webapps.kill_switch.models import KillSwitch
from webapps.user.const import Gender
from webapps.user.models import GENDERS
from webapps.voucher.utils import VoucherMigrationConfig

log = logging.getLogger('booksy.es_debug')


class DetailsRequestSerializer(serializers.Serializer):
    business_id = serializers.IntegerField(
        required=True,
        max_value=2**31 - 1,  # max int value supported by ES
    )
    staffer_id = serializers.IntegerField(required=False)
    category_id = CommaSeparatedListField(required=False)
    treatment_id = serializers.IntegerField(required=False)
    include_venues = serializers.IntegerField(required=False)
    gender = serializers.ChoiceField(required=False, choices=GENDERS)
    geo_location = GeoLocationSerializerField(required=False)
    no_thumbs = serializers.BooleanField(required=False, default=False)
    with_combos = serializers.IntegerField(default=0)
    combos_with_addons = serializers.IntegerField(default=0)
    with_markdown = serializers.IntegerField(default=0)


# pylint: disable=too-many-ancestors
class BusinessDetailsHandler(RequestHandler, BusinessESMixin):
    """Return business details.

    swagger:
        summary:
            Return business details (and customer_info data for logged users)
        notes: |
            <b>Logged users get additional customer_info data</b>
        type: BusinessDetailsResults
        parameters:
            - name: business_id
              description: Business id
              type: integer
              paramType: path
              required: true
            - name: staffer_id
              description: Staffer id
              type: integer
              paramType: query
              required: false
            - name: category_id
              description: Business Category ID (filter for inspirations)
              type: integer
              paramType: query
              required: false
            - name: treatment_id
              description: Sort services by treatment
              type: integer
              paramType: query
              required: false
            - name: gender
              description: User gender
              paramType: query
              required: False
              type: string
              enum:
                - M
                - F
                - R
            - name: geo_location
              description:
                latitude,longitude of a geo point where we look for
                businesses. <br>
                Ex. "40.75,-73.99" <br>
              paramType: query
              required: False
              type: string
            - name: include_venues
              description: Deprecated. Will be ignored. Always true
              paramType: query
              required: False
              type: integer
              enum:
                - 0
                - 1
            - name: no_thumbs
              type: boolean
              required: False
              paramType: query
            - name: with_combos
              type: integer
              required: False
              paramType: query
              enum:
                - 0
                - 1
            - name: combos_with_addons
              type: integer
              required: False
              paramType: query
              enum:
                - 0
                - 1
            - name: with_markdown
              type: integer
              required: False
              paramType: query
              enum:
                - 0
                - 1
    """

    _simplified_booking_targeting_service = SimplifiedBookingTargetingService()

    @session(optional_login=True)
    def get(self, business_id=None):
        """Get business details"""

        data = self._prepare_get_arguments()
        data = {k: v for k, v in list(data.items()) if v != ''}
        data['business_id'] = business_id

        if not data.get('gender') and self.user and self.user.gender:
            data['gender'] = self.user.gender

        serializer = DetailsRequestSerializer(data=data)
        self.validate_serializer(serializer)
        data = serializer.validated_data
        staffer_id = data.get('staffer_id')

        ret = {}
        bookmarked_resources = []
        client_discount = None

        filter_params = None

        if (
            not KillSwitch.exist_and_alive(KillSwitch.System.SKIP_HIDDEN_ON_WEB)
            and self.is_customer_web
        ):
            filter_params = {'web_search': True}

        doc = self.get_business_details(business_id, filter_params)
        customer_info = self.format_customer_info(business_id, doc.is_b_listing)
        if customer_info:
            ret['customer_info'] = customer_info
            ret['booking_box'] = self.booking_box(business_id, staffer_id)
            bookmarked_resources = customer_info.pop('bookmarked_resources', [])
            client_discount = customer_info.get('discount', 0)

        ret['business'] = self.format_business(
            doc,
            staffer_id,
            bookmarked_resources,
            no_thumbs=data.get('no_thumbs', False),
            gender=data.get('gender', Gender.Both),
            treatment_id=data.get('treatment_id'),
            client_discount=client_discount,
            with_combos=data.get('with_combos', 0),
            combos_with_addons=data.get('combos_with_addons', 0),
            with_markdown=data.get('with_markdown', 0),
        )
        self.add_promoted_label(ret['business'])

        if VoucherMigrationConfig.is_strict() and not ret['business'].pos_pay_by_app_enabled:
            ret['business']['force_old_gc_flow'] = True

        ret['legal_footer_visible'] = (
            settings.API_COUNTRY in settings.COUNTRIES_WITH_LEGAL_FOOTER_ENABLED
        )
        self.finish_with_json(200, ret)

    @staticmethod
    # @lru_booksy_cache(timeout=20*24*60*60)
    def get_business_details(business_id, filter_params=None):
        """Get BusinessDetails dict. Throws tornado.web.HTTPError

        :param data: dict. With search params
                required keys:
                    business_id
                optional keys:
                    filter_params
        """
        serializer = BusinessDetailsHitSerializer(context={'no_thumbs': False})
        search_data = {'ids': [business_id]}
        if filter_params:
            search_data.update(filter_params)

        search = (
            BusinessByIdSearchable(
                ESDocType.BUSINESS,
                serializer=serializer,
            )
            .params(
                routing=business_id,
                size=1,
            )
            .search(search_data)
        )
        resp = search.execute()
        if resp.hits.total.value == 0:
            b_listing_id = BusinessDetailsHandler.get_b_listing_id(business_id, filter_params)
            BusinessDetailsHandler.quick_assert_404(
                not b_listing_id,
                reason={
                    'code': 'b_listing_redirect',
                    'description': str(b_listing_id),
                },
            )
            if Business.objects.filter(
                id=business_id,
                visible=False,
                active=True,
            ).exists():
                raise HTTPErrorWithCode(
                    410,
                    reason={
                        'code': 'not_public',
                        'description': _('Business is not publicly available.'),
                    },
                )
            raise tornado.web.HTTPError(404)

        try:
            business = resp[0]
        except IndexError:
            resp = search.execute()
            business = resp[0]

        return business

    @staticmethod
    def get_b_listing_id(business_id, filter_params=None):
        """
        Check if there is B Listing for given business_id source
        and return its id.
        """
        search_data = {'b_listing_source_id': business_id}
        if filter_params:
            search_data.update(filter_params)

        search = (
            BusinessByIdSearchable(
                ESDocType.BUSINESS,
                serializer=BusinessDetailsHitSerializer,
            )
            .search(search_data)
            .params(size=1)
        )
        resp = search.execute()
        if resp.hits.total.value > 0:
            return resp[0].id
        return None

    # pylint: disable=too-many-arguments, too-many-branches, too-many-positional-arguments
    def format_business(
        self,
        doc,
        staffer_id,
        bookmarked_resources,
        no_thumbs,
        gender,
        treatment_id=None,
        client_discount=None,
        with_combos=None,
        combos_with_addons=None,
        with_markdown=None,
    ):
        doc['top_services'] = doc['top_services'][gender]

        if not with_markdown:
            clear_markdown_from_service_description(doc.service_categories)

        if not with_combos:
            allowed_ids = set()
            for category in doc.service_categories:
                category['services'] = [
                    service
                    for service in category['services']
                    if not getattr(service, 'combo_type', None)
                ]
                allowed_ids.update(service['id'] for service in category.services)

            doc['top_services'] = [
                service for service in doc['top_services'] if service['id'] in allowed_ids
            ]

        if with_combos and not combos_with_addons:
            for category in doc.service_categories:
                for service in category.services:
                    if getattr(service, 'combo_type', None):
                        service.addons_available = False

        if no_thumbs:
            for image_type in doc['images']:
                if doc['images'][image_type] and isinstance(doc['images'][image_type], AttrList):
                    for image in doc['images'][image_type]:
                        del image['thumbnails']
        if staffer_id:
            doc.service_categories = self.filter_service_categories(
                int(staffer_id),
                doc.service_categories,
            )

        if client_discount:
            doc.service_categories = ServicePromotionListing.apply_client_discount(
                doc.service_categories,
                client_discount,
            )
            ServicePromotionListing.apply_client_discount_flat_services(
                doc.top_services,
                client_discount,
            )

        if not client_discount and EnablePeakHoursFlag():
            sv = VariantsAdapter(business_id=doc.id)
            if sv.has_active_peak_hours():
                doc.service_categories = sv.apply_peak_hours_services(doc.service_categories)

        if treatment_id:
            treatment_id = int(treatment_id)
            doc.treatment_services = [
                AttrDict({'category_name': category.name, **service.to_dict()})
                for category in doc.service_categories
                for service in category.services
                if service.treatment and service.treatment.id == treatment_id
            ]

        for staffer in doc.staff:
            staffer.bookmarked = staffer.id in bookmarked_resources

        if not settings.WAITLIST_ENABLED:
            doc['waitlist_disabled'] = True

        if SimplifiedBookingTargeting():
            provider_feature_checklist = (
                self._simplified_booking_targeting_service.provider_feature_checklist(doc['id'])
            )

            if provider_feature_checklist:
                doc['simplified_booking_feature_checklist'] = {
                    'forwarded': provider_feature_checklist.forwarded,
                    'physio': provider_feature_checklist.physio,
                    'network': provider_feature_checklist.network,
                    'umbrella_venue': provider_feature_checklist.umbrella_venue,
                    'has_combo_services': provider_feature_checklist.has_combo_services,
                    'has_mobile_services': provider_feature_checklist.has_mobile_services,
                    'has_online_services': provider_feature_checklist.has_online_services,
                    'has_addons': provider_feature_checklist.has_addons,
                    'has_payments': provider_feature_checklist.has_payments,
                    'has_questions': provider_feature_checklist.has_questions,
                    'has_custom_forms': provider_feature_checklist.has_custom_forms,
                }

        return doc

    @staticmethod
    def filter_service_categories(staffer_id, service_categories):
        for category in service_categories:
            for service in category.services:
                variants = []
                for variant in service.variants:
                    # fix for elasticsearch not updating staffer_id field on service variants:
                    # check if field exists, and if not, then we can assume that the service
                    # has the same staffers as the variants, because if the user updated variants
                    # then elastic would update in a bit as well
                    if hasattr(variant, 'staffer_id') and staffer_id in variant.staffer_id:
                        variants.append(variant)
                    elif not hasattr(variant, 'staffer_id') and staffer_id in service.staffer_id:
                        variants = service.variants
                        break
                service.variants = variants
            category.services = [
                service
                for service in category.services
                if service.variants and staffer_id in service.staffer_id
            ]

        return [category for category in service_categories if category.services]

    def _get_customer_info(self, business_id):
        return self.user.business_customer_infos.filter(business__id=business_id).first()

    def format_customer_info(self, business_id, is_b_listing=False):
        """If user is logged get bookmark, recurring and review data for biz."""
        if self.user is None:
            return
        if ReadOnlyDBBusinessCustomerInfoFlag():
            with using_db_for_reads(READ_ONLY_DB):
                customer_info = self._get_customer_info(business_id)
        else:
            customer_info = self._get_customer_info(business_id)

        if customer_info is None:
            if is_b_listing:
                # fake customer info
                customer_info = BusinessCustomerInfo(
                    business_id=business_id,
                    user=self.user,
                )
            else:
                return
        return ShortAppointmentCustomerInfoSerializer(
            customer_info,
            context={
                'business': customer_info.business,
            },
        ).data

    @on_operational_error(return_value=None)
    @using_db_for_reads(READ_ONLY_DB)
    def booking_box(self, business_id, staffer_id):
        assert self.user

        return BookingBox(
            self.user,
            business_id=business_id,
            staffer_id=staffer_id,
        ).get_serialized_appointment()

    @staticmethod
    def add_promoted_label(business):
        if getattr(business, 'promoted'):
            business.promoted_labels = get_promoted_labels()


class BusinessBasicDetailsHandler(BusinessDetailsHandler):  # pylint: disable=too-many-ancestors
    """
    swagger:
        summary:
            Return basic business details
        type: BusinessBasicDetailsResults
        parameters:
            - name: business_id
              description: Business id
              type: integer
              paramType: path
              required: true
    :swagger
    """

    @session(optional_login=True)
    def get(self, business_id=None):
        """Get business details"""
        if business_id is None or business_id == '':
            self.return_error(
                [{'code': 'required', 'field': 'id', 'description': 'id is required'}]
            )
            return

        doc = self.get_business_details(business_id)
        self.finish(
            {
                'business': {
                    'id': doc.id,
                    'name': doc.name,
                }
            }
        )
