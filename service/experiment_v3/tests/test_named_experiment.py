from unittest.mock import patch

import pytest

from lib.enums import StrEnum
from service.experiment_v3.named_experiment import NamedExperimentHandler
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.experiment_v3.exp.base import BaseExperiment
from webapps.experiment_v3.models import (
    ExperimentSlot,
    ExperimentVariant,
    Experiment,
)


# pylint: disable=abstract-method
class ExampleExperiment(BaseExperiment):
    class Variants(StrEnum):
        CONTROL = 'control'
        TEST_GROUP = 'test_group'

    name = 'example_name'
    config = {
        'variants': [
            {
                'name': Variants.CONTROL,
                'weight': 0.5,
            },
            {
                'name': Variants.TEST_GROUP,
                'weight': 0.5,
            },
        ]
    }


class BaseNamedExperimentTestCase(BaseAsyncHTTPTest):
    url = '/experiment/{}/experiment_variant/{}/'.format


@pytest.mark.django_db
class NamedExperimentHandlerTestCase(BaseNamedExperimentTestCase):
    example_extra_data = {
        'registration_email': '<EMAIL>',
    }

    def setUp(self):
        super().setUp()
        NamedExperimentHandler.valid_experiments[ExampleExperiment.name] = ExampleExperiment
        ExampleExperiment.initialize()

    def tearDown(self):
        super().tearDown()
        NamedExperimentHandler.valid_experiments.pop(ExampleExperiment.name)
        Experiment.objects.filter(name=ExampleExperiment.name).delete()

    def test_get_experiment_variant_valid_experiment_basic(self):
        relation_id_0 = 'f80d523b-5c8c-48cd-ba6a-b392e84009e8'
        resp_0 = self.fetch(self.url(ExampleExperiment.name, relation_id_0))
        assert resp_0.code == 200
        assert 'selected_variant' in resp_0.json
        assert resp_0.json['selected_variant'] in ExampleExperiment.Variants.values()

    def test_get_experiment_variant_valid_experiment_not_in_db(self):
        Experiment.objects.all().delete()
        resp = self.fetch(self.url(ExampleExperiment.name, 'abcdefg'))
        assert resp.code == 200
        assert resp.json == {
            'previously_joined': None,
            'selected_variant': None,
        }

    def test_update_experiment_valid_slot(self):
        relation_id = 'f80d523b-5c8c-48cd-ba6a-b392e84009e8'
        ExampleExperiment(relation_id).get_variant()

        resp_0 = self.fetch(
            self.url(ExampleExperiment.name, relation_id),
            method='PUT',
            body={
                'extra_data': self.example_extra_data,
            },
        )
        assert resp_0.code == 200
        slots = ExperimentSlot.objects.filter(
            relation_id=relation_id,
            variant__experiment__name=ExampleExperiment.name,
        )
        assert slots.count() == 1
        slot = slots.first()
        dict_assert(slot.extra_data, self.example_extra_data)

    def test_update_experiment_slot_invalid_relation_id(self):
        relation_id = 'f80d523b-5c8c-48cd-ba6a-b392e84009e8'

        resp_0 = self.fetch(
            self.url(ExampleExperiment.name, relation_id),
            method='PUT',
            body={
                'extra_data': self.example_extra_data,
            },
        )
        assert resp_0.code == 400
        assert not ExperimentSlot.objects.filter(
            relation_id=relation_id,
            variant__experiment__name=ExampleExperiment.name,
        ).exists()

    def test_update_experiment_slot_invalid_experiment_name(self):
        relation_id = 'f80d523b-5c8c-48cd-ba6a-b392e84009e8'
        ExampleExperiment(relation_id).get_variant()

        resp_0 = self.fetch(
            self.url('some_non_random_invalid_experiment_name', relation_id),
            method='PUT',
            body={
                'extra_data': self.example_extra_data,
            },
        )
        assert resp_0.code == 400
        assert not ExperimentSlot.objects.filter(
            relation_id=relation_id,
            variant__experiment__name='some_non_random_invalid_experiment_name',
        ).exists()

    def test_partial_update_extra_data(self):
        relation_id = 'f80d523b-5c8c-48cd-ba6a-b392e84009e8'
        foo_dict = dict(foo='foo')
        bar_dict = dict(bar='bar')
        foobar_dict = dict(foo='foo', bar='bar')

        ExampleExperiment(relation_id).get_variant()
        slots = ExperimentSlot.objects.filter(
            relation_id=relation_id, variant__experiment__name=ExampleExperiment.name
        )
        assert slots.count() == 1
        slots.update(extra_data=foo_dict)

        resp = self.fetch(
            self.url(ExampleExperiment.name, relation_id),
            method='PUT',
            args={'partial': 1},
            body={
                'extra_data': bar_dict,
            },
        )
        assert resp.code == 200

        slots = ExperimentSlot.objects.filter(
            relation_id=relation_id,
            variant__experiment__name=ExampleExperiment.name,
        )
        assert slots.count() == 1
        dict_assert(slots.first().extra_data, foobar_dict)

    def test_partial_update__duplicated_slot(self):
        control_variant = ExperimentVariant.objects.get(
            experiment__name=ExampleExperiment.name,
            name=ExampleExperiment.Variants.CONTROL,
        )
        test_variant = ExperimentVariant.objects.get(
            experiment__name=ExampleExperiment.name,
            name=ExampleExperiment.Variants.TEST_GROUP,
        )

        relation_id = '1'
        ExperimentSlot.objects.create(relation_id=relation_id, variant=control_variant)
        with patch.object(ExperimentSlot, 'validate_unique'):
            ExperimentSlot.objects.create(relation_id=relation_id, variant=test_variant)

        duplicates = ExperimentSlot.objects.filter(
            variant__experiment__name=ExampleExperiment.name,
            relation_id=relation_id,
        )
        assert duplicates.count() == 2

        extra_data = {'foo': 'bar'}
        resp = self.fetch(
            self.url(ExampleExperiment.name, relation_id),
            method='PUT',
            args={'partial': 1},
            body={'extra_data': extra_data},
        )
        assert resp.code == 200
        dict_assert(duplicates.first().extra_data, extra_data)


@pytest.mark.django_db
class NamedExperimentHandlerInvalidTestCase(BaseNamedExperimentTestCase):
    def test_get_experiment_variant_invalid_experiment(self):
        resp = self.fetch(self.url(ExampleExperiment.name, 'abcdefg'))
        assert resp.code == 400
