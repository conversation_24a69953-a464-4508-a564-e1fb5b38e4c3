from datetime import date, datetime

import pytest
from dateutil import tz
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.test import TestCase, override_settings
from freezegun import freeze_time
from mock import MagicMock, PropertyMock, patch
from model_bakery import baker
from pytz import UTC
from rest_framework import status

from country_config import Country
from lib.booksy_sms.utils import SMSCosts
from lib.feature_flag.feature import (
    MessageBlastGDPREnabledUSCAFlag,
    SmsBlastMarketingTimeAdjustFlag,
    SMSMarketingRequiresConsentFlag,
)
from lib.feature_flag.feature.notification import RemoveFRSuffixFromSMSBodyFlag
from lib.test_utils import get_business_notifications, spy_mock, user_recipe
from lib.tests.utils import override_feature_flag
from service.billing.tests import gen_func
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.billing.models import BillingCycle, BillingSubscription
from webapps.business.conftest import _get_clean_business_customer_index
from webapps.business.models import Business, BusinessPolicyAgreement
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business_customer_info.enums import BCIGroupName
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image
from webapps.marketing.models import Unsubscribed
from webapps.message_blast.channels import MessageBlastChannelType
from webapps.message_blast.consts import (
    BLAST_SHORT_BODY_LENGTH_LIMIT,
    BLAST_SMS_FR_SHORTCODE,
    BLAST_TITLE_LENGTH_LIMIT,
)
from webapps.message_blast.enums import (
    MESSAGE_BLAST_RECOMMENDED_LIST,
    MessageBlastDateType,
    MessageBlastGroupEnum,
    MessageBlastInternalNames,
    MessageBlastTemplateStatus,
    MessageBlastTimeType,
)
from webapps.message_blast.models import (
    BlockedPhoneNumber,
    CommonMessageBlastTemplate,
    MessageBlast,
    MessageBlastImage,
    MessageBlastTemplate,
    SMSBlastMarketingConsent,
)
from webapps.message_blast.notifications import MessageBlastNotification
from webapps.message_blast.schedules import MessageBlastSendingSchedules
from webapps.message_blast.tasks import (
    reset_to_default_message_blast_templates_task,
    send_message_blast_lazy,
)
from webapps.message_blast.tests.test_helpers import (
    check_all_recommended_ever_activated,
    create_templates,
    update_message_blast_groups,
)
from webapps.notification.base import Channel
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.notification.enums import BlastSendType
from webapps.notification.models import NotificationHistory, UserNotification
from webapps.profile_completeness.models import Reward
from webapps.profile_completeness.rewards import MessageBlastTemplateReward
from webapps.purchase.models import Subscription
from webapps.subdomain_grpc.client import SubdomainGRPC
from webapps.user.enums import AuthOriginEnum

baker.generators.add('lib.interval.fields.IntervalField', gen_func)


@pytest.mark.django_db
class MessageBlastTemplateListingHandlerTests(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        update_message_blast_groups()
        create_templates()
        self.url = f'/business_api/me/businesses/{self.business.id}/message_blast/'

    def test_get(self):
        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        groups = resp.json['groups']
        assert len(groups) == 4

        assert groups[0]['internal_name'] == MessageBlastGroupEnum.FIRST_IMPRESSION
        assert len(groups[0]['business_templates']) == 3
        assert (
            groups[0]['business_templates'][0]['internal_name']
            == MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )
        assert (
            groups[0]['business_templates'][1]['internal_name']
            == MessageBlastInternalNames.INFORM_ABOUT_OTHER_SERVICES
        )
        assert (
            groups[0]['business_templates'][2]['internal_name']
            == MessageBlastInternalNames.PROMOTE_GIFT_CARDS
        )

        assert groups[1]['internal_name'] == MessageBlastGroupEnum.REACTIVATE
        assert len(groups[1]['business_templates']) == 3
        assert (
            groups[1]['business_templates'][0]['internal_name']
            == MessageBlastInternalNames.INVITE_FOR_VISIT
        )
        assert (
            groups[1]['business_templates'][1]['internal_name']
            == MessageBlastInternalNames.REINVITE_FREE_SERVICE
        )
        assert (
            groups[1]['business_templates'][2]['internal_name']
            == MessageBlastInternalNames.REINVITE_DISCOUNT
        )

        assert groups[2]['internal_name'] == MessageBlastGroupEnum.SPECIAL_OCCASIONS
        assert len(groups[2]['business_templates']) == 2
        assert (
            groups[2]['business_templates'][0]['internal_name']
            == MessageBlastInternalNames.HAPPY_BIRTHDAY
        )
        assert (
            groups[2]['business_templates'][1]['link_to_group']
            == MessageBlastGroupEnum.MORE_OCCASIONS
        )

        assert groups[3]['internal_name'] == MessageBlastGroupEnum.YOUR_OWN_MESSAGES
        assert len(groups[3]['business_templates']) == 1
        assert (
            groups[3]['business_templates'][0]['link_to_group']
            == MessageBlastGroupEnum.ONE_TIME_MESSAGE
        )

        assert resp.json['ask_for_message_blast_activation'] is True

    def test_get_with_flat_flag(self):
        resp = self.fetch(self.url, method='GET', args={'flat': True})

        assert resp.code == status.HTTP_200_OK

        groups = resp.json['groups']

        assert len(groups) == 4

        assert groups[0]['internal_name'] == MessageBlastGroupEnum.FIRST_IMPRESSION
        assert groups[0]['sent_messages'] == 0
        assert len(groups[0]['business_templates']) == 3
        first_impression_group = [
            MessageBlastInternalNames.WELCOME_NEW_CLIENT,
            MessageBlastInternalNames.INFORM_ABOUT_OTHER_SERVICES,
            MessageBlastInternalNames.PROMOTE_GIFT_CARDS,
        ]
        for number, internal_name in enumerate(first_impression_group):
            assert groups[0]['business_templates'][number]['internal_name'] == internal_name
            assert groups[0]['business_templates'][number]['recommended'] is True

        assert groups[1]['internal_name'] == MessageBlastGroupEnum.REACTIVATE
        assert len(groups[1]['business_templates']) == 3
        assert groups[1]['sent_messages'] == 0
        reactivate_group = [
            MessageBlastInternalNames.INVITE_FOR_VISIT,
            MessageBlastInternalNames.REINVITE_FREE_SERVICE,
            MessageBlastInternalNames.REINVITE_DISCOUNT,
        ]
        for number, internal_name in enumerate(reactivate_group):
            assert groups[1]['business_templates'][number]['internal_name'] == internal_name
        assert groups[1]['business_templates'][0]['recommended'] is True

        assert groups[2]['internal_name'] == MessageBlastGroupEnum.SPECIAL_OCCASIONS
        assert groups[2]['sent_messages'] is None
        assert len(groups[2]['business_templates']) == 16
        special_occasions_group = [
            MessageBlastInternalNames.HAPPY_BIRTHDAY,
            MessageBlastInternalNames.HOLIDAY_BOOKING,
            MessageBlastInternalNames.SPRING,
            MessageBlastInternalNames.FATHERS_DAY,
            MessageBlastInternalNames.MOTHERS_DAY,
            MessageBlastInternalNames.HALLOWEEN,
            MessageBlastInternalNames.NEW_YEAR,
            MessageBlastInternalNames.HOLDAY_GIFTCARD,
            MessageBlastInternalNames.VALENTINES_DAY,
            MessageBlastInternalNames.BACK_TO_SCHOOL,
            MessageBlastInternalNames.INDEPENDENCE_DAY,
            MessageBlastInternalNames.MEMORIAL_DAY,
            MessageBlastInternalNames.LABOR_DAY,
            MessageBlastInternalNames.MLK_DAY,
            MessageBlastInternalNames.VETERANS_DAY,
            MessageBlastInternalNames.THANKSGIVING_DAY,
        ]
        for number, internal_name in enumerate(special_occasions_group):
            assert groups[2]['business_templates'][number]['internal_name'] == internal_name

        your_own_messages_group = [
            MessageBlastInternalNames.INFORM_ABOUT_NEW_OPENING,
            MessageBlastInternalNames.INFORM_ABOUT_TIME_OFF,
            MessageBlastInternalNames.INFORM_ABOUT_NEW_SERVICES,
            MessageBlastInternalNames.INVITE_TO_EVENT,
            MessageBlastInternalNames.ASK_FOR_REFERRAL,
            MessageBlastInternalNames.INFORM_ABOUT_PRICE_CHANGES,
        ]
        assert groups[3]['internal_name'] == MessageBlastGroupEnum.YOUR_OWN_MESSAGES
        assert groups[3]['sent_messages'] is None
        assert len(groups[3]['business_templates']) == 6
        for number, internal_name in enumerate(your_own_messages_group):
            assert groups[3]['business_templates'][number]['internal_name'] == internal_name

        assert resp.json['ask_for_message_blast_activation'] is True
        assert resp.json['all_recommended_activated'] is False
        assert resp.json['own_message']['internal_name'] == MessageBlastInternalNames.OWN_MESSAGE

    def test_get_booksy_reward(self):
        Reward.objects.create(
            business=self.business,
            reward_internal_name=MessageBlastTemplateReward.reward_internal_name,
        )
        resp = self.fetch(self.url, method='GET')

        assert resp.code == status.HTTP_200_OK

        groups = resp.json['groups']
        assert len(groups) == 4

        assert groups[0]['internal_name'] == MessageBlastGroupEnum.FIRST_IMPRESSION

        assert groups[1]['internal_name'] == MessageBlastGroupEnum.REACTIVATE

        assert groups[2]['internal_name'] == MessageBlastGroupEnum.SPECIAL_OCCASIONS

        assert len(groups[3]['business_templates']) == 2
        assert (
            groups[3]['business_templates'][0]['link_to_group']
            == MessageBlastGroupEnum.ONE_TIME_MESSAGE
        )
        assert (
            groups[3]['business_templates'][1]['link_to_group']
            == MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST
        )

    def test_get_booksy_reward_with_flat_flag(self):
        Reward.objects.create(
            business=self.business,
            reward_internal_name=MessageBlastTemplateReward.reward_internal_name,
        )
        resp = self.fetch(self.url, method='GET', args={'flat': True})

        assert resp.code == status.HTTP_200_OK

        groups = resp.json['groups']

        assert len(groups) == 4
        your_own_messages_group = [
            MessageBlastInternalNames.INFORM_ABOUT_NEW_OPENING,
            MessageBlastInternalNames.INFORM_ABOUT_TIME_OFF,
            MessageBlastInternalNames.INFORM_ABOUT_NEW_SERVICES,
            MessageBlastInternalNames.INVITE_TO_EVENT,
            MessageBlastInternalNames.ASK_FOR_REFERRAL,
            MessageBlastInternalNames.INFORM_ABOUT_PRICE_CHANGES,
            MessageBlastInternalNames.GIFT_CARD_BALANCES,
            MessageBlastInternalNames.ASK_FOR_REVIEWS,
            MessageBlastInternalNames.APPRECIATE_CUSTOMER,
            MessageBlastInternalNames.UPDATED_BOOKS,
            MessageBlastInternalNames.WHERE_YOU_CAN_BOOK,
        ]
        assert groups[3]['internal_name'] == MessageBlastGroupEnum.YOUR_OWN_MESSAGES
        assert groups[3]['sent_messages'] is None
        assert len(groups[3]['business_templates']) == 11
        for number, internal_name in enumerate(your_own_messages_group):
            assert groups[3]['business_templates'][number]['internal_name'] == internal_name


@pytest.mark.django_db
class MessageBlastTemplateGroupHandlerTests(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        update_message_blast_groups()
        create_templates()

    def test_get_first_impression(self):
        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/group/{MessageBlastGroupEnum.FIRST_IMPRESSION}'
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK

        group = resp.json['group']
        assert group['internal_name'] == MessageBlastGroupEnum.FIRST_IMPRESSION
        assert (
            group['business_templates'][0]['internal_name']
            == MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )
        assert (
            group['business_templates'][1]['internal_name']
            == MessageBlastInternalNames.INFORM_ABOUT_OTHER_SERVICES
        )
        assert (
            group['business_templates'][2]['internal_name']
            == MessageBlastInternalNames.PROMOTE_GIFT_CARDS
        )

    def test_get_reactivate(self):
        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/group/{MessageBlastGroupEnum.REACTIVATE}'
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK

        group = resp.json['group']
        assert group['internal_name'] == MessageBlastGroupEnum.REACTIVATE
        assert (
            group['business_templates'][0]['internal_name']
            == MessageBlastInternalNames.INVITE_FOR_VISIT
        )
        assert (
            group['business_templates'][1]['internal_name']
            == MessageBlastInternalNames.REINVITE_FREE_SERVICE
        )
        assert (
            group['business_templates'][2]['internal_name']
            == MessageBlastInternalNames.REINVITE_DISCOUNT
        )

    def test_get_special_occasions(self):
        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/group/{MessageBlastGroupEnum.SPECIAL_OCCASIONS}'
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK

        group = resp.json['group']
        assert group['internal_name'] == MessageBlastGroupEnum.SPECIAL_OCCASIONS
        assert (
            group['business_templates'][0]['internal_name']
            == MessageBlastInternalNames.HAPPY_BIRTHDAY
        )
        assert (
            group['business_templates'][1]['link_to_group'] == MessageBlastGroupEnum.MORE_OCCASIONS
        )

    def test_get_booksy_reward(self):
        Reward.objects.create(
            business=self.business,
            reward_internal_name=MessageBlastTemplateReward.reward_internal_name,
        )
        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/group/{MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST}'
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK

        group = resp.json['group']
        assert group['internal_name'] == MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST
        assert (
            group['business_templates'][0]['internal_name']
            == MessageBlastInternalNames.GIFT_CARD_BALANCES
        )
        assert (
            group['business_templates'][1]['internal_name']
            == MessageBlastInternalNames.ASK_FOR_REVIEWS
        )
        assert (
            group['business_templates'][2]['internal_name']
            == MessageBlastInternalNames.APPRECIATE_CUSTOMER
        )
        assert (
            group['business_templates'][3]['internal_name']
            == MessageBlastInternalNames.UPDATED_BOOKS
        )
        assert (
            group['business_templates'][4]['internal_name']
            == MessageBlastInternalNames.WHERE_YOU_CAN_BOOK
        )

    def test_get_more_occasions(self):
        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/group/{MessageBlastGroupEnum.MORE_OCCASIONS}'
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK

        group = resp.json['group']
        assert group['internal_name'] == MessageBlastGroupEnum.MORE_OCCASIONS
        assert (
            group['business_templates'][0]['internal_name']
            == MessageBlastInternalNames.HOLIDAY_BOOKING
        )
        assert group['business_templates'][1]['internal_name'] == MessageBlastInternalNames.SPRING
        assert (
            group['business_templates'][2]['internal_name'] == MessageBlastInternalNames.FATHERS_DAY
        )
        assert (
            group['business_templates'][3]['internal_name'] == MessageBlastInternalNames.MOTHERS_DAY
        )
        assert (
            group['business_templates'][4]['internal_name'] == MessageBlastInternalNames.HALLOWEEN
        )
        assert group['business_templates'][5]['internal_name'] == MessageBlastInternalNames.NEW_YEAR
        assert (
            group['business_templates'][6]['internal_name']
            == MessageBlastInternalNames.HOLDAY_GIFTCARD
        )
        assert (
            group['business_templates'][7]['internal_name']
            == MessageBlastInternalNames.VALENTINES_DAY
        )
        assert (
            group['business_templates'][8]['internal_name']
            == MessageBlastInternalNames.BACK_TO_SCHOOL
        )

    def test_get_own_message(self):
        self.url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/group/{MessageBlastGroupEnum.YOUR_OWN_MESSAGES}'
        )
        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK

        group = resp.json['group']
        assert group['internal_name'] == MessageBlastGroupEnum.YOUR_OWN_MESSAGES
        assert (
            group['business_templates'][0]['link_to_group']
            == MessageBlastGroupEnum.ONE_TIME_MESSAGE
        )


@pytest.mark.django_db
class MessageBlastTemplateHandlerTests(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        update_message_blast_groups()
        create_templates()

    def test_get_by_id(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )

        message_blast_template = baker.make(
            MessageBlastTemplate,
            common_template=common_template,
            group=common_template.group,
            internal_name=common_template.internal_name,
            business=self.business,
            body_short='test body short',
        )

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/{common_template.id}/'
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_404_NOT_FOUND

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/{message_blast_template.id}/'
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK

        assert (
            resp.json['template']['internal_name'] == MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )
        assert resp.json['template']['channel_priority'] == {
            'default': True,
            'sms': False,
            'prefer_push': False,
            'push': False,
            'email': False,
        }
        assert resp.json['template']['body_short'] == 'test body short'

        # MB 3.1 reset to common test
        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/{common_template.id}/?parent=true'
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_404_NOT_FOUND

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/{message_blast_template.id}/?parent=true'
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK

        assert resp.json['template']['body_short'] != 'test body short'
        assert 'We hope you loved your experience at' in resp.json['template']['body_short']

    def test_get_by_name(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )
        baker.make(
            CommonMessageBlastTemplate,
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT,
            body='test body',
            body_short='test body short',
            group=common_template.group,
        )
        message_blast_template = baker.make(
            MessageBlastTemplate,
            common_template=common_template,
            group=common_template.group,
            internal_name=common_template.internal_name,
            business=self.business,
            body_short='test body short',
            body='test body',
        )

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/{common_template.id}/'
        resp = self.fetch(url, method='GET')
        self.assertEqual(resp.code, status.HTTP_404_NOT_FOUND)

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/{message_blast_template.internal_name}/'
        )
        resp = self.fetch(url, method='GET')

        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertEqual(
            resp.json['template']['internal_name'], MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )
        self.assertEqual(resp.json['template']['id'], message_blast_template.id)
        self.assertEqual(resp.json['template']['body'], message_blast_template.body)

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/test_no_internal_name/'
        )
        resp = self.fetch(url, method='GET')
        self.assertEqual(resp.code, status.HTTP_404_NOT_FOUND)

    def test_get_template_after_image_was_soft_deleted(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )
        image_to_delete = baker.make(Image, business=self.business)

        msg_blast_template = baker.make(
            MessageBlastTemplate,
            common_template=common_template,
            group=common_template.group,
            internal_name=common_template.internal_name,
            image=image_to_delete,
            business=self.business,
            body_short='test body short',
        )
        url = (
            f'/business_api/me/businesses/{self.business.id}/message_blast/{msg_blast_template.id}/'
        )
        resp = self.fetch(url, method='GET')
        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertIsNotNone(resp.json['template']['image'])

        image_to_delete.soft_delete()

        url = (
            f'/business_api/me/businesses/{self.business.id}/message_blast/{msg_blast_template.id}/'
        )
        resp = self.fetch(url, method='GET')
        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertIsNone(resp.json['template']['image'])

    def test_get_template_after_image_was_deleted(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )
        image_to_delete = baker.make(Image, business=self.business)

        msg_blast_template = baker.make(
            MessageBlastTemplate,
            common_template=common_template,
            group=common_template.group,
            internal_name=common_template.internal_name,
            image=image_to_delete,
            business=self.business,
            body_short='test body short',
        )
        url = (
            f'/business_api/me/businesses/{self.business.id}/message_blast/{msg_blast_template.id}/'
        )
        resp = self.fetch(url, method='GET')
        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertIsNotNone(resp.json['template']['image'])

        image_to_delete.delete()

        url = (
            f'/business_api/me/businesses/{self.business.id}/message_blast/{msg_blast_template.id}/'
        )
        resp = self.fetch(url, method='GET')
        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertIsNone(resp.json['template']['image'])

    def test_get_one_time_with_schedule(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.HAPPY_BIRTHDAY
        )

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/{common_template.id}/'
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK

        assert resp.json['template']['internal_name'] == MessageBlastInternalNames.HAPPY_BIRTHDAY
        schedule_numbers = MessageBlastSendingSchedules.get(
            MessageBlastInternalNames.HAPPY_BIRTHDAY
        ).get_values()
        schedules = resp.json['template']['schedules']
        schedule_result = [schedule['number'] for schedule in schedules]
        assert schedule_result == schedule_numbers

    @freeze_time(datetime(2021, 10, 20, 10, 15, tzinfo=UTC))
    def test_get_one_time_with_dates(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.HOLIDAY_BOOKING
        )

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/{common_template.id}/'
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK

        assert resp.json['template']['internal_name'] == MessageBlastInternalNames.HOLIDAY_BOOKING
        assert resp.json['template']['date_month'] == 11
        assert resp.json['template']['date_day'] == 15
        assert resp.json['template']['date_hour'] == MessageBlastTimeType.MORNING.value
        assert resp.json['template']['date_text'] == "Nov 15, 2021"
        assert resp.json['template']['date_text_recommended'] == (
            "Nov 15, 2021 (recommended - Second Monday in November)"
        )

    @freeze_time(datetime(2021, 11, 15, tzinfo=UTC))
    def test_get_one_time_with_dates_next_year(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.HOLIDAY_BOOKING
        )

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/{common_template.id}/'
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK

        assert resp.json['template']['internal_name'] == MessageBlastInternalNames.HOLIDAY_BOOKING
        assert resp.json['template']['date_month'] == 11
        assert resp.json['template']['date_day'] == 14
        assert resp.json['template']['date_hour'] == MessageBlastTimeType.MORNING.value
        assert resp.json['template']['date_text'] == "Nov 14, 2022"
        assert resp.json['template']['date_text_recommended'] == (
            "Nov 14, 2022 (recommended - Second Monday in November)"
        )

    def test_get_stop_code(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )

        with override_settings(API_COUNTRY=Country.FR):
            url = (
                f'/business_api/me/businesses/{self.business.id}'
                f'/message_blast/{common_template.id}/'
            )
            resp = self.fetch(url, method='GET')
            assert resp.code == status.HTTP_200_OK
            assert resp.json['stop_code'] == BLAST_SMS_FR_SHORTCODE

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/{common_template.id}/'
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['stop_code'] is None

    @override_feature_flag({RemoveFRSuffixFromSMSBodyFlag.flag_name: True})
    def test_remove_stop_code_in_fr(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )

        with override_settings(API_COUNTRY=Country.FR):
            url = (
                f'/business_api/me/businesses/{self.business.id}'
                f'/message_blast/{common_template.id}/'
            )
            resp = self.fetch(url, method='GET')
            assert resp.code == status.HTTP_200_OK
            assert resp.json['stop_code'] is None

    def test_put_common_template(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/{common_template.id}/'

        channel_priority = {
            'default': False,
            'push': False,
            'prefer_push': False,
            'sms': False,
            'email': True,
        }

        body = {
            'title': 'Test',
            'body': 'Test2',
            'body_short': 'Test3',
            'image': None,
            'automated_status': MessageBlastTemplateStatus.ACTIVE,
            'channel_priority': channel_priority,
        }

        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == status.HTTP_200_OK

        message_blast_template = MessageBlastTemplate.objects.get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT, business=self.business
        )

        assert message_blast_template.title == body['title']
        assert message_blast_template.body == body['body']
        assert message_blast_template.body_short == body['body_short']
        assert message_blast_template.image == body['image']
        assert message_blast_template.channel_priority == channel_priority
        assert resp.json['template']['date_hour'] == MessageBlastTimeType.MORNING.value

        # Try to override common_template again
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == status.HTTP_404_NOT_FOUND

    def test_put_send_and_reset_to_default(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.HOLIDAY_BOOKING
        )

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/{common_template.id}/'

        body = {
            'title': 'Test',
            'body': 'Test2',
            'body_short': 'Test3',
            'image': None,
            'automated_status': MessageBlastTemplateStatus.ACTIVE,
            'date_month': 12,
            'date_day': 1,
            'date_hour': MessageBlastTimeType.MIDDAY,
        }

        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == status.HTTP_200_OK

        message_blast_template = MessageBlastTemplate.objects.get(
            internal_name=MessageBlastInternalNames.HOLIDAY_BOOKING, business=self.business
        )
        assert message_blast_template.date_type == MessageBlastDateType.STRICT

        with freeze_time(datetime(2021, 12, 1, 16, tzinfo=UTC)):
            message_blast_template.create_blast(bcis=[bci.id])

        message_blast = MessageBlast.objects.filter(
            business=self.business,
            internal_name=MessageBlastInternalNames.HOLIDAY_BOOKING,
        ).first()
        assert message_blast.body_short == 'Test3'
        message_blast.sent = False
        message_blast.save()

        with freeze_time(datetime(2021, 11, 30, 16, tzinfo=UTC)):
            # start task
            results = reset_to_default_message_blast_templates_task()
            assert 'holiday_booking' not in results['templates_reset']

        message_blast.sent = True
        message_blast.save()

        with freeze_time(datetime(2021, 12, 2, 10, tzinfo=UTC)):
            # start task
            results = reset_to_default_message_blast_templates_task()
            assert results['templates_reset']['holiday_booking'] == 1

            message_blast_template.refresh_from_db()

            assert (
                message_blast_template.date_type
                == common_template.date_type
                == MessageBlastDateType.FIRST_MONDAY
            )
            assert message_blast_template.date_month == common_template.date_month == 11
            assert message_blast_template.date_monday == common_template.date_monday == 2

    def test_put_common_template_one_time(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.INFORM_ABOUT_NEW_OPENING,
        )

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/{common_template.id}/'

        body = {
            'title': 'Test',
            'body': 'Test2',
            'body_short': 'Test3',
            'image': None,
            'automated_status': MessageBlastTemplateStatus.ACTIVE,  # not allowed
            'date_day': None,
            'date_month': None,
        }

        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'one_time_status'

        body['automated_status'] = MessageBlastTemplateStatus.INACTIVE  # allowed option

        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == status.HTTP_200_OK

        message_blast_template = MessageBlastTemplate.objects.get(
            internal_name=MessageBlastInternalNames.INFORM_ABOUT_NEW_OPENING, business=self.business
        )

        assert message_blast_template.title == body['title']
        assert message_blast_template.body == body['body']
        assert message_blast_template.body_short == body['body_short']
        assert message_blast_template.image == body['image']
        assert resp.json['template']['date_hour'] == MessageBlastTimeType.MORNING.value

        # Try to override common_template again
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == status.HTTP_404_NOT_FOUND

    def test_put_common_template_with_date_schedule_hour(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/{common_template.id}/'

        body = {
            'title': 'Test',
            'body': 'Test2',
            'body_short': 'Test3',
            'image': None,
            'automated_status': MessageBlastTemplateStatus.ACTIVE,
            'date_schedule': 24,
            'date_hour': MessageBlastTimeType.MIDDAY,
        }

        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == status.HTTP_200_OK

        message_blast_template = MessageBlastTemplate.objects.get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT, business=self.business
        )

        assert message_blast_template.title == body['title']
        assert message_blast_template.body == body['body']
        assert message_blast_template.body_short == body['body_short']
        assert message_blast_template.image == body['image']
        assert message_blast_template.date_schedule == 24
        assert message_blast_template.date_hour == MessageBlastTimeType.MIDDAY.value

    @override_settings(SAVE_HISTORY=True)
    def test_put_message_blast_template_with_history(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )

        message_blast_template = baker.make(
            MessageBlastTemplate,
            common_template=common_template,
            group=common_template.group,
            internal_name=common_template.internal_name,
            business=self.business,
        )

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/{message_blast_template.id}/'
        )
        channel_priority = {
            'default': False,
            'push': False,
            'prefer_push': False,
            'sms': False,
            'email': True,
        }
        body = {
            'title': 'Test',
            'body': 'Test2',
            'body_short': 'Test3',
            'image': None,
            'automated_status': MessageBlastTemplateStatus.ACTIVE,
            'channel_priority': channel_priority,
        }

        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == status.HTTP_200_OK

        template = MessageBlastTemplate.objects.get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT, business=self.business
        )

        assert template.title == body['title']
        assert template.body == body['body']
        assert template.body_short == body['body_short']
        assert template.image == body['image']
        assert template.channel_priority == body['channel_priority']

        assert template.history.all().count() == 2

    def test_put_message_blast_template_one_time(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.INFORM_ABOUT_NEW_OPENING
        )

        message_blast_template = baker.make(
            MessageBlastTemplate,
            common_template=common_template,
            group=common_template.group,
            internal_name=common_template.internal_name,
            business=self.business,
        )

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/{message_blast_template.id}/'
        )

        body = {
            'title': 'Test',
            'body': 'Test2',
            'body_short': 'Test3',
            'image': None,
            'automated_status': MessageBlastTemplateStatus.INACTIVE,
        }

        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == status.HTTP_200_OK

        template = MessageBlastTemplate.objects.get(
            internal_name=MessageBlastInternalNames.INFORM_ABOUT_NEW_OPENING, business=self.business
        )

        assert template.title == body['title']
        assert template.body == body['body']
        assert template.body_short == body['body_short']
        assert template.image == body['image']


@pytest.mark.django_db
class OneTimeMessageBlastHandlerTests(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        update_message_blast_groups()
        create_templates()

        self.business.sms_notification_status = Business.SMSStatus.ENABLED
        self.business.save()

    url = '/business_api/me/businesses/{}/message_blast/{}/one_time_send/'.format

    @staticmethod
    def get_data(**kwargs):
        return {
            'title': 'bb',
            'body': 'ccc',
            'body_short': 'ddd',
            'image': None,
            'recipients': {'group': BCIGroupName.ALL_CUSTOMERS},
            **kwargs,
        }

    @staticmethod
    def get_common_template() -> CommonMessageBlastTemplate:
        return (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )

    def test_post_common_not_one_time(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )

        url = self.url(self.business.id, common_template.id)

        resp = self.fetch(url, method='POST', body=self.get_data())
        assert resp.code == status.HTTP_404_NOT_FOUND

    def test_post_not_one_time(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )
        message_blast_template = baker.make(
            MessageBlastTemplate,
            common_template=common_template,
            group=common_template.group,
            internal_name=common_template.internal_name,
            business=self.business,
        )

        url = self.url(self.business.id, message_blast_template.id)

        resp = self.fetch(url, method='POST', body=self.get_data())
        assert resp.code == status.HTTP_404_NOT_FOUND

    def test_post_common(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        bci.reindex(refresh_index=True)

        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )

        url = self.url(self.business.id, common_template.id)

        data = self.get_data()
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_201_CREATED

        # Check if nothing changed
        common_template_2 = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )

        assert common_template.title == common_template_2.title
        assert common_template.body == common_template_2.body
        assert common_template.body_short == common_template_2.body_short

        message_blast_id = resp.json['message_blast_id']
        message_blast = MessageBlast.objects.get(id=message_blast_id)
        assert message_blast.body == data['body']
        assert message_blast.body_short == data['body_short']
        assert message_blast.title == data['title']
        assert message_blast.name == common_template.name
        assert message_blast.image == data['image']
        assert bci.id in message_blast.bcis

    def test_post_sms(self):
        subdomain = 'barber007'
        SubdomainGRPC.claim(
            data=dict(
                business_id=self.business.id,
                country_code=settings.API_COUNTRY,
                subdomain=subdomain,
            )
        )

        data = self.get_data(
            **{
                'body_short': f'blah blah blah {self.business.get_seo_url()}',
            }
        )
        self._common_sms_blast_test_part(data, subdomain)

    def test_post_sms_with_blast_subdomain_added_manually(self):
        SubdomainGRPC.claim(
            data=dict(
                business_id=self.business.id,
                country_code=settings.API_COUNTRY,
                subdomain='barber112',
            )
        )

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='***********',
        )
        bci.reindex(refresh_index=True)

        blast_subdomain = self.business.get_seo_url(
            channel=Channel.Type.SMS,
            is_blast=True,
        )
        data = self.get_data(
            **{
                'body_short': f'blah blah blah {blast_subdomain}',
            }
        )
        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )
        resp = self.fetch(
            path=self.url(self.business.id, common_template.id),
            method='POST',
            body=data,
        )
        # self._common_sms_blast_test_part(data)
        message_blast = MessageBlast.objects.get(id=resp.json['message_blast_id'])
        notification_history = NotificationHistory.objects.filter(
            task_id=f'MessageBlastNotification, {message_blast.id}',
        ).first()
        assert '/b/b/' not in notification_history.title
        # no subdomain-domain because only .booksy.com is allowed:
        assert notification_history.title == 'blah blah blah :8888/b/\nReply STOP to opt-out.'

    def _common_sms_blast_test_part(self, data, _):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='***********',
        )
        bci.reindex(refresh_index=True)
        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )
        spy__blast_sms_content = spy_mock(MessageBlastNotification.get_sms_content)
        with patch(
            'webapps.message_blast.notifications.MessageBlastNotification.get_sms_content',
            spy__blast_sms_content,
        ):
            resp = self.fetch(
                path=self.url(self.business.id, common_template.id),
                method='POST',
                body=data,
            )
        assert resp.code == status.HTTP_201_CREATED
        # no subdomain-domain because only .booksy.com is allowed:
        assert spy__blast_sms_content.mock.result[0] == 'blah blah blah :8888'

        message_blast = MessageBlast.objects.get(id=resp.json['message_blast_id'])
        assert message_blast.body == data['body']
        assert message_blast.body_short == 'blah blah blah :8888'
        assert message_blast.title == data['title']
        assert message_blast.image == data['image']
        assert bci.id in message_blast.bcis

        notification_history = NotificationHistory.objects.filter(
            task_id=f'MessageBlastNotification, {message_blast.id}',
        ).first()
        self.business.get_seo_url(
            channel=Channel.Type.SMS,
            is_blast=True,
        )
        # no subdomain-domain because only .booksy.com is allowed:
        assert notification_history.title == 'blah blah blah :8888\nReply STOP to opt-out.'
        assert not get_business_notifications(self.business.id)

    def test_post_common_with_sms_notification_validation(self):
        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )

        url = self.url(self.business.id, common_template.id)
        data = {
            'title': 'bb',
            'body': 'ccc',
            'body_short': 'ddd',
            'recipients': {'group': BCIGroupName.ALL_CUSTOMERS},
        }

        self.business.sms_notification_status = Business.SMSStatus.DISABLED
        self.business.save()
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_400_BAD_REQUEST

        self.business.sms_notification_status = Business.SMSStatus.PENDING
        self.business.save()
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_400_BAD_REQUEST

        self.business.sms_notification_status = Business.SMSStatus.ENABLED
        self.business.save()
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_201_CREATED

    def test_name_title_validation(self):
        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )

        url = self.url(self.business.id, common_template.id)
        data = {
            'title': 'b' * (BLAST_TITLE_LENGTH_LIMIT + 1),
            'body': 'ccc',
            'body_short': 'ddd',
            'recipients': {'group': BCIGroupName.ALL_CUSTOMERS},
        }

        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_400_BAD_REQUEST

        data = {
            'title': 'bbb',
            'body': 'cccc',
            'body_short': 'd' * (BLAST_SHORT_BODY_LENGTH_LIMIT + 1),
            'recipients': {'group': BCIGroupName.ALL_CUSTOMERS},
        }
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_400_BAD_REQUEST

        data = {
            'title': 'bbb',
            'body': 'c' * 500,
            'body_short': 'ddd',
            'recipients': {'group': BCIGroupName.ALL_CUSTOMERS},
        }
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_201_CREATED

    def test_post_common_with_custom_image(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        bci.reindex(refresh_index=True)

        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )

        url = self.url(self.business.id, common_template.id)
        mb_image = baker.make(
            Image,
            business=self.business,
            category=ImageTypeEnum.BIZ_PHOTO,
        )
        data = {
            'title': 'bb',
            'body': 'ccc',
            'body_short': 'ddd',
            'image': {'id': mb_image.id},
            'recipients': {'group': BCIGroupName.ALL_CUSTOMERS},
        }
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_201_CREATED
        message_blast_id = resp.json['message_blast_id']
        message_blast = MessageBlast.objects.get(id=message_blast_id)
        assert message_blast.sent_by == self.user

    @override_feature_flag({SmsBlastMarketingTimeAdjustFlag.flag_name: True})
    def test_post_now_at_night_marketing_override(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        bci.reindex(refresh_index=True)

        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )

        url = self.url(self.business.id, common_template.id)
        data = {
            'title': 'title',
            'body': 'ccc',
            'body_short': 'ddd',
            'image': None,
            'recipients': {'group': BCIGroupName.ALL_CUSTOMERS},
        }

        business_tz = tz.gettz(self.business.time_zone_name)
        test_datetime = datetime(2021, 11, 11, 23, tzinfo=business_tz)
        with freeze_time(test_datetime):
            self.fetch(url, method='POST', body=data)
        message_blast = MessageBlast.objects.first()
        self.assertFalse(message_blast.sent)
        self.assertEqual(message_blast.scheduled_date, date(2021, 11, 12))
        self.assertEqual(message_blast.date_hour, MessageBlastTimeType.MORNING.value)

    @override_feature_flag({SmsBlastMarketingTimeAdjustFlag.flag_name: True})
    def test_post_now_marketing_override(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        bci.reindex(refresh_index=True)

        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )

        url = self.url(self.business.id, common_template.id)
        data = {
            'title': 'title',
            'body': 'ccc',
            'body_short': 'ddd',
            'image': None,
            'recipients': {'group': BCIGroupName.ALL_CUSTOMERS},
        }

        business_tz = tz.gettz(self.business.time_zone_name)
        test_datetime = datetime(2021, 11, 11, 13, tzinfo=business_tz)
        with freeze_time(test_datetime):
            self.fetch(url, method='POST', body=data)
        message_blast = MessageBlast.objects.first()
        self.assertTrue(message_blast.sent)
        self.assertIsNone(message_blast.scheduled_date)
        self.assertIsNone(message_blast.date_hour)

    @freeze_time(datetime(2021, 11, 11, 10, tzinfo=UTC))
    @patch('webapps.message_blast.events.send_message_blast')
    def test_post_scheduled(self, send_message_blast_mock):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        bci.reindex(refresh_index=True)

        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )

        url = self.url(self.business.id, common_template.id)
        data = {
            'title': 'title',
            'body': 'ccc',
            'body_short': 'ddd',
            'image': None,
            'recipients': {'group': BCIGroupName.ALL_CUSTOMERS},
            'date_hour': MessageBlastTimeType.MORNING,
            'scheduled_date': '2021-11-12',
        }
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_201_CREATED
        send_message_blast_mock.assert_not_called()
        message_blast = MessageBlast.objects.first()
        assert message_blast.title == 'title'
        assert message_blast.sent is False
        assert message_blast.sent is False
        assert message_blast.scheduled_date == date(2021, 11, 12)
        assert message_blast.name == common_template.name

    @override_settings(API_COUNTRY=Country.PL)
    def test_post_without_agreements(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        bci.reindex(refresh_index=True)

        baker.make(
            BusinessPolicyAgreement,
            business=self.business,
            gdpr_enabled=True,
        )

        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )

        url = self.url(self.business.id, common_template.id)

        data = self.get_data()
        data['recipients']['with_agreements'] = True
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_201_CREATED

        message_blast_id = resp.json['message_blast_id']
        message_blast = MessageBlast.objects.get(id=message_blast_id)
        assert message_blast.bcis == []

        data['recipients']['with_agreements'] = False
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_201_CREATED

        # Check if nothing changed
        common_template_2 = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )

        assert common_template.title == common_template_2.title
        assert common_template.body == common_template_2.body
        assert common_template.body_short == common_template_2.body_short
        message_blast_id = resp.json['message_blast_id']
        message_blast = MessageBlast.objects.get(id=message_blast_id)
        assert message_blast.body == data['body']
        assert message_blast.body_short == data['body_short']
        assert message_blast.title == data['title']
        assert message_blast.image == data['image']
        assert message_blast.sent_by == self.user
        assert bci.id in message_blast.bcis

    def test_blocked_phone_number_should_block_only_sms_blasts(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+**************',
            email='<EMAIL>',
            web_communication_agreement=True,
        )
        bci.reindex(refresh_index=True)

        self.assertFalse(BlockedPhoneNumber.objects.filter(cell_phone=bci.cell_phone).exists())

        resp = self.fetch(
            path=self.url(self.business.id, self.get_common_template().id),
            method='POST',
            body=self.get_data(),
        )
        self.assertEqual(resp.code, status.HTTP_201_CREATED)

        message_blast_id = resp.json['message_blast_id']
        self.assertEqual(
            len(
                NotificationHistoryDocument.tasks_get(
                    task_id=f'MessageBlastNotification, {message_blast_id}',
                    type=UserNotification.SMS_NOTIFICATION,
                )
            ),
            1,
        )

        baker.make(BlockedPhoneNumber, cell_phone=bci.cell_phone)

        resp = self.fetch(
            path=self.url(self.business.id, self.get_common_template().id),
            method='POST',
            body=self.get_data(),
        )
        self.assertEqual(resp.code, status.HTTP_201_CREATED)

        message_blast_id = resp.json['message_blast_id']
        notifications = NotificationHistoryDocument.tasks_get(
            task_id=f'MessageBlastNotification, {message_blast_id}',
        )
        self.assertEqual(len(notifications), 1)
        self.assertEqual(notifications[0].type, UserNotification.EMAIL_NOTIFICATION)

    def test_should_not_send_sms_if_user_has_blocked_phone_number(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='',
            email='<EMAIL>',
            web_communication_agreement=True,
            user=user_recipe.make(cell_phone='+**************'),
        )
        bci.reindex(refresh_index=True)
        baker.make(BlockedPhoneNumber, cell_phone=bci.user.cell_phone)

        resp = self.fetch(
            path=self.url(self.business.id, self.get_common_template().id),
            method='POST',
            body=self.get_data(),
        )
        self.assertEqual(resp.code, status.HTTP_201_CREATED)

        message_blast_id = resp.json['message_blast_id']
        self.assertEqual(
            len(
                NotificationHistoryDocument.tasks_get(
                    task_id=f'MessageBlastNotification, {message_blast_id}',
                    type=UserNotification.SMS_NOTIFICATION,
                )
            ),
            0,
        )

    def test_should_not_send_sms_if_bci_has_blocked_phone_but_user_not(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+**************',
            email='<EMAIL>',
            web_communication_agreement=True,
            user=user_recipe.make(cell_phone='+**************'),
        )
        bci.reindex(refresh_index=True)

        baker.make(BlockedPhoneNumber, cell_phone=bci.cell_phone)
        self.assertFalse(BlockedPhoneNumber.objects.filter(cell_phone=bci.user.cell_phone).exists())

        resp = self.fetch(
            path=self.url(self.business.id, self.get_common_template().id),
            method='POST',
            body=self.get_data(),
        )
        self.assertEqual(resp.code, status.HTTP_201_CREATED)

        message_blast_id = resp.json['message_blast_id']
        self.assertEqual(
            len(
                NotificationHistoryDocument.tasks_get(
                    task_id=f'MessageBlastNotification, {message_blast_id}',
                    type=UserNotification.SMS_NOTIFICATION,
                )
            ),
            0,
        )


@pytest.mark.django_db
class MessageBlastContentTemplateHandlerTests(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        self.url = f'/business_api/me/businesses/{self.business.id}/message_blast/content/'
        self.image = baker.make(Image, image_url='exmaple_url.com')

    def test_content_serializer(self):
        title = 'Title'
        body = 'Some content text'
        body_short = 'Some sms text'
        resp = self.fetch(
            self.url,
            method='POST',
            body={
                'image': {'id': self.image.id},
                'title': title,
                'body': body,
                'body_short': body_short,
            },
        )
        assert resp.code == status.HTTP_200_OK

        assert title in resp.json['email']
        assert body in resp.json['email']
        assert self.image.image_url in resp.json['email']
        assert body_short in resp.json['sms']
        assert body_short in resp.json['push']

    def test_stripped_content_serializer(self):
        title = 'Title'
        body = 'Some content text'
        body_short = 'Some sms text'
        resp = self.fetch(
            self.url,
            method='POST',
            body={
                'image': {'id': self.image.id},
                'title': title,
                'body': body,
                'body_short': body_short,
                'stripped': True,
            },
        )
        assert resp.code == status.HTTP_200_OK

        assert title in resp.json['email']
        assert body in resp.json['email']
        assert 'body' not in resp.json['email']
        assert 'html' not in resp.json['email']
        assert self.image.image_url in resp.json['email']
        assert body_short in resp.json['sms']
        assert body_short in resp.json['push']

    def test_sms_content_with_special_characters(self):
        title = 'Title'
        body = 'Some content text'
        body_short = 'ęśąćż'  # pozdro dla mBanku
        expected_body_short = 'esacz'

        resp = self.fetch(
            self.url,
            method='POST',
            body={
                'title': title,
                'body': body,
                'body_short': body_short,
            },
        )

        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertIn(title, resp.json['email'])
        self.assertIn(body, resp.json['email'])
        self.assertEqual(expected_body_short, resp.json['sms'])
        self.assertEqual(body_short, resp.json['push'])


@pytest.mark.django_db
class MessageBlastCustomerCounterHandlerTests(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        update_message_blast_groups()
        create_templates()
        _get_clean_business_customer_index()

    def test_post_without_gdpr(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        bci.reindex(refresh_index=True)

        bci2 = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        bci2.reindex(refresh_index=True)

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/counter/'

        data = {'group': BCIGroupName.ALL_CUSTOMERS}
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_200_OK
        assert resp.json['all_customers'] == 2
        assert resp.json['customers_with_agreements'] == 2

    @override_settings(API_COUNTRY=Country.PL)
    def test_post_gdpr(self):
        baker.make(
            BusinessPolicyAgreement,
            business=self.business,
            gdpr_enabled=True,
        )

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            web_communication_agreement=False,
        )
        bci.reindex(refresh_index=True)

        bci2 = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            web_communication_agreement=True,
        )
        bci2.reindex(refresh_index=True)

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/counter/'

        data = {'group': BCIGroupName.ALL_CUSTOMERS}
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_200_OK
        assert resp.json['all_customers'] == 2
        assert resp.json['customers_with_agreements'] == 1

    @override_feature_flag({MessageBlastGDPREnabledUSCAFlag.flag_name: True})
    @override_settings(API_COUNTRY=Country.US)
    def test_post_us_feature_falg(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            web_communication_agreement=False,
        )
        bci.reindex(refresh_index=True)

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/counter/'

        data = {'group': BCIGroupName.ALL_CUSTOMERS, 'ids': [bci.id]}
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_200_OK
        assert resp.json['all_customers'] == 1
        assert resp.json['customers_with_agreements'] == 0

    def test_post_unsubscribed(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        baker.make(Unsubscribed, bci=bci)
        bci.reindex(refresh_index=True)

        bci2 = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            web_communication_agreement=True,
        )
        bci2.reindex(refresh_index=True)

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/counter/'

        data = {'group': BCIGroupName.ALL_CUSTOMERS}
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_200_OK
        assert resp.json['all_customers'] == 1
        assert resp.json['customers_with_agreements'] == 1


@pytest.mark.django_db
@patch(
    'webapps.notification.models.NotificationSMSStatistics.sms_limits', MagicMock(return_value=100)
)
class ManualMessageBlastCostEstimationHandler(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        update_message_blast_groups()
        create_templates()
        _get_clean_business_customer_index()

    @override_settings(API_COUNTRY=Country.PL)
    def test_post_agreements(self):
        baker.make(
            BusinessPolicyAgreement,
            business=self.business,
            gdpr_enabled=True,
        )

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            web_communication_agreement=False,
            email='<EMAIL>',
        )
        bci.reindex(refresh_index=True)

        bci2 = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            web_communication_agreement=True,
            email='<EMAIL>',
        )
        bci2.reindex(refresh_index=True)

        template = CommonMessageBlastTemplate.objects.filter_one_time_messages(
            self.business.id,
        ).last()

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/{template.id}/cost_estimation/'
        )

        data = {
            'group': BCIGroupName.ALL_CUSTOMERS,
            'with_agreements': True,
        }

        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_200_OK
        assert resp.json['receivers_amount'] == 1

        data = {
            'group': BCIGroupName.ALL_CUSTOMERS,
            'with_agreements': False,
        }

        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_200_OK
        assert resp.json['receivers_amount'] == 2

    @override_settings(SMS_DEMO_ACCOUNT_PREPAID_COUNT=9)
    def test_post_trial(self):
        self.business.status = Business.Status.TRIAL
        self.business.save()

        for i in range(7):
            bci = baker.make(
                BusinessCustomerInfo,
                business=self.business,
                cell_phone=f'+*********{i}',
                email=f'{i}@test.com',
            )
            bci.reindex(refresh_index=True)

        template = CommonMessageBlastTemplate.objects.filter_one_time_messages(
            self.business.id,
        ).last()

        template.body_short = 'a' * 300
        template.save()

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/{template.id}/cost_estimation/'
        )

        data = {'group': BCIGroupName.ALL_CUSTOMERS}

        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_200_OK
        expected_response = {
            "receivers_amount": 7,
            "sms_cost": SMSCosts.get_part_price_and_currency()[0],
            "total_sms_cost": 5 * SMSCosts.get_part_price_and_currency()[0],
            "paid_sms_amount": 5,
            "sms_amount": 14,
            "sms_recipients": 7,
            "email_recipients": 7,
            "push_recipients": 0,
            "free_sms_amount": 9,
            "sms_parts_per_receiver": 2,
            "out_of_sms": False,
        }
        dict_assert(resp.json, expected_response, strict=True)

    @patch('webapps.purchase.utils.get_subscription_prepaid_sms_allowance')
    @patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
    def test_post_over_limit(self, prepaid_sms_allowance_mock):
        prepaid_sms_allowance_mock.return_value = 1

        with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
            baker.make(
                Subscription,
                business=self.business,
                start=datetime(2021, 9, 1, tzinfo=UTC),
            )

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+*********44',
            email='<EMAIL>',
        )
        bci.reindex(refresh_index=True)

        bci2 = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+*********45',
            email='<EMAIL>',
        )
        bci2.reindex(refresh_index=True)

        template = CommonMessageBlastTemplate.objects.filter_one_time_messages(
            self.business.id,
        ).last()

        template.body_short = 'a' * 300
        template.save()

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/{template.id}/cost_estimation/'
        )

        data = {'group': BCIGroupName.ALL_CUSTOMERS}

        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_200_OK
        expected_response = {
            "receivers_amount": 2,
            "sms_cost": SMSCosts.get_part_price_and_currency()[0],
            "total_sms_cost": 3 * SMSCosts.get_part_price_and_currency()[0],
            "paid_sms_amount": 3,
            "sms_amount": 4,
            "sms_recipients": 2,
            "email_recipients": 2,
            "push_recipients": 0,
            "free_sms_amount": 1,
            "sms_parts_per_receiver": 2,
            "out_of_sms": False,
        }
        dict_assert(resp.json, expected_response, strict=True)

    @patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
    @patch('webapps.purchase.utils.get_subscription_prepaid_sms_allowance')
    def test_post_within_limit(self, prepaid_sms_allowance_mock):
        prepaid_sms_allowance_mock.return_value = 10

        with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
            baker.make(
                Subscription,
                business=self.business,
                start=datetime(2021, 9, 1, tzinfo=UTC),
            )
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+*********44',
            email='<EMAIL>',
        )
        bci.reindex(refresh_index=True)

        bci2 = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+*********45',
            email='<EMAIL>',
        )
        bci2.reindex(refresh_index=True)

        template = CommonMessageBlastTemplate.objects.filter_one_time_messages(
            self.business.id,
        ).last()

        template.body_short = 'a' * 300
        template.save()

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/{template.id}/cost_estimation/'
        )

        data = {'group': BCIGroupName.ALL_CUSTOMERS}

        resp = self.fetch(url, method='POST', body=data)

        assert resp.code == status.HTTP_200_OK
        expected_response = {
            "receivers_amount": 2,
            "sms_cost": SMSCosts.get_part_price_and_currency()[0],
            "total_sms_cost": 0.0,
            "paid_sms_amount": 0,
            "sms_amount": 4,
            "sms_recipients": 2,
            "email_recipients": 2,
            "push_recipients": 0,
            "free_sms_amount": 10,
            "sms_parts_per_receiver": 2,
            "out_of_sms": False,
        }
        dict_assert(resp.json, expected_response, strict=True)

    @freeze_time(datetime(2021, 9, 30, tzinfo=UTC))
    @patch.object(BillingCycle, 'current_sms_price', new_callable=PropertyMock)
    def test_post_new_billing(
        self,
        current_sms_price,
    ):
        current_sms_price.return_value = 0.05
        self.business.has_new_billing = True
        self.business.save()

        subscription = baker.make(
            BillingSubscription,
            business=self.business,
            date_start=datetime(2021, 9, 1, tzinfo=UTC),
            payment_period=relativedelta(months=1),
            currency='USD',
        )
        baker.make(
            BillingCycle,
            business=self.business,
            subscription=subscription,
            sms_allowance=15,
        )

        for i in range(40):
            bci = baker.make(
                BusinessCustomerInfo,
                business=self.business,
                cell_phone=f'+*********{i}',
                email=f'{i}@test.com',
            )
            baker.make(
                SMSBlastMarketingConsent,
                business=self.business,
                bci=bci,
                cell_phone=bci.cell_phone,
                consented=True,
            )
            bci.reindex(refresh_index=True)

        template = CommonMessageBlastTemplate.objects.filter_one_time_messages(
            self.business.id,
        ).last()

        template.body_short = 'A' * 10  # one sms part per BCI
        template.save()

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/{template.id}/cost_estimation/'
        )

        data = {'group': BCIGroupName.ALL_CUSTOMERS}

        resp = self.fetch(url, method='POST', body=data)
        print(resp.json)
        assert resp.code == status.HTTP_200_OK
        assert resp.json['sms_cost'] == 0.05
        expected_response = {
            "receivers_amount": 40,
            "sms_cost": 0.05,
            "total_sms_cost": 25 * 0.05,
            "paid_sms_amount": 25,
            "sms_amount": 40,
            "sms_recipients": 40,
            "email_recipients": 40,
            "push_recipients": 0,
            "free_sms_amount": 15,
            "sms_parts_per_receiver": 1,
            "out_of_sms": False,
        }
        dict_assert(resp.json, expected_response, strict=True)

    @override_settings(SMS_DEMO_ACCOUNT_PREPAID_COUNT=5, SMS_LIMITS_IN_SUBSCRIPTION=False)
    @patch('webapps.notification.utils.legacy_get_non_trial_sms_limits')
    def test_post_new_billing_but_no_sub(self, sms_limit_mock):
        sms_limit_mock.return_value = {
            'plan_prepaid_sms_count': 0,
            'postpaid_part_cost': SMSCosts.get_part_price_and_currency()[0],
        }

        self.business.has_new_billing = True
        self.business.save()

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+*********44',
        )
        bci.reindex(refresh_index=True)

        template = CommonMessageBlastTemplate.objects.filter_one_time_messages(
            self.business.id,
        ).last()

        template.body_short = 'A' * 10  # one sms part per BCI
        template.save()

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/{template.id}/cost_estimation/'
        )

        data = {'group': BCIGroupName.ALL_CUSTOMERS}

        resp = self.fetch(url, method='POST', body=data)
        self.assertEqual(resp.code, status.HTTP_200_OK)
        expected_response = {
            "receivers_amount": 1,
            "sms_cost": SMSCosts.get_part_price_and_currency()[0],
            "total_sms_cost": 0.0,
            "paid_sms_amount": 0,
            "sms_amount": 1,
            "sms_recipients": 1,
            "email_recipients": 0,
            "push_recipients": 0,
            "free_sms_amount": 5,
            "sms_parts_per_receiver": 1,
            "out_of_sms": False,
        }
        dict_assert(resp.json, expected_response, strict=True)


@pytest.mark.django_db
@patch(
    'webapps.notification.models.NotificationSMSStatistics.sms_limits', MagicMock(return_value=0)
)
class ManualMessageBlastCostEstimationHandlerSMSLimit(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        update_message_blast_groups()
        create_templates()
        _get_clean_business_customer_index()

    @patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
    @patch('webapps.purchase.utils.get_subscription_prepaid_sms_allowance')
    def test_post_over_limit_out_of_sms(self, prepaid_sms_allowance_mock):
        prepaid_sms_allowance_mock.return_value = 0

        with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
            baker.make(
                Subscription,
                business=self.business,
                start=datetime(2021, 9, 1, tzinfo=UTC),
            )

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+*********44',
            email='<EMAIL>',
        )
        bci.reindex(refresh_index=True)

        bci2 = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+*********45',
            email='<EMAIL>',
        )
        bci2.reindex(refresh_index=True)

        template = CommonMessageBlastTemplate.objects.filter_one_time_messages(
            self.business.id,
        ).last()

        template.body_short = 'a' * 300
        template.save()

        url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/{template.id}/cost_estimation/'
        )

        data = {'group': BCIGroupName.ALL_CUSTOMERS}

        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_200_OK
        expected_response = {
            "receivers_amount": 2,
            "sms_cost": SMSCosts.get_part_price_and_currency()[0],
            "total_sms_cost": 0 * SMSCosts.get_part_price_and_currency()[0],
            "paid_sms_amount": 0,
            "sms_amount": 0,
            "sms_recipients": 0,
            "push_recipients": 0,
            "email_recipients": 2,
            "free_sms_amount": 0,
            "sms_parts_per_receiver": 2,
            "out_of_sms": True,
        }
        dict_assert(resp.json, expected_response, strict=True)


@pytest.mark.django_db
@patch(
    'webapps.notification.models.NotificationSMSStatistics.sms_limits', MagicMock(return_value=100)
)
class MessageBlastCostEstimationHandler(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        _get_clean_business_customer_index()
        self.bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+*********44',
            web_communication_agreement=False,
            email='<EMAIL>',
        )
        self.consent1 = baker.make(
            SMSBlastMarketingConsent,
            business=self.business,
            bci=self.bci,
            cell_phone=self.bci.cell_phone,
            consented=True,
        )
        self.bci.reindex(refresh_index=True)

        self.bci2 = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+*********45',
            web_communication_agreement=True,
            email='<EMAIL>',
        )
        self.consent2 = baker.make(
            SMSBlastMarketingConsent,
            business=self.business,
            bci=self.bci2,
            cell_phone=self.bci2.cell_phone,
            consented=True,
        )
        self.bci2.reindex(refresh_index=True)
        channel_priority = {
            MessageBlastChannelType.DEFAULT.value: True,
            MessageBlastChannelType.SMS.value: False,
            MessageBlastChannelType.PREFER_PUSH.value: False,
            MessageBlastChannelType.PUSH.value: False,
            MessageBlastChannelType.EMAIL.value: False,
        }
        self.data_long_sms = {
            'group': BCIGroupName.ALL_CUSTOMERS,
            'body_short': 'a' * 300,
            'channel_priority': channel_priority,
        }
        self.data_short_sms = {
            'group': BCIGroupName.ALL_CUSTOMERS,
            'body_short': 'a' * 10,
            'channel_priority': channel_priority,
        }
        self.url = f'/business_api/me/businesses/{self.business.id}/message_blast/cost_estimation/'

    def test_post_without_gdpr_agreement(self):
        resp = self.fetch(self.url, method='POST', body=self.data_short_sms)
        assert resp.code == status.HTTP_200_OK
        receivers = resp.json['receivers']
        assert receivers['gdpr'] is False
        assert receivers['with_gdpr_agreement']['data'] is None
        data_without_gdpr = receivers['without_gdpr_agreement']['data']
        assert data_without_gdpr['receivers_amount'] == 2

    def test_post_check_flag_without_es(self):
        resp = self.fetch(self.url, method='POST', body=self.data_short_sms)
        assert resp.code == status.HTTP_200_OK
        assert resp.json['receivers']['without_gdpr_agreement']['data']['receivers_amount'] == 2

    @override_feature_flag({MessageBlastGDPREnabledUSCAFlag.flag_name: True})
    def test_post_without_gdpr_agreement_feature_flag_us(self):
        resp = self.fetch(self.url, method='POST', body=self.data_short_sms)
        assert resp.code == status.HTTP_200_OK
        receivers = resp.json['receivers']
        expected = {
            'gdpr': True,
            'without_gdpr_agreement': {
                'data': {
                    'receivers_amount': 2,
                    'sms_empty': False,
                    'sms_cost': 0.005,
                    'sms_cost_total': 0.0,
                    'estimated_sms': 2,
                    'sms_recipients': 2,
                    'estimated_email': 2,
                    'estimated_push': 0,
                    'sms_parts_per_receiver': 1,
                }
            },
            'with_gdpr_agreement': {
                'data': {
                    'receivers_amount': 1,
                    'sms_empty': False,
                    'sms_cost': 0.005,
                    'sms_cost_total': 0.0,
                    'estimated_sms': 1,
                    "sms_recipients": 1,
                    'estimated_email': 1,
                    'estimated_push': 0,
                    'sms_parts_per_receiver': 1,
                }
            },
        }
        self.assertEqual(receivers, expected)

    @override_settings(API_COUNTRY=Country.PL)
    def test_post_with_gdpr_agreement(self):
        baker.make(
            BusinessPolicyAgreement,
            business=self.business,
            gdpr_enabled=True,
        )
        resp = self.fetch(self.url, method='POST', body=self.data_short_sms)
        receivers = resp.json['receivers']
        assert receivers['gdpr'] is True
        assert receivers['with_gdpr_agreement']['data'] is not None
        data_with_gdpr = receivers['with_gdpr_agreement']['data']
        assert data_with_gdpr['receivers_amount'] == 1

        assert receivers['without_gdpr_agreement']['data'] is not None
        data_without_gdpr = receivers['without_gdpr_agreement']['data']
        assert data_without_gdpr['receivers_amount'] == 2

    @override_settings(SMS_DEMO_ACCOUNT_PREPAID_COUNT=9)
    def test_post_trial(self):
        # 7 + 2 BCI were created in setup and 'all_customers' group passed
        # 9 sms paid. Sms limit (9 paid + 9 free). 18 parts sent (9 x 2), because long sms
        self.business.status = Business.Status.TRIAL
        self.business.save()
        _get_clean_business_customer_index()

        for i in range(7):
            bci = baker.make(
                BusinessCustomerInfo,
                business=self.business,
                cell_phone=f'+*********{i}',
                email=f'{i}<EMAIL>',
            )
            baker.make(
                SMSBlastMarketingConsent,
                business=self.business,
                bci=bci,
                cell_phone=bci.cell_phone,
                consented=True,
            )
            bci.reindex(refresh_index=True)

        resp = self.fetch(self.url, method='POST', body=self.data_long_sms)
        assert resp.code == status.HTTP_200_OK
        expected_response = {
            "receivers": {
                "gdpr": False,
                "without_gdpr_agreement": {
                    "data": {
                        "receivers_amount": 9,
                        "sms_empty": False,
                        "sms_cost": 0.005,
                        "sms_cost_total": 0.045,  # 9 paid
                        "estimated_sms": 18,
                        "sms_recipients": 9,
                        "estimated_email": 9,
                        "estimated_push": 0,
                        "sms_parts_per_receiver": 2,
                    }
                },
                "with_gdpr_agreement": {"data": None},
            }
        }
        dict_assert(resp.json, expected_response, strict=True)

    @patch('webapps.purchase.utils.get_subscription_prepaid_sms_allowance')
    @patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
    def test_post_over_limit(self, prepaid_sms_allowance_mock):
        # 3 sms paid. Sms limit (1 free). 4 parts sent (2 x 2), because long sms
        prepaid_sms_allowance_mock.return_value = 1

        with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
            baker.make(
                Subscription,
                business=self.business,
                start=datetime(2021, 9, 1, tzinfo=UTC),
            )

        resp = self.fetch(self.url, method='POST', body=self.data_long_sms)
        assert resp.code == status.HTTP_200_OK
        expected_response = {
            "receivers": {
                "gdpr": False,
                "without_gdpr_agreement": {
                    "data": {
                        "receivers_amount": 2,
                        "sms_empty": False,
                        "sms_cost": 0.005,
                        "sms_cost_total": 0.015,  # 3 paid
                        "estimated_sms": 4,
                        "sms_recipients": 2,
                        "estimated_email": 2,
                        "estimated_push": 0,
                        "sms_parts_per_receiver": 2,
                    }
                },
                "with_gdpr_agreement": {"data": None},
            }
        }
        dict_assert(resp.json, expected_response, strict=True)

    @patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
    @patch('webapps.purchase.utils.get_subscription_prepaid_sms_allowance')
    def test_post_within_limit(self, prepaid_sms_allowance_mock):
        # 0 sms paid. Sms limit (10 free). 4 parts sent (2 x 2), because long sms
        prepaid_sms_allowance_mock.return_value = 10
        with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
            baker.make(
                Subscription,
                business=self.business,
                start=datetime(2021, 9, 1, tzinfo=UTC),
            )
        resp = self.fetch(self.url, method='POST', body=self.data_long_sms)

        assert resp.code == status.HTTP_200_OK
        expected_response = {
            "receivers": {
                "gdpr": False,
                "without_gdpr_agreement": {
                    "data": {
                        "receivers_amount": 2,
                        "sms_empty": False,
                        "sms_cost": 0.005,
                        "sms_cost_total": 0.00,
                        "estimated_sms": 4,
                        "sms_recipients": 2,
                        "estimated_email": 2,
                        "estimated_push": 0,
                        "sms_parts_per_receiver": 2,
                    }
                },
                "with_gdpr_agreement": {"data": None},
            }
        }
        dict_assert(resp.json, expected_response, strict=True)

    @freeze_time(datetime(2021, 9, 30, tzinfo=UTC))
    @patch.object(BillingCycle, 'current_sms_price', new_callable=PropertyMock)
    def test_post_new_billing(self, current_sms_price):
        # 25 sms paid. Sms limit (40 paid). 4 parts sent (2 x 1), because short sms
        current_sms_price.return_value = 0.05
        _get_clean_business_customer_index()
        self.business.has_new_billing = True
        self.business.save()

        subscription = baker.make(
            BillingSubscription,
            business=self.business,
            date_start=datetime(2021, 9, 1, tzinfo=UTC),
            payment_period=relativedelta(months=1),
            currency='USD',
        )
        baker.make(
            BillingCycle,
            business=self.business,
            subscription=subscription,
            sms_allowance=15,
        )

        for i in range(40):
            bci = baker.make(
                BusinessCustomerInfo,
                business=self.business,
                cell_phone=f'+*********{i}',
                email=f'{i}<EMAIL>',
            )
            baker.make(
                SMSBlastMarketingConsent,
                business=self.business,
                bci=bci,
                cell_phone=bci.cell_phone,
                consented=True,
            )
            bci.reindex(refresh_index=True)

        resp = self.fetch(self.url, method='POST', body=self.data_short_sms)
        assert resp.code == status.HTTP_200_OK
        expected_response = {
            "receivers": {
                "gdpr": False,
                "without_gdpr_agreement": {
                    "data": {
                        # 40 + 2 created in setup and 'all_customers' group passed
                        "receivers_amount": 42,
                        "sms_empty": False,
                        "sms_cost": 0.05,
                        "sms_cost_total": 27 * 0.05,
                        "estimated_sms": 42,
                        "sms_recipients": 42,
                        "estimated_email": 42,
                        "estimated_push": 0,
                        "sms_parts_per_receiver": 1,
                    }
                },
                "with_gdpr_agreement": {"data": None},
            }
        }
        dict_assert(resp.json, expected_response, strict=True)

    @override_settings(SMS_DEMO_ACCOUNT_PREPAID_COUNT=5, SMS_LIMITS_IN_SUBSCRIPTION=False)
    @patch('webapps.notification.utils.legacy_get_non_trial_sms_limits')
    def test_post_new_billing_but_no_sub(self, sms_limit_mock):
        # 0 sms paid. Sms limit (5 free). 2 parts sent (2 x 1), because short sms
        sms_limit_mock.return_value = {
            'plan_prepaid_sms_count': 0,
            'postpaid_part_cost': SMSCosts.get_part_price_and_currency()[0],
        }
        self.business.has_new_billing = True
        self.business.save()

        resp = self.fetch(self.url, method='POST', body=self.data_short_sms)
        self.assertEqual(resp.code, status.HTTP_200_OK)
        expected_response = {
            "receivers": {
                "gdpr": False,
                "without_gdpr_agreement": {
                    "data": {
                        "receivers_amount": 2,
                        "sms_empty": False,
                        "sms_cost": 0.005,
                        "sms_cost_total": 0.0,
                        "estimated_sms": 2,
                        "sms_recipients": 2,
                        "estimated_email": 2,
                        "estimated_push": 0,
                        "sms_parts_per_receiver": 1,
                    }
                },
                "with_gdpr_agreement": {"data": None},
            }
        }
        dict_assert(resp.json, expected_response, strict=True)

    @override_settings(API_COUNTRY=Country.PL)
    def test_post_gdpr_select_group_with_excluded(self):
        baker.make(
            BusinessPolicyAgreement,
            business=self.business,
            gdpr_enabled=True,
        )

        bci3 = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            web_communication_agreement=True,
            email='<EMAIL>',
        )
        bci3.reindex(refresh_index=True)

        data = self.data_short_sms.copy()
        data['excluded'] = [bci3.id]

        resp = self.fetch(self.url, method='POST', body=data)
        self.assertEqual(resp.code, status.HTTP_200_OK)

        assert resp.json['receivers']['with_gdpr_agreement']['data']['receivers_amount'] == 1
        assert resp.json['receivers']['without_gdpr_agreement']['data']['receivers_amount'] == 2

    def test_blocked_phone_number_should_affect_estimated_sms_number(self):
        self.assertFalse(BlockedPhoneNumber.objects.filter(cell_phone=self.bci.cell_phone).exists())

        resp = self.fetch(self.url, method='POST', body=self.data_short_sms)
        self.assertEqual(resp.code, status.HTTP_200_OK)

        self.assertEqual(
            resp.json['receivers']['without_gdpr_agreement']['data']['receivers_amount'], 2
        )
        self.assertEqual(
            resp.json['receivers']['without_gdpr_agreement']['data']['estimated_sms'], 2
        )

        baker.make(BlockedPhoneNumber, cell_phone=self.bci.cell_phone)

        resp = self.fetch(self.url, method='POST', body=self.data_short_sms)
        self.assertEqual(resp.code, status.HTTP_200_OK)

        self.assertEqual(
            resp.json['receivers']['without_gdpr_agreement']['data']['receivers_amount'], 2
        )
        self.assertEqual(
            resp.json['receivers']['without_gdpr_agreement']['data']['estimated_sms'], 1
        )

    @override_settings(API_COUNTRY=Country.US)
    def test_post_without_double_opt_in_consent(self):
        SMSBlastMarketingConsent.objects.delete()
        resp = self.fetch(self.url, method='POST', body=self.data_short_sms)

        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertEqual(
            resp.json['receivers']['without_gdpr_agreement']['data']['estimated_sms'], None
        )

    @override_settings(API_COUNTRY=Country.US)
    def test_post_double_opt_in_consented(self):
        resp = self.fetch(self.url, method='POST', body=self.data_short_sms)

        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertEqual(
            resp.json['receivers']['without_gdpr_agreement']['data']['estimated_sms'], 2
        )


@pytest.mark.django_db
@patch(
    'webapps.notification.models.NotificationSMSStatistics.sms_limits', MagicMock(return_value=1)
)
class MessageBlastCostEstimationHandlerSMSLimit(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        _get_clean_business_customer_index()

    @override_settings(API_COUNTRY=Country.PL)
    @patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
    @patch('webapps.purchase.utils.get_subscription_prepaid_sms_allowance')
    def test_post_out_of_sms(self, prepaid_sms_allowance_mock):
        prepaid_sms_allowance_mock.return_value = 0
        baker.make(
            BusinessPolicyAgreement,
            business=self.business,
            gdpr_enabled=True,
        )
        with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
            baker.make(
                Subscription,
                business=self.business,
                start=datetime(2021, 9, 1, tzinfo=UTC),
            )

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            web_communication_agreement=False,
            cell_phone='+*********44',
        )
        bci.reindex(refresh_index=True)

        bci2 = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            web_communication_agreement=True,
            cell_phone='+*********45',
        )
        bci2.reindex(refresh_index=True)

        url = f'/business_api/me/businesses/{self.business.id}/message_blast/cost_estimation/'
        data = {
            'group': BCIGroupName.ALL_CUSTOMERS,
            'body_short': 'a' * 10,
            'channel_priority': {
                MessageBlastChannelType.DEFAULT.value: True,
                MessageBlastChannelType.SMS.value: False,
                MessageBlastChannelType.PREFER_PUSH.value: False,
                MessageBlastChannelType.PUSH.value: False,
                MessageBlastChannelType.EMAIL.value: False,
            },
        }
        resp = self.fetch(url, method='POST', body=data)

        assert resp.code == status.HTTP_200_OK
        expected_response = {
            "receivers": {
                "gdpr": True,
                "without_gdpr_agreement": {
                    "data": {
                        "receivers_amount": 0,
                        "sms_empty": True,
                        "sms_cost": 0.1,
                        "sms_cost_total": 0.0,
                        "estimated_sms": None,
                        "sms_recipients": 0,
                        "estimated_email": 0,
                        "estimated_push": 0,
                        "sms_parts_per_receiver": 1,
                    }
                },
                "with_gdpr_agreement": {
                    "data": {
                        "receivers_amount": 1,
                        "sms_empty": False,
                        "sms_cost": 0.1,
                        "sms_cost_total": 0.1,
                        "estimated_sms": 1,
                        "sms_recipients": 1,
                        "estimated_email": 0,
                        "estimated_push": 0,
                        "sms_parts_per_receiver": 1,
                    }
                },
            }
        }
        dict_assert(resp.json, expected_response, strict=True)


@pytest.mark.django_db
class ActivateMessageBlastHandlerTests(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        update_message_blast_groups()
        create_templates(7)
        self.url = f'/business_api/me/businesses/{self.business.id}/message_blast/activate/'

    def test_get(self):
        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK
        templates = resp.json['business_templates']

        assert len(templates) == 4
        for template in templates:
            assert template['automated_status'] == MessageBlastTemplateStatus.INACTIVE

    def test_post(self):
        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK
        business_events = self.business.events
        assert business_events.ask_for_message_blast_activation is True

        data = {
            'common_message_blast_template_ids': [
                resp.json['business_templates'][0]['id'],
                resp.json['business_templates'][1]['id'],
            ]
        }

        resp = self.fetch(self.url, method='POST', body=data)
        assert resp.code == status.HTTP_200_OK
        groups = resp.json['groups']

        templates = [template for group in groups for template in group['business_templates']]

        assert len(groups) == 4
        assert len(templates) == 7

        assert templates[0]['internal_name'] == MessageBlastInternalNames.WELCOME_NEW_CLIENT
        assert templates[0]['automated_status'] == MessageBlastTemplateStatus.ACTIVE

        assert (
            templates[1]['internal_name'] == MessageBlastInternalNames.INFORM_ABOUT_OTHER_SERVICES
        )
        assert templates[1]['automated_status'] == MessageBlastTemplateStatus.ACTIVE

        for i in range(2, 7):
            assert templates[i]['automated_status'] == MessageBlastTemplateStatus.INACTIVE

        business_events.refresh_from_db()
        assert business_events.ask_for_message_blast_activation is False

    def test_post_as_superuser(self):
        self.session = self.user.create_session(
            origin=AuthOriginEnum.BOOKSY, superuser_name='su', fingerprint=''
        )
        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK
        business_event = self.business.events
        assert business_event.ask_for_message_blast_activation is True

        data = {
            'common_message_blast_template_ids': [
                resp.json['business_templates'][0]['id'],
                resp.json['business_templates'][1]['id'],
            ]
        }

        resp = self.fetch(self.url, method='POST', body=data)
        assert resp.code == status.HTTP_200_OK

        groups = resp.json['groups']
        templates = [template for group in groups for template in group['business_templates']]
        for template in templates:
            assert template['automated_status'] == MessageBlastTemplateStatus.INACTIVE
        business_event.refresh_from_db()
        assert business_event.ask_for_message_blast_activation is True


@pytest.mark.django_db
class ActivateRecommendedMessageBlastHandlerTests(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        update_message_blast_groups()
        create_templates(7)
        self.url = (
            f'/business_api/me/businesses/{self.business.id}'
            f'/message_blast/activate_recommended/'
        )

    def test_post(self):
        resp = self.fetch(self.url, method='POST', body={})

        assert resp.code == status.HTTP_200_OK
        assert check_all_recommended_ever_activated(business_id=self.business.id) is True
        message_blast_templates = MessageBlastTemplate.objects.filter(
            business=self.business,
            internal_name__in=MESSAGE_BLAST_RECOMMENDED_LIST,
            automated_status=MessageBlastTemplateStatus.ACTIVE,
        )
        assert message_blast_templates.count() == 4

    @patch('webapps.message_blast.helpers.turn_on_recommended_blasts_for_business')
    def test_post_as_superuser(self, mock_recommended):
        self.session = self.user.create_session(
            origin=AuthOriginEnum.BOOKSY,
            superuser_name='su',
            fingerprint='',
        )
        resp = self.fetch(self.url, method='POST', body={})

        assert resp.code == status.HTTP_200_OK
        assert check_all_recommended_ever_activated(business_id=self.business.id) is False
        message_blast_templates = MessageBlastTemplate.objects.filter(
            business=self.business,
            internal_name__in=MESSAGE_BLAST_RECOMMENDED_LIST,
            automated_status=MessageBlastTemplateStatus.ACTIVE,
        )
        assert message_blast_templates.count() == 0
        mock_recommended.assert_not_called()


@pytest.mark.django_db
class TestCreateBlastTemplateForPromotions(TestCase):
    def run_test(self):
        business = baker.make(Business)
        image = baker.make(Image, image_url='exmaple_url.com')
        baker.make(
            MessageBlastImage,
            template_internal_name=MessageBlastInternalNames.BUSINESS_PROMOTIONS,
            image=image,
        )
        title = 'title text'
        text = 'body text'
        message_blast_template = CommonMessageBlastTemplate.create_blast_template_for_promotions(
            business=business, title=title, text=text
        )

        result = {
            'id': None,
            'title': title,
            'body': text,
            'body_short': text,
            'image': {'id': image.id, 'image_url': image.image_url},
        }
        assert message_blast_template == result


@pytest.mark.django_db
class MessageBlastTestSendHandlerTests(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}/message_blast/{}/one_time_send_test/'.format

    def setUp(self):
        super().setUp()
        update_message_blast_groups()
        create_templates(1)

    @staticmethod
    def get_data(**kwargs):
        return {
            'title': 'title',
            'body': 'ccc',
            'body_short': 'ddd',
            'image': None,
            **kwargs,
        }

    def test_post(self):
        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )

        message_blast_template = baker.make(
            MessageBlastTemplate,
            common_template=common_template,
            group=common_template.group,
            internal_name=common_template.internal_name,
            business=self.business,
        )

        url = self.url(self.business.id, message_blast_template.id)
        resp = self.fetch(url, method='POST', body=self.get_data())
        assert resp.code == status.HTTP_201_CREATED
        message_blast_id = resp.json['message_blast_id']
        message_blast = MessageBlast.objects.get(id=message_blast_id)
        assert message_blast.sent_by == self.user


@pytest.mark.django_db
class MessageBlastScheduledHandlerTests(BaseAsyncHTTPTest):
    def test_get_scheduled(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
        )
        bci.reindex(refresh_index=True)
        message_blast = baker.make(
            MessageBlast,
            business=self.business,
            internal_name=MessageBlastInternalNames.OWN_MESSAGE,
            sent=False,
            title='title',
            date_hour=MessageBlastTimeType.MORNING,
            scheduled_date=date(2021, 11, 12),
            bcis=[bci.id],
            estimated_sms_cost=1.1,
        )
        url = f'/business_api/me/businesses/{self.business.id}/message_blast/scheduled/'
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['message_blasts'] is not None
        assert len(resp.json['message_blasts']) == 1
        mb_outcome = resp.json['message_blasts'][0]
        assert message_blast.id == mb_outcome['id']
        assert message_blast.title == mb_outcome['title'] == 'title'
        assert mb_outcome['schedule_date'] == 'Nov 12, 2021'
        assert len(message_blast.bcis) == mb_outcome['recipients_count'] == 1
        assert mb_outcome['estimated_sms_cost'] == 1.1

    def test_delete(self):
        blast = baker.make(
            MessageBlast,
            business=self.business,
            sent=False,
            scheduled_date=date(2021, 11, 12),
        )
        url = f'/business_api/me/businesses/{self.business.id}/message_blast/scheduled/{blast.id}'
        resp = self.fetch(url, method='DELETE')

        assert resp.code == 200
        assert MessageBlast.objects.all().count() == 0


@pytest.mark.django_db
class MessageBlastStopCodeHandlerTests(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()

        self.url = f'/business_api/me/businesses/{self.business.id}/message_blast/stop_code/'

    def test_get_stop_code(self):
        with override_settings(API_COUNTRY=Country.FR):
            resp = self.fetch(self.url, method='GET')
            assert resp.code == status.HTTP_200_OK
            assert resp.json['stop_code'] == BLAST_SMS_FR_SHORTCODE

        resp = self.fetch(self.url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['stop_code'] is None


@pytest.mark.django_db
class TestBlastSendTypeInNotificationHistory(BaseAsyncHTTPTest):
    def setUp(self):
        super().setUp()
        update_message_blast_groups()
        create_templates()

        self.business.sms_notification_status = Business.SMSStatus.ENABLED
        self.business.save()

    @staticmethod
    def get_data(**kwargs):
        return {
            'title': 'bb',
            'body': 'ccc',
            'body_short': 'ddd',
            'image': None,
            'recipients': {'group': BCIGroupName.ALL_CUSTOMERS},
            **kwargs,
        }

    def test_automatic_blast_message(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+*********05',
        )
        bci.reindex(refresh_index=True)

        common_template = CommonMessageBlastTemplate.objects.filter_baseclass().get(
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT
        )
        message_blast_template = baker.make(
            MessageBlastTemplate,
            common_template=common_template,
            group=common_template.group,
            internal_name=common_template.internal_name,
            business=self.business,
        )
        message_blast = baker.make(
            MessageBlast,
            template=message_blast_template,
            bcis=[bci.id],
            with_agreements=True,
            body='test bod',
            body_short='2345',
            business=self.business,
        )

        send_message_blast_lazy.delay(message_blast_id=message_blast.id)
        notification_history = NotificationHistory.objects.filter(
            task_id=f'MessageBlastNotification, {message_blast.id}',
        ).first()
        assert notification_history.blast_send_type == BlastSendType.AUTOMATIC
        NotificationHistoryDocument.refresh_index()
        notification_history_document = NotificationHistoryDocument.tasks_get(
            task_id=f'MessageBlastNotification, {message_blast.id}'
        )
        assert notification_history_document[0].blast_send_type == BlastSendType.AUTOMATIC

    def test_manual_test_blast_message(self):
        url = '/business_api/me/businesses/{}/message_blast/{}/one_time_send_test/'
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='***********',
        )
        bci.reindex(refresh_index=True)
        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )

        resp = self.fetch(
            path=url.format(self.business.id, common_template.id),
            method='POST',
            body=self.get_data(),
        )
        message_blast = MessageBlast.objects.get(id=resp.json['message_blast_id'])
        NotificationHistoryDocument.refresh_index()
        notification_history_document = NotificationHistoryDocument.tasks_get(
            task_id=f'TestMessageBlastNotification, {message_blast.id}'
        )
        assert notification_history_document[0].blast_send_type == BlastSendType.MANUAL

    def test_manual_blast_message(self):
        url = '/business_api/me/businesses/{}/message_blast/{}/one_time_send/'
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='***********',
        )
        bci.reindex(refresh_index=True)
        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )
        resp = self.fetch(
            path=url.format(self.business.id, common_template.id),
            method='POST',
            body=self.get_data(),
        )
        message_blast = MessageBlast.objects.get(id=resp.json['message_blast_id'])
        notification_history = NotificationHistory.objects.filter(
            task_id=f'MessageBlastNotification, {message_blast.id}'
        ).first()
        assert notification_history.blast_send_type == BlastSendType.MANUAL
        NotificationHistoryDocument.refresh_index()
        notification_history_document = NotificationHistoryDocument.tasks_get(
            task_id=f'MessageBlastNotification, {message_blast.id}'
        )
        assert notification_history_document[0].blast_send_type == BlastSendType.MANUAL

    @override_feature_flag({SMSMarketingRequiresConsentFlag.flag_name: True})
    def test_manual_blast_message_with_agreements_false_should_not_send_sms(self):
        url = '/business_api/me/businesses/{}/message_blast/{}/one_time_send/'
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='***********',
            email='<EMAIL>',
            web_communication_agreement=False,
        )
        bci.reindex(refresh_index=True)
        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )
        data = self.get_data()
        data['recipients']['with_agreements'] = False

        resp = self.fetch(
            path=url.format(self.business.id, common_template.id),
            method='POST',
            body=data,
        )
        message_blast = MessageBlast.objects.get(id=resp.json['message_blast_id'])
        assert not NotificationHistory.objects.filter(
            type=UserNotification.SMS_NOTIFICATION
        ).exists()

        notification_documents = NotificationHistoryDocument.tasks_get(
            task_id=f'MessageBlastNotification, {message_blast.id}'
        )
        assert len(notification_documents) == 1
        assert notification_documents[0].type == UserNotification.EMAIL_NOTIFICATION

    @override_feature_flag({SMSMarketingRequiresConsentFlag.flag_name: True})
    @override_settings(API_COUNTRY=Country.CA)
    def test_manual_blast_message_with_agreements_false_ca_should_not_send_sms(self):
        url = '/business_api/me/businesses/{}/message_blast/{}/one_time_send/'
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='***********',
            email='<EMAIL>',
            web_communication_agreement=False,
        )
        bci.reindex(refresh_index=True)
        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )
        data = self.get_data()
        data['recipients']['with_agreements'] = False

        resp = self.fetch(
            path=url.format(self.business.id, common_template.id),
            method='POST',
            body=data,
        )
        message_blast = MessageBlast.objects.get(id=resp.json['message_blast_id'])
        assert not NotificationHistory.objects.filter(
            type=UserNotification.SMS_NOTIFICATION
        ).exists()

        notification_documents = NotificationHistoryDocument.tasks_get(
            task_id=f'MessageBlastNotification, {message_blast.id}'
        )
        assert len(notification_documents) == 1
        assert notification_documents[0].type == UserNotification.EMAIL_NOTIFICATION

    @override_feature_flag({SMSMarketingRequiresConsentFlag.flag_name: True})
    @override_settings(API_COUNTRY=Country.PL)
    def test_manual_blast_message_with_agreements_false_pl_should_send_sms(self):
        url = '/business_api/me/businesses/{}/message_blast/{}/one_time_send/'
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='+**************',
            web_communication_agreement=False,
        )
        bci.reindex(refresh_index=True)
        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )
        data = self.get_data()
        data['recipients']['with_agreements'] = False

        resp = self.fetch(
            path=url.format(self.business.id, common_template.id),
            method='POST',
            body=data,
        )
        assert resp.code == status.HTTP_201_CREATED
        assert NotificationHistory.objects.filter(type=UserNotification.SMS_NOTIFICATION).exists()

    @override_feature_flag({SMSMarketingRequiresConsentFlag.flag_name: True})
    def test_manual_test_blast_message_with_agreements_false_should_not_send_sms(self):
        url = '/business_api/me/businesses/{}/message_blast/{}/one_time_send_test/'
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            cell_phone='***********',
            email='<EMAIL>',
            web_communication_agreement=False,
        )
        bci.reindex(refresh_index=True)
        common_template = (
            CommonMessageBlastTemplate.objects.filter_baseclass()
            .filter(
                group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
                link_to_group__isnull=True,
            )
            .first()
        )
        data = self.get_data()
        data['recipients']['with_agreements'] = False

        resp = self.fetch(
            path=url.format(self.business.id, common_template.id),
            method='POST',
            body=data,
        )
        message_blast = MessageBlast.objects.get(id=resp.json['message_blast_id'])
        assert not NotificationHistory.objects.filter(
            type=UserNotification.SMS_NOTIFICATION
        ).exists()

        notification_documents = NotificationHistoryDocument.tasks_get(
            task_id=f'TestMessageBlastNotification, {message_blast.id}'
        )
        assert len(notification_documents) == 1
        assert notification_documents[0].type == UserNotification.EMAIL_NOTIFICATION


@pytest.mark.django_db
@pytest.mark.parametrize(
    "date_month,date_day,date_monday,common_date_type,date_type",
    [
        (11, None, 2, MessageBlastDateType.FIRST_MONDAY, MessageBlastDateType.STRICT),
        (11, 1, None, MessageBlastDateType.STRICT, MessageBlastDateType.FIRST_MONDAY),
    ],
)
def test_valid_dates(
    setup_templates_and_blasts_data,
    configure_templates,
    date_month,
    date_day,
    date_monday,
    common_date_type,
    date_type,
):
    configure_templates(date_month, date_day, date_monday, common_date_type, date_type)

    with freeze_time(datetime(2021, 11, 30, 16, tzinfo=UTC)):
        results = reset_to_default_message_blast_templates_task()
        assert 'holiday_booking' not in results['templates_reset']

    setup_templates_and_blasts_data["message_blast"].sent = True
    setup_templates_and_blasts_data["message_blast"].save()

    with freeze_time(datetime(2021, 12, 2, 10, tzinfo=UTC)):
        results = reset_to_default_message_blast_templates_task()
        assert results['templates_reset']['holiday_booking'] == 1

        setup_templates_and_blasts_data["message_blast_template"].refresh_from_db()
        setup_templates_and_blasts_data["common_template"].refresh_from_db()

        assert (
            setup_templates_and_blasts_data["message_blast_template"].date_type
            == setup_templates_and_blasts_data["common_template"].date_type
            == common_date_type
        )
        assert (
            setup_templates_and_blasts_data["message_blast_template"].date_month
            == setup_templates_and_blasts_data["common_template"].date_month
            == date_month
        )
        assert (
            setup_templates_and_blasts_data["message_blast_template"].date_day
            == setup_templates_and_blasts_data["common_template"].date_day
            == date_day
        )
        assert (
            setup_templates_and_blasts_data["message_blast_template"].date_monday
            == setup_templates_and_blasts_data["common_template"].date_monday
            == date_monday
        )
