import calendar
import datetime
import logging
import typing as t
from copy import deepcopy

import pytz
import tornado.web
from bo_obs.datadog.enums import BooksyTeams
from bo_obs.datadog.enums import DatadogCustomServices, DatadogOperationNames
from bo_obs.datadog.mixins import set_apm_tag_in_current_span, MANUAL_KEEP_KEY
from dateutil.tz import tzfile
from ddtrace import tracer
from django.conf import settings
from django.core.paginator import (
    EmptyPage,
    Paginator,
)
from django.db.models import (
    Count,
    Prefetch,
    Q,
)
from django.db.models.functions import (
    TruncDay,
    TruncMonth,
)
from django.utils.encoding import force_str
from django.utils.translation import activate, gettext as _
from rest_framework import (
    serializers,
    status,
)

import lib.tools
from lib.db import (
    READ_ONLY_DB,
    build_search_query,
    using_db_for_reads,
)
from lib.feature_flag.feature import SendSMSNotificationWithPaymentLinkFlag
from lib.feature_flag.feature.payment import (
    EditTransactionWithLockFlag,
    PaymentRowSummaryUseReadReplicaFlag,
)
from lib.locks import (
    AbstractLock,
    BusinessTransactionActionLock,
    EditTransactionLock,
    UserPosTransactionLock,
)
from lib.payment_providers.entities import DeviceDataEntity, FraudPreventionAuthAdditionalDataEntity
from lib.payments.utils import get_nethone_attempt_reference
from lib.point_of_sale.enums import BasketPaymentAnalyticsTrigger, PaymentMethodType
from lib.pos.utils import txn_refactor_stage2_enabled
from lib.serializers import (
    PaginatorSerializer,
    SendEmailRequest,
)
from lib.tools import (
    firstof,
    sget,
)
from service.pos.tools import (
    BasePOSHandler,
    PaymentRowMixin,
)
from service.tools import (
    DryRunMixin,
    RequestHandler,
    RequestLockMixin,
    json_request,
    session,
)
from webapps import consts
from webapps.adyen.typing import DeviceDataDict
from webapps.booking.enums import WhoMakesChange
from webapps.booking.models import Appointment
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.family_and_friends.factory import (
    create_member_transaction_for_family_and_friends_appointment,
)
from webapps.french_certification.services import SellerService
from webapps.kill_switch.models import KillSwitch
from webapps.notification.models import NotificationHistory
from webapps.notification.push import notification_receivers_list
from webapps.notification.tasks.push import send_push_notification
from webapps.pos import enums
from webapps.pos.enums import (
    CARD_TYPE__BLIK,
    CARD_TYPE__KEYED_IN_PAYMENT,
    compatibilities,
    PaymentProviderEnum,
    PaymentRowsSummaryScopes,
    PaymentTypeEnum,
    receipt_status,
)
from webapps.pos.enums.receipt_status import (
    NEGATIVE_VALUE_STATUSES,
    STATUSES_WITHOUT_MONEY,
)
from webapps.pos.exceptions import RefundNotPossibleException
from webapps.pos.models import (
    POS,
    PaymentMethod,
    PaymentRow,
    PaymentRowChange,
    PaymentRowQuerySet,
    PaymentType,
    Transaction,
)
from webapps.pos.notifications import SMSWithPaymentLinkNotification
from webapps.pos.provider import get_payment_provider
from webapps.pos.provider.proxy import ProxyProvider
from webapps.pos.refund import do_refund
from webapps.pos.serializers import (
    BusinessCashFlowSerializer,
    BusinessTransactionActionRequest,
    PaymentRowListingSerializer,
    PaymentRowsSummarySerializer,
    ReceiptDetailsSerializer,
    TransactionSerializer,
)
from webapps.pos.services import (
    PaymentRowService,
    TransactionService,
)
from webapps.pos.tasks import SendReceiptToCustomer
from webapps.pos.tools import (
    RECEIPT_STATUS_FILTERS,
    TRANSACTION_FILTERS,
    get_business_cash_flow_objects,
    get_transaction_counts_by_id,
)
from webapps.pos.utils import check_seller
from webapps.register.models import Register
from webapps.register.serializers import RegisterListingSerializer
from webapps.search_engine_tuning.tasks import update_business_customer_tunings_params_task
from webapps.turntracker.helpers import (
    create_finish_turns,
    get_subbookings_with_succeded_payment,
)
from webapps.user.tools import get_system_user
from webapps.voucher.models import VoucherTemplate

log = logging.getLogger('business_transactions')


class GetBusinessTransactionsRequestSerializer(PaginatorSerializer):
    booking_id = serializers.IntegerField(required=False)
    customer_card_id = serializers.IntegerField(required=False)
    voucher_id = serializers.IntegerField(required=False)
    query = serializers.CharField(required=False, allow_blank=True)
    payment_type = serializers.ChoiceField(
        choices=PaymentTypeEnum.choices(),
        default=None,
        required=False,
    )
    transaction_type = serializers.ChoiceField(
        choices=sorted(TRANSACTION_FILTERS),
        default='all',
        required=False,
    )
    status_type = serializers.ChoiceField(
        choices=RECEIPT_STATUS_FILTERS,
        default='all',
        required=False,
    )
    status_type_exclude = serializers.ChoiceField(
        choices=RECEIPT_STATUS_FILTERS,
        default=None,
        required=False,
    )
    date_from = serializers.DateField(required=False, allow_null=True)
    date_till = serializers.DateField(required=False, allow_null=True)

    @staticmethod
    def validate_query(query):
        return None if query == '' else query

    def validate(self, attrs):
        tz = self.context['tz']
        for field in ['from', 'till']:
            if ('date_' + field) in attrs:
                diff = datetime.timedelta(days=int(field == 'till'))
                attrs['datetime_' + field] = datetime.datetime.combine(
                    attrs['date_' + field] + diff,
                    datetime.time(),
                ).replace(tzinfo=tz)
        return super(self.__class__, self).validate(attrs)


class BaseAutoPayHandler:

    @staticmethod
    def get_blik_code(pos, data) -> str | None:
        if (
            settings.POS__BLIK
            and sget(data, ['external_payment_method', 'partner']) == PaymentMethodType.BLIK
        ):
            return sget(data, ['external_payment_method', 'token'])

    def auto_accept_pay_by_app(
        self,
        pos: POS,
        transaction: Transaction,
        device_data: DeviceDataDict,
    ) -> Transaction:
        return self.capture_payment_auto_accept(
            pos=pos,
            transaction=transaction,
            device_data=device_data,
            receipt_status_code=receipt_status.CALL_FOR_PAYMENT,
            payment_type_code=PaymentTypeEnum.PAY_BY_APP,
        )

    def auto_accept_business_prepayment(
        self,
        pos: POS,
        transaction: Transaction,
        device_data: DeviceDataDict,
    ) -> Transaction:
        return self.capture_payment_auto_accept(
            pos=pos,
            transaction=transaction,
            device_data=device_data,
            receipt_status_code=receipt_status.CALL_FOR_PREPAYMENT,
            payment_type_code=PaymentTypeEnum.PREPAYMENT,
        )

    @staticmethod
    def capture_blik_payment(
        blik_code: str,
        pos: POS,
        transaction: Transaction,
        device_data: DeviceDataDict,
        receipt_status_code: str,
    ):
        payment_method = PaymentMethod(
            provider=PaymentProviderEnum.STRIPE_PROVIDER,
            card_type=CARD_TYPE__BLIK,
        )
        payment_method.token = blik_code

        blik_payment_row = transaction.latest_receipt.payment_rows.filter(
            status=receipt_status_code,
            payment_type__code=PaymentTypeEnum.BLIK,
        ).get()

        provider_code = TransactionService.get_payment_provider_code(
            pos=pos,
            payment_type_code=PaymentTypeEnum.BLIK,
            txn=transaction,
        )
        provider = get_payment_provider(
            codename=provider_code,
            txn=transaction,
        )

        provider.make_payment(
            transaction,
            payment_method=payment_method,
            payment_row=blik_payment_row,
            device_data=device_data,
            trigger=BasketPaymentAnalyticsTrigger.BUSINESS__BLIK,
        )

    def auto_accept_blik(
        self,
        blik_code: str,
        pos: POS,
        transaction: Transaction,
        device_data: DeviceDataDict,
    ):
        return self.capture_blik_payment(
            blik_code=blik_code,
            pos=pos,
            transaction=transaction,
            device_data=device_data,
            receipt_status_code=receipt_status.CALL_FOR_PAYMENT,
        )

    @staticmethod
    def capture_payment_auto_accept(
        pos: POS,
        transaction: Transaction,
        device_data: DeviceDataDict,
        receipt_status_code: str,
        payment_type_code: PaymentTypeEnum,
    ) -> Transaction:
        # There can be only 1 pba row with CFP status
        pba_row = transaction.latest_receipt.payment_rows.filter(
            status=receipt_status_code,
            payment_type__code=payment_type_code,
        ).first()

        business_payment_provider_code = (
            PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
                TransactionService.get_payment_provider_code(
                    pos=pos,
                    payment_type_code=payment_type_code,
                    txn=transaction,
                )
            )
        )

        if (
            pos.payment_auto_accept
            and pba_row
            and transaction.latest_receipt.status_code == receipt_status_code
            and transaction.customer
            and transaction.customer.is_payment_auto_accept_possible
        ):
            payment_method = transaction.customer.payment_methods.filter(
                default=True,
                provider=business_payment_provider_code,
            ).first()

            if payment_method is not None:
                provider = get_payment_provider(
                    codename=payment_method.provider,
                    txn=transaction,
                )
                return provider.make_payment(
                    transaction,
                    payment_method=payment_method,
                    payment_row=pba_row,
                    device_data=device_data,
                    trigger=BasketPaymentAnalyticsTrigger.BUSINESS__MOBILE_PAYMENT_AUTO_ACCEPT,
                )

    def auto_accept_square_from_web(self, transaction: Transaction) -> None:
        # There can be only 1 square row with PENDING status
        square_row = (
            transaction.latest_receipt.payment_rows.filter(
                status=receipt_status.PENDING, payment_type__code=PaymentTypeEnum.SQUARE
            )
            .select_related('payment_type')
            .first()
        )

        if square_row and self.booking_source.name == consts.WEB:
            square_row.update_status(
                receipt_status.PAYMENT_SUCCESS,
                log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
                log_note='Square auto accept from Web',
            )


class BaseNotificationHandler:
    def send_transaction_notifications(self, transaction: Transaction) -> None:
        self.send_sms_with_payment_link_if_needed(transaction)
        self.send_call_for_payment_push_if_needed(transaction)

    @staticmethod
    def send_call_for_payment_push_if_needed(transaction: Transaction) -> None:
        if (
            transaction is None
            or transaction.customer is None
            or transaction.latest_receipt is None
            or transaction.latest_receipt.status_code != receipt_status.CALL_FOR_PAYMENT
            or transaction.pos.business.customer_notifications_disabled
            or transaction.payment_rows.filter(payment_type__code=PaymentTypeEnum.BLIK).exists()
        ):
            return

        if (
            txn_refactor_stage2_enabled(transaction)
            and transaction.customer.is_payment_auto_accept_possible
        ):  # TODO temp solution for https://booksy.atlassian.net/browse/POS-839, https://booksy.atlassian.net/browse/POS-806
            return

        booking = transaction.rows.filter(subbooking_id__isnull=False).first()
        receivers = (
            notification_receivers_list(user_id=transaction.customer.id)
            if transaction.customer is not None
            else []
        )

        from webapps.user.models import UserProfile

        customer_profile = UserProfile.objects.filter(
            user=transaction.customer,
            profile_type=UserProfile.Type.CUSTOMER,
        ).first()
        if customer_profile:
            activate(customer_profile.language)

        send_push_notification(
            receivers=receivers,
            alert=_("Please finish your payment"),
            target=('transaction.call_for_payment', transaction.id),
            history_data={
                'booking_id': None if booking is None else booking.id,
                'business_id': transaction.pos.business_id,
                'customer_id': transaction.customer_id,
                'customer_card_id': transaction.customer_card_id,
                'sender': NotificationHistory.SENDER_BUSINESS,
                'task_id': 'transaction:call_for_payment:transaction_id=%d' % (transaction.id,),
            },
        )

    @staticmethod
    def send_sms_with_payment_link_if_needed(transaction: Transaction):
        if (
            not SendSMSNotificationWithPaymentLinkFlag()
            or sget(transaction, ['pos', 'business', 'customer_notifications_disabled'])
            or sget(transaction, ['latest_receipt', 'status_code'])
            != receipt_status.CALL_FOR_PAYMENT
            or transaction.payment_rows.filter(payment_type__code=PaymentTypeEnum.BLIK).exists()
        ):
            return

        if transaction.customer:
            return SMSWithPaymentLinkNotification(transaction_id=transaction.id).send()

        return


class BaseTransactionHandlerMixin(RequestLockMixin):

    def get_pos_for_business(
        self,
        business_id,
        prefetch_bci_ids=None,
        prefetch_voucher_template_ids=None,
    ):
        """Returns POS instance."""
        if not settings.POS:
            raise tornado.web.HTTPError(404)

        qs = POS.objects.select_related(
            'business',
        ).prefetch_related(
            'tips',
            'tax_rates',
            'commission_defaults',
            Prefetch(
                'payment_types',
                queryset=PaymentType.all_objects.all(),
            ),
        )
        if prefetch_voucher_template_ids:
            qs = qs.prefetch_related(
                Prefetch(
                    'voucher_templates',
                    queryset=VoucherTemplate.objects.filter(
                        id__in=prefetch_voucher_template_ids,
                    ),
                    to_attr='prefetched_voucher_templates',
                ),
            )
        if prefetch_bci_ids:
            qs = qs.prefetch_related(
                Prefetch(
                    'business__business_customer_infos',
                    queryset=BusinessCustomerInfo.objects.filter(
                        id__in=prefetch_bci_ids,
                    ),
                    to_attr='prefetched_business_customer_infos',
                )
            )
        return self.get_object_or_404(qs, business_id=business_id, active=True)

    def acquire_transaction_lock(
        self,
        lock_id: int,
        lock_class: t.Type[AbstractLock],
    ):
        """Locks transaction to prevent duplicated transactions in POS.

        We've experienced a race condition between multiple HTTP requests
        creating transactions for bookings, which resulted in duplicated
        transactions in POS.

        To prevent this: if current request is not a dry run we setup a time
        expiring lock on the user. If acquiring the lock fails that means that
        another request from the same user is already being processed, so we
        drop the request that could not acquire the lock.
        """
        return self._acquire_lock(
            lock_id,
            lock_class,
            _('Could not create transaction'),
        )

    @staticmethod
    def set_legacy_value_to_default(register_id):
        """Sets None if front provided -1. -1 is not supported now."""
        return register_id if register_id != -1 else None

    def select_register(self):
        """Returns register for transaction."""

        selected_register_id = self.set_legacy_value_to_default(
            self.data.get('selected_register_id', None)
        )

        return selected_register_id


def modify_invalid_payload_from_ios(data):
    if (
        data.get('booking')
        and data.get('bookings')
        and (len(data['bookings']) > 1 or data['bookings'][0]['booking_id'] != data['booking'])
    ):
        data['multibooking'] = data.pop('booking')


class BusinessTransactionsHandler(
    DryRunMixin,
    RequestHandler,
    BaseTransactionHandlerMixin,
    BaseNotificationHandler,
    BaseAutoPayHandler,
):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @session(login_required=True, api_key_required=True)
    @json_request
    def post(self, business_id):
        """
        swagger:
            summary: Create Transaction
            notes: >
                ->POS-T01U.<br/>
                <b>bookings</b> field should contain one or more bookings.
                For multibooking request specify all bookings belonging
                to multibooking.
                <br/>
                Transaction specific fields (all optional):
                <ul>
                    <li><b>products</b></li>
                    <li><b>tip</b></li>
                    <li><b>payment_type_code</b></li>
                </ul>
                <br/>Note: for deposit transaction see deposits api.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: body
                  description: Business Create Transaction Data
                  type: BusinessCreateTransactionRequest
                  paramType: body
            type: TransactionDetailsResponse
        :swagger
        """

        # We've experienced a race condition between multiple HTTP requests
        # creating transactions for bookings, which resulted in duplicated
        # transactions in POS.
        #
        # To prevent this: if current request is not a dry run we setup a time
        # expiring lock on the user. If acquiring the lock fails that means that
        # another request from the same user is already being processed, so we
        # drop the request that could not acquire the lock.
        self.set_dry_run_from_params()
        if not self.data['dry_run'] and self.user and self.user.external_api_id:
            self.acquire_transaction_lock(
                lock_id=self.user.external_api_id,
                lock_class=UserPosTransactionLock,
            )

        SellerService.verify_seller_data(business_id=business_id)

        # TODO: remove this hack when fix from 65743 will be done
        modify_invalid_payload_from_ios(self.data)

        voucher_customer_ids = {
            v['voucher_customer']['id']
            for v in self.data.get('vouchers', [])
            if v.get('voucher_customer')
        }
        voucher_template_ids = {
            v['voucher_template_id']
            for v in self.data.get('vouchers', [])
            if v.get('voucher_template_id')
        }
        pos = self.get_pos_for_business(
            business_id,
            prefetch_bci_ids=voucher_customer_ids,
            prefetch_voucher_template_ids=voucher_template_ids,
        )
        self.business_with_advanced_staffer(pos.business)

        selected_register_id = self.select_register()

        access_level = getattr(self, 'access_level', None)
        serializer = TransactionSerializer(
            data=self.data,
            context={
                'access_level': access_level,
                'business': pos.business,
                'compatibilities': self.data.get('compatibilities', {}),
                'device_data': DeviceDataEntity(
                    device_fingerprint=self.fingerprint,
                    phone_number=self.user.cell_phone,
                    user_agent=self.user_agent,
                    ip=self.forwarded_ip,
                ),
                'dry_run': self.data.get('dry_run', False),
                'is_frontdesk': self.is_frontdesk,
                'biz_endpoint': True,
                'operator': self.user,
                'park_sale': self.data.get('park_sale', False),
                'pos': pos,
                'selected_register_id': selected_register_id,
            },
        )
        validated_data = self.validate_serializer(serializer)

        if validated_data['dry_run']:
            register = pos.get_open_register(
                selected_register_id=selected_register_id,
                operator=self.user,
                takeover_when_only_one_is_open=self.is_frontdesk,
            )

            # Use bulk fetch for only one register to make 1 query instead of 9
            if register is not None:
                Register.bulk_fetch_operation_totals([register])

            return self.finish_with_json(
                201,
                {
                    'transaction': serializer.data,
                    'register': (
                        RegisterListingSerializer(
                            instance=register,
                            context={'pos': pos},
                        ).data
                        if register
                        else None
                    ),
                },
            )

        transaction = serializer.save()
        create_member_transaction_for_family_and_friends_appointment(transaction, use_parent=False)
        if self.data.get('from_turntracker', None):
            subbookings = transaction.appointment.subbookings
            subbookings_with_finish_turns = list(
                get_subbookings_with_succeded_payment(transaction, business_id, subbookings)
            )
            create_finish_turns(business_id, subbookings_with_finish_turns)

        device_data = DeviceDataDict(
            fingerprint=self.fingerprint,
            phone_number=self.user.cell_phone,
            user_agent=self.user_agent,
        )
        if blik_code := self.get_blik_code(pos, self.data):
            self.auto_accept_blik(
                blik_code=blik_code,
                pos=pos,
                transaction=transaction,
                device_data=device_data,
            )
        else:
            self.auto_accept_pay_by_app(
                pos,
                transaction,
                device_data,
            )

        # HACK AUTO-ACCEPT PAYMENT FROM WEB >>>
        self.auto_accept_square_from_web(transaction)

        # serializer.data must not been called before instance assignment!
        # Use query to prefetch_all
        serializer.instance = Transaction.objects.filter(id=transaction.id).prefetch_all().first()

        register = pos.get_open_register(
            selected_register_id=selected_register_id,
            operator=self.user,
            takeover_when_only_one_is_open=self.is_frontdesk,
        )

        # Use bulk fetch for only one register to make 1 query instead of 9
        if register is not None:
            Register.bulk_fetch_operation_totals([register])

        # <editor-fold desc="early_finish">
        self.send_transaction_notifications(serializer.instance)

        if transaction.customer_card_id is not None:
            update_business_customer_tunings_params_task.delay([transaction.customer_card_id])
        # </editor-fold>

        return self.finish_with_json(
            201,
            {
                'transaction': serializer.data,
                'register': (
                    RegisterListingSerializer(
                        instance=register,
                        context={
                            'pos': pos,
                        },
                    ).data
                    if register
                    else None
                ),
            },
        )

    @session(login_required=True, api_key_required=True)
    @using_db_for_reads(
        database_name=READ_ONLY_DB,
        condition=lambda: KillSwitch.alive(KillSwitch.System.REPLICA_POS_TRANSACTIONS),
    )
    def get(self, business_id):
        """
        swagger:
            summary: Get list of Transactions
            notes: >
                POS-D1 POS-D2
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: booking_id
                  type: integer
                  required: False
                  paramType: query
                  description: optionally filter by SubBooking ID
                - name: customer_card_id
                  type: integer
                  required: False
                  paramType: query
                  description: optionally filter by Customer Card ID
                - name: transaction_type
                  description: filter transactions
                  type: string
                  paramType: query
                  defaultValue: all
                  enum_from_const: webapps.pos.tools.TRANSACTION_FILTERS
                - name: status_type
                  description: filter transactions by latest_receipt status
                  type: string
                  paramType: query
                  defaultValue: all
                  enum_from_const: webapps.pos.tools.RECEIPT_STATUS_FILTERS
                - name: status_type_exclude
                  description: exclude transactions by latest_receipt status
                  type: string
                  paramType: query
                  enum_from_const: webapps.pos.tools.RECEIPT_STATUS_FILTERS
                - name: query
                  description: filter transactions by text matching
                  type: string
                  paramType: query
                - name: date_from
                  description: only bookings <tt>latest_receipt.created >= date_from</tt>
                  type: string
                  paramType: query
                - name: date_till
                  description: |
                      only bookings <tt>latest_receipt.created &lt; date_till</tt>
                      automaticly convert date to full day (adds a day)
                  type: string
                  paramType: query
                - name: page
                  description: Results page
                  paramType: query
                  type: integer
                  defaultValue: 1
                  minimum: 1
                - name: per_page
                  description: how many transactions per page to return
                  paramType: query
                  type: integer
                  minimum: 0
                  defaultValue: 20
                  maximum: 1000
            type: SearchTransactionsResponse
        """
        # validate POS is activated
        pos = self.get_pos_for_business(business_id=business_id)
        self.business_with_advanced_staffer(pos.business)
        data = self._prepare_get_arguments()
        serializer = GetBusinessTransactionsRequestSerializer(
            data=data,
            context={'tz': pos.business.get_timezone()},
        )
        data = self.validate_serializer(serializer)

        transactions_qs = pos.transactions.all()

        if data.get('booking_id'):
            transactions_qs = transactions_qs.filter(
                rows__subbooking_id=data['booking_id'],
            )
        if data.get('customer_card_id'):
            transactions_qs = transactions_qs.filter(
                customer_card_id=data['customer_card_id'],
            )
        if data.get('voucher_id'):
            transactions_qs = transactions_qs.filter(
                latest_receipt__payment_rows__voucher_id=data['voucher_id']
            )

        if data.get('status_type', 'all') != 'all':
            transactions_qs = transactions_qs.filter(
                latest_receipt__status_code__in=receipt_status.STATUS_TYPES[data['status_type']]
            )

            if data.get('status_type') == 'nav_unfinished':
                # 43742. Show only prepayments connected to finished bookings.
                transactions_qs = transactions_qs.filter(
                    ~Q(latest_receipt__status_code=receipt_status.PREPAYMENT_SUCCESS)
                    | Q(appointment__status=Appointment.STATUS.FINISHED)
                )

        if 'datetime_from' in data:
            transactions_qs = transactions_qs.filter(
                latest_receipt__created__gte=data['datetime_from']
            )
        if 'datetime_till' in data:
            transactions_qs = transactions_qs.filter(
                latest_receipt__created__lt=data['datetime_till']
            )

        if 'transaction_id' in data:
            transactions_qs = transactions_qs.filter(id__contains=data['transaction_id'])
        if data.get('query') is not None:
            transactions_qs = transactions_qs.filter(
                build_search_query(
                    data.get('query'),
                    [
                        'customer_data',
                        'rows__subbooking__service_name',
                        'rows__service_variant__service__name',
                        'id',
                    ],
                )
            )
        #
        # # filter edited transactions
        transaction_ids = list(
            transactions_qs.filter(children__isnull=True)
            .distinct('id')
            .values_list('id', flat=True)
        )
        transaction_counts = get_transaction_counts_by_id(transaction_ids)

        transactions_qs = pos.transactions.filter(id__in=transaction_ids).prefetch_all()
        filtered_transactions = transactions_qs.filter(
            TRANSACTION_FILTERS[data['transaction_type']]
        ).order_by('-latest_receipt__created')

        access_level = getattr(self, 'access_level', None)
        response = {
            'count': transaction_counts[data['transaction_type']],
            'page': data['page'],
            'per_page': data['per_page'],
            'transactions': TransactionSerializer(
                instance=filtered_transactions[data['offset'] : data['limit']],
                many=True,
                context={
                    'access_level': access_level,
                    'pos': pos,
                },
            ).data,
            'transaction_counts': transaction_counts,
        }

        return self.finish(response)


class BusinessTransactionDetailsHandler(
    DryRunMixin,
    RequestHandler,
    BaseTransactionHandlerMixin,
    BaseNotificationHandler,
    BaseAutoPayHandler,
):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT, BooksyTeams.PAYMENT_PROCESSING)

    @session(login_required=True, api_key_required=True)
    @using_db_for_reads(
        database_name=READ_ONLY_DB,
        condition=lambda: KillSwitch.alive(KillSwitch.System.REPLICA_POS_TRANSACTION_DETAILS),
    )
    def get(self, business_id, transaction_id):
        """
        swagger:
            summary: Get POS Transaction Details
            notes: >
                .
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: transaction_id
                  type: integer
                  paramType: path
                  description: Transaction id
            type: TransactionDetailsResponse
        """
        pos = self.get_pos_for_business(business_id=business_id)
        self.business_with_advanced_staffer(pos.business)

        transaction = self.get_object_or_404(
            Transaction.objects.prefetch_all(),
            id=transaction_id,
            pos=pos,
        )

        register = transaction.register
        # Use bulk fetch for only one register to make 1 query instead of 9
        if register:
            Register.bulk_fetch_operation_totals([register])

        access_level = getattr(self, 'access_level', None)
        ctx = {
            'access_level': access_level,
            'force_tip_choices': pos.tips_enabled,
            'operator': self.user,
            'pos': pos,
        }

        response = {
            'transaction': TransactionSerializer(
                instance=transaction,
                context=ctx,
            ).data,
            'register': (
                RegisterListingSerializer(
                    instance=register,
                    context={'pos': pos},
                ).data
                if register
                else None
            ),
        }
        return self.finish(response)

    @session(login_required=True, api_key_required=True)
    @json_request
    def put(self, business_id, transaction_id):
        """
        swagger:
            summary: Update Transaction
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: transaction_id
                  type: integer
                  paramType: path
                  description: Transaction id
                - name: body
                  description: Business Create Transaction Data
                  type: BusinessCreateTransactionRequest
                  paramType: body
            type: TransactionDetailsResponse
        :swagger
        """
        if not self.data.get('dry_run') and EditTransactionWithLockFlag():
            self._acquire_lock(
                lock_id=transaction_id,
                lock_class=EditTransactionLock,
                error_description='Could not edit transaction',
            )

        # TODO: remove this hack when fix from 65743 will be done
        self.set_dry_run_from_params()
        modify_invalid_payload_from_ios(self.data)
        data = self.data
        pos = self.get_pos_for_business(business_id=business_id)
        self.business_with_advanced_staffer(pos.business)

        old_txn = self.get_object_or_404(
            Transaction.objects.prefetch_all(),
            id=transaction_id,
            pos=pos,
        )
        data['parent_txn'] = old_txn.id
        data['charge_date'] = old_txn.charge_date

        status_code = old_txn.latest_receipt.status_code
        edit = True if status_code not in receipt_status.FAKE_EDIT_STATUSES else False

        selected_register_id = self.select_register()

        access_level = getattr(self, 'access_level', None)
        serializer = TransactionSerializer(
            data=data,
            context={
                'access_level': access_level,
                'business': pos.business,
                'compatibilities': self.data.get('compatibilities', {}),
                'device_data': DeviceDataEntity(
                    device_fingerprint=self.fingerprint,
                    phone_number=self.user.cell_phone,
                    user_agent=self.user_agent,
                    ip=self.forwarded_ip,
                ),
                'edit': edit,
                'edit_screen': True,
                'old_txn': old_txn,
                'operator': self.user,
                'is_frontdesk': self.is_frontdesk,
                'biz_endpoint': True,
                'park_sale': self.data.get('park_sale', False),
                'pos': pos,
                'selected_register_id': selected_register_id,
            },
        )
        self.validate_serializer(serializer)

        if not serializer.validated_data['dry_run']:
            if status_code not in [
                receipt_status.ARCHIVED,
                receipt_status.PARK_SALE,
                receipt_status.PREPAYMENT_SUCCESS,
                receipt_status.PREPAYMENT_FAILED,
                receipt_status.PAYMENT_CANCELED,
                receipt_status.GIFT_CARD_DEPOSIT,
                receipt_status.BOOKSY_PAY_SUCCESS,
                receipt_status.BOOKSY_PAY_FAILED,
            ]:
                old_txn.update_payment_rows(
                    status=receipt_status.ARCHIVED,
                    receipt_number=old_txn.latest_receipt.receipt_number,
                    log_action=PaymentRowChange.MULTI_ROW_UPDATE,
                    log_note='BusinessTransactionDetailsHandler - edit transaction',
                    operator=self.user,
                )
            transaction = serializer.save()
            data = serializer.data

        else:
            data = deepcopy(serializer.data)
            data['id'] = transaction_id

        register = old_txn.register
        # Use bulk fetch for only one register to make 1 query instead of 9
        if register:
            Register.bulk_fetch_operation_totals([register])

        resp = {
            'transaction': data,
            'register': (
                RegisterListingSerializer(
                    instance=register,
                    context={'pos': pos},
                ).data
                if register
                else None
            ),
        }
        # <editor-fold desc="early_finish section">
        if not serializer.validated_data['dry_run'] and not edit:
            device_data = DeviceDataDict(
                fingerprint=self.fingerprint,
                phone_number=self.user.cell_phone,
                user_agent=self.user_agent,
            )
            if blik_code := self.get_blik_code(pos, self.data):
                self.auto_accept_blik(
                    blik_code=blik_code,
                    pos=pos,
                    transaction=transaction,
                    device_data=device_data,
                )
            else:
                self.auto_accept_pay_by_app(
                    pos,
                    transaction,
                    device_data,
                )

            # HACK AUTO-ACCEPT PAYMENT FROM WEB >>>
            self.auto_accept_square_from_web(transaction)
            self.send_transaction_notifications(transaction)
        # </editor-fold>
        return self.finish_with_json(status.HTTP_200_OK, resp)

    @session(login_required=True, api_key_required=True)
    @json_request
    def delete(self, business_id, transaction_id):
        pos = self.get_object_or_404(
            POS.objects.select_related('business'),
            business_id=business_id,
            active=True,
        )
        self.business_with_manager(pos.business)

        txn = self.get_object_or_404(Transaction, id=transaction_id, pos=pos)
        payment_rows = txn.latest_receipt.payment_rows.all()
        can_delete = True

        if txn.lock or [x for x in payment_rows if x.locked]:
            can_delete = False

        if can_delete and txn.latest_receipt.status_code not in receipt_status.DELETABLE_STATUSES:
            can_delete = False

        lib.tools.sasrt(
            can_delete,
            400,
            [
                {
                    'field': 'latest_receipt.status_code',
                    'type': 'validation',
                    'description': _('Can\'t delete this transactions'),
                }
            ],
        )

        if txn.latest_receipt.status_code == receipt_status.PARK_SALE:
            # Park sale can be deleted. It is kind of draft.
            transaction_series = txn.series()
            for transaction in transaction_series:
                transaction.soft_delete()
        else:
            txn.update_payment_rows(
                receipt_status.ARCHIVED,
                receipt_number=txn.latest_receipt.receipt_number,
                log_action=PaymentRowChange.MULTI_ROW_UPDATE,
                log_note='BusinessTransactionActionHandler delete transaction',
                operator=self.user,
            )

        return self.finish_with_json(status.HTTP_200_OK, {})


class BusinessTransactionActionHandler(
    RequestHandler,
    BaseTransactionHandlerMixin,
):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @session(login_required=True, api_key_required=True)
    @json_request
    def post(self, business_id, transaction_id):
        """
        swagger:
            summary: Execute Transaction Action
            notes: >
                POS-CFP-01->
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business ID
                - name: transaction_id
                  type: integer
                  paramType: path
                  description: Transaction ID
                - name: body
                  description: Business Transaction Action Data
                  type: BusinessTransactionActionRequest
                  paramType: body
            type: TransactionActionResponse
        """
        self.acquire_transaction_lock(
            lock_id=transaction_id,
            lock_class=BusinessTransactionActionLock,
        )
        pos = self.get_pos_for_business(business_id=business_id)
        self.business_with_advanced_staffer(pos.business)

        transaction = self.get_object_or_404(
            Transaction.objects.prefetch_all(),
            id=transaction_id,
            pos=pos,
        )

        pr = transaction.latest_receipt.payment_rows.last()
        self.is_allowed_payment_type_code_or_raise(pr.payment_type)
        provider = (
            get_payment_provider(
                codename=pr.provider,
                txn=transaction,
            )
            if pr.provider
            else None
        )

        selected_register_id = self.select_register()

        request = BusinessTransactionActionRequest(
            data=self.data,
            instance=transaction,
            context={
                'pos': pos,
                'operator': self.user,
                'selected_register_id': selected_register_id,
                'is_frontdesk': self.is_frontdesk,
            },
        )
        data = self.validate_serializer(request)

        register = pos.get_open_register(
            operator=self.user,
            selected_register_id=selected_register_id,
            takeover_when_only_one_is_open=self.is_frontdesk,
        )

        if data['action'] == enums.BUSINESS_ACTION__SET_PAYMENT_STATUS:
            payment_row = transaction.latest_receipt.payment_rows.get(id=data['row_id'])
            payment_row.update_status(
                data['payment_status'],
                operator=self.user,
                log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
                log_note='BusinessTransactionActionHandler set payment status',
            )
            transaction = Transaction.objects.get(id=transaction.id)

        if data['action'] == enums.BUSINESS_ACTION__CANCEL_PAYMENT:
            result = TransactionService.action__cancel_payment(
                txn=transaction,
                log_note='BusinessTransactionActionHandler cancel payment',
            )

            self.quick_assert(
                result,
                ('not_valid', 'validation', 'action'),
                _('Transaction cannot be cancelled anymore'),
            )

        elif data['action'] == enums.BUSINESS_ACTION__CHARGE_DEPOSIT:
            provider.charge_deposit(
                transaction,
                operator=self.user,
                register=register,
                device_data=DeviceDataDict(
                    fingerprint=self.fingerprint,
                    phone_number=self.user.cell_phone,
                    user_agent=self.user_agent,
                ),
            )

        elif data['action'] == enums.BUSINESS_ACTION__CANCEL_DEPOSIT:
            provider.cancel_deposit(transaction)

        elif data['action'] == enums.BUSINESS_ACTION__RETRY_KIP:
            with tracer.trace(DatadogOperationNames.KIP_RETRY, service=DatadogCustomServices.KIP):
                set_apm_tag_in_current_span(MANUAL_KEEP_KEY)
                intents = transaction.latest_receipt.payment_rows.last().intents.all()
                transaction.appointment.update_appointment(
                    updated_by=get_system_user(),
                    status=transaction.appointment.STATUS.PENDING_PAYMENT,
                    who_makes_change=WhoMakesChange.BUSINESS,
                )

                txn_data = TransactionSerializer.get_data_for_renewal(transaction, force=True)
                txn_data['dry_run'] = False
                txn_data['parent_txn'] = transaction.id
                txn_data['appointment'] = transaction.appointment.id
                txn_data['payment_type_code'] = PaymentTypeEnum.KEYED_IN_PAYMENT
                serializer = TransactionSerializer(
                    data=txn_data,
                    context={
                        'pos': transaction.pos,
                        'business': transaction.pos.business,
                        'prepayment': True,
                        'old_txn': transaction,
                        'operator': transaction.operator,
                        compatibilities.COMPATIBILITIES: {compatibilities.PREPAYMENT: True},
                    },
                )
                validated_data = self.validate_serializer(serializer)
                transaction = serializer.save()
                validated_data['row_id'] = transaction.payment_rows.last().id

                pba_row = PaymentRow.objects.get(id=transaction.payment_rows.last().id)
                pba_row.intents.add(*intents)
                payment_method = PaymentMethod(card_type=CARD_TYPE__KEYED_IN_PAYMENT)
                payment_method.token = data['payment_token']

                fraud_prevention = None
                if nethone_attempt_reference := get_nethone_attempt_reference(self.request.headers):
                    fraud_prevention = FraudPreventionAuthAdditionalDataEntity(
                        nethone_attempt_reference=nethone_attempt_reference,
                    )

                ProxyProvider.make_payment(
                    transaction=transaction,
                    payment_method=payment_method,
                    payment_row=pba_row,
                    trigger=BasketPaymentAnalyticsTrigger.KIP_RETRY,
                    fraud_prevention=fraud_prevention,
                )

        # reload transaction from database
        transaction = Transaction.objects.filter(id=transaction.id).prefetch_all().last()

        access_level = getattr(self, 'access_level', None)
        response = {
            'transaction': TransactionSerializer(
                instance=transaction,
                context={
                    'access_level': access_level,
                    'pos': transaction.pos,
                },
            ).data,
            'register': (
                RegisterListingSerializer(
                    instance=register,
                    context={'pos': pos},
                ).data
                if register
                else None
            ),
        }
        if data['action'] == enums.BUSINESS_ACTION__CANCEL_PAYMENT:
            # add data for transaction renewal to response
            response['transaction_renewal_data'] = TransactionSerializer.get_data_for_renewal(
                transaction
            )

        return self.finish(response)

    @staticmethod
    def is_allowed_payment_type_code_or_raise(payment_type):
        if payment_type.code == PaymentTypeEnum.KEYED_IN_PAYMENT:
            return True
        if payment_type.code in PaymentTypeEnum.terminal_methods():
            raise tornado.web.HTTPError(
                status.HTTP_400_BAD_REQUEST,
                reason='Using this endpoint for BCR transactions is disallowed',
            )
        return True


class BusinessTransactionSendReceiptHandler(
    BaseTransactionHandlerMixin,
    RequestHandler,
):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @session(login_required=True, api_key_required=True)
    @json_request
    def post(self, business_id, transaction_id):
        """
        swagger:
            summary: Send POS Transaction Receipt
            notes: >
                .
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: transaction_id
                  type: integer
                  paramType: path
                  description: Transaction id
                - name: body
                  description: specify recipient email for this receipt
                  type: SendEmailRequest
                  paramType: body
            type: SendEmailResponse
        :swagger
        swaggerModels:
            SendEmailRequest:
                id: SendEmailRequest
                properties:
                    email:
                        type: string
                        required: false
                        description:
                            Optionally specify report recipient.
                            If None, then current user's email will be used.
        :swaggerModels

        """
        pos = self.get_pos_for_business(business_id=business_id)
        self.business_with_advanced_staffer(pos.business)

        transaction = self.get_object_or_404(Transaction, id=transaction_id, pos=pos)

        serializer = SendEmailRequest(
            data=self.data or {},
            context={
                'email': (
                    (transaction.customer and transaction.customer.email)
                    or (transaction.customer_card and transaction.customer_card.email)
                ),
            },
        )
        data = self.validate_serializer(serializer)

        self.quick_assert(
            data['email'],
            ('invalid', 'database', 'customer'),
            _('Receipt without customer.'),
        )

        # <editor-fold desc="early_finish section">
        SendReceiptToCustomer.delay(
            pos_id=pos.id,
            transaction_id=transaction.id,
            language=self.language,
            email=data['email'],
        )
        # </editor-fold>
        return self.finish_with_json(status.HTTP_200_OK, {'email': data['email']})


class BusinessTransactionLastReceiptHandler(
    RequestHandler,
    BaseTransactionHandlerMixin,
):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @session(login_required=True, api_key_required=True)
    def get(self, business_id, transaction_id):
        """
        swagger:
            summary: Get most recent Transaction Receipt
            notes: >
                Endpoint to do polling for payment.
                Use `finalized` to determine if do next query
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: transaction_id
                  type: integer
                  paramType: path
                  description: Transaction id
            type: BusinessTransactionLastReceiptResponse
        :swagger

        swaggerModels:
            BusinessTransactionLastReceiptResponse:
                id: BusinessTransactionLastReceiptResponse
                description:
                required:
                  - receipt
                  - finalized
                properties:
                    receipt:
                        type: ReceiptDetails
                    finalized:
                        type: boolean
                        description: >
                            If `true` then `last_receipt` will not change.
                            So there is no need to ask this endpoint any more.
        :swaggerModels
        """
        pos = self.get_pos_for_business(business_id=business_id)
        self.business_with_advanced_staffer(pos.business)

        transaction = self.get_object_or_404(
            Transaction,
            id=transaction_id,
            pos=pos,
        )
        last_receipt = transaction.latest_receipt
        response_serializer = ReceiptDetailsSerializer(
            instance=last_receipt,
            context={
                'pos': pos,
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'serialized_for_business': True,
            },
        )

        return self.finish_with_json(
            status.HTTP_200_OK,
            {
                'receipt': response_serializer.data,
                'finalized': last_receipt.status_code in receipt_status.FINALIZED_STATUSES,
            },
        )


class BusinessTransactionSeriesDetailsHandler(
    RequestHandler,
    BaseTransactionHandlerMixin,
):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    @using_db_for_reads(
        database_name=READ_ONLY_DB,
        condition=lambda: KillSwitch.alive(KillSwitch.System.REPLICA_POS_TRANSACTION_DETAILS),
    )
    def get(self, business_id, transaction_id):
        """
        swagger:
            summary: Get POS Transaction Series Details
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: transaction_id
                  type: integer
                  paramType: path
                  description: Transaction id
            type: TransactionSeriesResponse
        :swagger
        swaggerModels:
            AppointmentInfoInTransactionSeries:
                id: AppointmentInfoInTransactionSeries
                description: >
                    (read-only) an appointment associated with current
                    transaction series
                required:
                    - customer
                properties:
                    customer:
                        type: OnlyFullName
                        description: info about appointment customer
            OnlyFullName:
                id: OnlyFullName
                description: a simple object with only a full name field
                required:
                    - full_name
                properties:
                    full_name:
                        type: string
                        description: a full name field

        """
        pos = self.get_pos_for_business(business_id=business_id)
        self.business_with_advanced_staffer(pos.business)

        transaction = self.get_object_or_404(Transaction, id=transaction_id, pos=pos)

        ctx = {
            'operator': self.user,
            'pos': pos,
        }
        transaction_series = transaction.series()
        txns_data = TransactionSerializer(
            instance=transaction_series,
            many=True,
            context=ctx,
        ).data

        # HACK for old commission edit
        payment_type_code = transaction.latest_receipt.payment_type.code
        for data in txns_data:
            data['payment_type_code'] = payment_type_code

        appointment = firstof(transaction.appointment for transaction in transaction_series)
        customer_full_name = appointment and (
            # full_name from customer card
            (appointment.booked_for and appointment.booked_for.as_customer_data().full_name)
            or
            # full_name from subbooking
            appointment.customer_name
        )
        return self.finish(
            {
                'transactions': txns_data,
                'appointment': (
                    {
                        'customer': {
                            'full_name': customer_full_name,
                        }
                    }
                    if appointment
                    else None
                ),
            }
        )


class BusinessRequestRefund(RequestHandler, BaseTransactionHandlerMixin):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @session(login_required=True, api_key_required=True)
    @json_request
    def post(self, business_id, payment_row_id):
        """
        swagger:
            summary: Send request for refund
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: payment_row_id
                  type: integer
                  paramType: path
                  description: Payment Row id
        :swagger
        """
        from webapps.pos.models import PaymentRow

        pr = self.get_object_or_404(PaymentRow, id=payment_row_id)
        pos = self.get_pos_for_business(business_id=business_id)
        self.business_with_advanced_staffer(pos.business)

        try:
            do_refund(payment_row=pr, user=self.user)
        except RefundNotPossibleException as exc:
            lib.tools.sasrt(
                False,
                400,
                [
                    {
                        'field': 'refund_requested',
                        'type': 'validation',
                        'description': _(exc),
                    }
                ],
            )

        return self.finish_with_json(status.HTTP_200_OK, {})


class BusinessTransactionCreateInvoiceHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @staticmethod
    def check_transaction(transaction: Transaction) -> None:
        is_possible, msg = transaction.can_generate_invoice()

        lib.tools.sasrt(
            is_possible,
            400,
            [
                {
                    'code': 'invalid',
                    'type': 'validation',
                    'description': force_str(msg),
                }
            ],
        )

    @staticmethod
    def check_buyer(transaction: Transaction, buyer_id: int) -> None:
        if not buyer_id:
            bci = transaction.customer_card
            buyer_can_be_created_from_bci = bool(
                bci
                and (
                    (bci.first_name or bci.user and bci.user.first_name)
                    and (bci.last_name or bci.user and bci.user.first_name)
                    and (bci.email or bci.user and bci.user.email)
                    and bci.city
                    and bci.zipcode
                    and (bci.address_line_1 or bci.address_line_2)
                )
            )
            lib.tools.sasrt(
                buyer_can_be_created_from_bci,
                400,
                [
                    {
                        'code': 'invoice_customer_details',
                        'type': 'validation',
                        'description': _('Customer does not have invoice details ' 'configured'),
                    }
                ],
            )

    @session(login_required=True)
    @json_request
    def post(self, business_id, transaction_id):
        """
        swagger:
            summary: Creates an invoice for a transaction, based on last receipt
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: transaction_id
                  type: integer
                  paramType: path
                  description: Transaction id
                - name: body
                  type: TransactionCreateInvoiceRequest
                  paramType: body
        :swagger

        swaggerModels:
            TransactionCreateInvoiceRequest:
                id: TransactionCreateInvoiceRequest
                required:
                    - buyer_id
                properties:
                    buyer_id:
                        type: integer
                    dry_run:
                        type: boolean
                        description: >
                            Dry Mode does not save anything, only validates.
        :swaggerModels
        """
        from webapps.pos.adapters import create_invoice_from_receipt

        business = self.business_with_staffer(business_id)
        transaction = self.get_object_or_404(
            Transaction.objects.filter(pos__business_id=business_id),
            id=transaction_id,
        )
        buyer_id = self.data.get('buyer_id')
        check_seller(business)
        self.check_transaction(transaction)
        self.check_buyer(transaction, buyer_id)
        dry_run = self.data.get('dry_run', False)

        try:
            result = create_invoice_from_receipt(
                transaction.latest_receipt,
                self.user_staffer_id,
                buyer_id,
                dry_run,
            )
        except serializers.ValidationError as e:
            is_invalid_buyer = any(
                [invalid_field.startswith('buyer') for invalid_field in e.detail.keys()]
            )
            errors = e.detail
            if is_invalid_buyer:
                errors = [
                    {
                        'code': 'invoice_customer_details',
                        'type': 'validation',
                        'description': _('Customer does not have invoice details ' 'configured'),
                    }
                ]
            return self.finish_with_json(e.status_code, {'errors': errors})

        return self.finish_with_json(status.HTTP_200_OK, result)


class BusinessCashFlowHandler(BasePOSHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    @using_db_for_reads(READ_ONLY_DB)
    def get(self, business_id):
        """
        swagger:
            summary: Get Business PaymentRows/FundTransfers
            type: array
            items:
                type: BusinessCashFlowResponse
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                - name: page
                  description: Results page
                  paramType: query
                  type: integer
                  defaultValue: 1
                  minimum: 1
                - name: per_page
                  description: how many transactions per page to return
                  paramType: query
                  type: integer
                  minimum: 0
                  defaultValue: 20
                  maximum: 1000
        :swagger
        swaggerModels:
            BusinessCashFlowResponse:
                id: BusinessCashFlowResponse
                description: 200 if everything is ok. 404 if not
                properties:
                    cash_flows:
                        type: array
                        items:
                            type: CashFlow
                    page:
                        type: integer
                        description: current results page number
                    per_page:
                        type: integer
                        description: number of items per page
                    count:
                        type: integer
                        description: count of all objects across all pages
        :swaggerModels
        """

        business, pos = self._get_business_with_pos(business_id)
        data = self._prepare_get_arguments()

        serializer = PaginatorSerializer(data=data)
        data = self.validate_serializer(serializer)
        page = data['page']
        per_page = data['per_page']

        cash_flow_qs, count = get_business_cash_flow_objects(
            pos,
            exclude_manual_transfer=True,
            page=page,
            per_page=per_page,
        )

        # due to pagination optimization done in get_business_cash_flow_objects
        # we don't use pager.count
        pager = Paginator(
            cash_flow_qs,
            per_page,
        )
        try:
            object_list = pager.page(page).object_list
        except EmptyPage:
            object_list = []

        objects = []
        for obj in object_list:
            if isinstance(obj, PaymentRow):
                objects.append(
                    {
                        'payment_row': obj,
                        'fund_transfer': None,
                    }
                )
            else:
                objects.append(
                    {
                        'payment_row': None,
                        'fund_transfer': obj,
                    }
                )

        serializer = BusinessCashFlowSerializer(
            objects,
            context={
                'business': business,
            },
            many=True,
        )

        return self.finish_with_json(
            status.HTTP_200_OK,
            {
                'cash_flows': serializer.data,
                'page': page,
                'per_page': per_page,
                'count': count,
            },
        )


class PaymentRowListingHandler(BasePOSHandler, PaymentRowMixin):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    @using_db_for_reads(READ_ONLY_DB)
    def get(self, business_id):
        """
        swagger:
            summary: Get Business PaymentRows
            type: array
            items:
                type: TransactionPaymentRow
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                - name: query
                  description: filter transactions by text matching
                  type: string
                  paramType: query
                - name: date_from
                  description: only payment rows from the given date, yyyy-mm-dd
                  type: string
                  paramType: query
                  format: date
                - name: date_till
                  description: only payment rows until the end of the given date, yyyy-mm-dd
                      automatically converts date to full day (adds a day)
                  type: string
                  paramType: query
                  format: date
                - name: page
                  description: Results page
                  paramType: query
                  type: integer
                  defaultValue: 1
                  minimum: 1
                - name: per_page
                  description: how many transactions per page to return
                  paramType: query
                  type: integer
                  minimum: 0
                  defaultValue: 20
                  maximum: 1000
        :swagger

        """
        self.business_with_advanced_staffer(business_id)
        pos = self.get_object_or_404(
            POS.objects.select_related('business'),
            business_id=business_id,
            active=True,
        )

        data = self._prepare_get_arguments()

        serializer = GetBusinessTransactionsRequestSerializer(
            data=data,
            context={'tz': pos.business.get_timezone()},
        )
        data = self.validate_serializer(serializer)

        per_page = data['per_page']
        page = data['page']

        offset_from = per_page * (page - 1)
        offset_to = per_page * page

        payment_rows_qs = PaymentRow.objects.filter(
            receipt__transaction__pos=pos,
            receipt__transaction__deleted__isnull=True,
        ).order_by('-id')

        if data.get('booking_id'):
            payment_rows_qs = payment_rows_qs.filter(
                receipt__transaction__rows__subbooking_id=data['booking_id'],
            )
        if data.get('customer_card_id'):
            payment_rows_qs = payment_rows_qs.filter(
                receipt__transaction__customer_card_id=data['customer_card_id'],
            )
        if data.get('voucher_id'):
            payment_rows_qs = payment_rows_qs.filter(
                voucher_id=data['voucher_id'],
            )
        if data.get('payment_type'):
            payment_rows_qs = payment_rows_qs.filter(
                payment_type__code=data['payment_type'],
            )
        if data.get('query') is not None:
            payment_rows_qs = payment_rows_qs.filter(
                build_search_query(
                    data.get('query'),
                    [
                        'receipt__transaction__customer_data',
                        'receipt__transaction__rows__subbooking__service_name',
                        'receipt__transaction__rows__service_variant__service__name',
                        'id',
                        'receipt__transaction__id',
                    ],
                )
            ).distinct()

        if 'datetime_from' in data:
            payment_rows_qs = payment_rows_qs.filter(created__gte=data['datetime_from'])
        if 'datetime_till' in data:
            payment_rows_qs = payment_rows_qs.filter(created__lt=data['datetime_till'])

        if 'transaction_id' in data:
            payment_rows_qs = payment_rows_qs.filter(
                receipt__transaction__id__contains=data['transaction_id']
            )

        last_prs_ids, payment_row_ids_only_success = self._get_valid_payment_rows_ids(
            payment_rows_qs,
        )
        prs_count = PaymentRow.objects.filter(
            id__in=last_prs_ids | payment_row_ids_only_success,
        ).count()

        prs_ids = set(last_prs_ids[:offset_to])
        prs_ids.update(set(payment_row_ids_only_success[:offset_to]))

        payment_rows = (
            PaymentRow.objects.filter(
                id__in=prs_ids,
            )
            .select_related('payment_type', 'receipt__transaction__customer_card')
            .order_by('-created')[offset_from:offset_to]
        )

        access_level = getattr(self, 'access_level', None)
        return self.finish_with_json(
            status.HTTP_200_OK,
            {
                'payment_rows': PaymentRowListingSerializer(
                    instance=payment_rows,
                    many=True,
                    context={
                        'pos': pos,
                        'valid_currency': True,
                        'access_level': access_level,
                    },
                ).data,
                'count': prs_count,
                'page': data['page'],
                'per_page': data['per_page'],
            },
        )


class PaymentRowsSummaryHandler(BasePOSHandler, PaymentRowMixin):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @staticmethod
    def _get_payment_row_days(
        payment_rows_qs: PaymentRowQuerySet, tz: tzfile
    ) -> t.List[datetime.datetime]:
        days_with_payment_rows = [row.created for row in payment_rows_qs]
        days_with_payment_rows = [
            day.astimezone(tz).replace(hour=0, minute=0, second=0, microsecond=1)
            for day in days_with_payment_rows
        ]
        days_with_payment_rows = list(dict.fromkeys(days_with_payment_rows))  # remove duplicates
        return days_with_payment_rows

    def _get_time_ranges(
        self, pos: POS, data: dict, scope: str, tz: tzfile
    ) -> (datetime.datetime, datetime.datetime, int):

        page = data['page']
        per_page = data['per_page']
        payment_rows_qs = PaymentRow.objects.filter(
            receipt__transaction__pos=pos,
            receipt__transaction__deleted__isnull=True,
        ).order_by('-created')
        payment_rows_qs = self.filter_payment_rows(payment_rows_qs)
        if data.get('query') is not None:
            payment_rows_qs = payment_rows_qs.filter(
                build_search_query(
                    data.get('query'),
                    [
                        'receipt__transaction__customer_data',
                        'receipt__transaction__rows__subbooking__service_name',
                        'receipt__transaction__rows__service_variant__service__name',
                        'id',
                        'receipt__transaction__id',
                    ],
                )
            ).distinct()

        # get the time range of payment rows depending on the scope
        if scope == PaymentRowsSummaryScopes.CUSTOM:
            from_date = data.get('datetime_from')
            till_date = data.get('datetime_till')
            count = 1

        elif scope == PaymentRowsSummaryScopes.DAY:
            days_with_payment_rows = [
                x
                for x in payment_rows_qs.annotate(
                    # pylint: disable=protected-access
                    trunc_date=TruncDay('created', tzinfo=pytz.timezone(tz._long_name)),
                )
                .values(
                    'trunc_date',
                )
                .annotate(
                    Count('id'),
                )
                .order_by(
                    '-trunc_date',
                )
            ]
            count = len(days_with_payment_rows)
            start = (page - 1) * per_page
            end = start + per_page if start + per_page <= count else count
            selected_dates = [x['trunc_date'] for x in days_with_payment_rows[start:end]]
            if not selected_dates:
                return None, None, count
            from_date = selected_dates[-1].astimezone(tz)
            till_date = selected_dates[0].astimezone(tz) + datetime.timedelta(days=1)

        elif scope == PaymentRowsSummaryScopes.MONTH:
            months_with_payment_rows = [
                x
                for x in payment_rows_qs.annotate(
                    # pylint: disable=protected-access
                    trunc_date=TruncMonth('created', tzinfo=pytz.timezone(tz._long_name)),
                )
                .values(
                    'trunc_date',
                )
                .annotate(
                    Count('id'),
                )
                .order_by(
                    '-trunc_date',
                )
            ]
            count = len(months_with_payment_rows)
            start = (page - 1) * per_page
            end = start + per_page if start + per_page <= count else count
            selected_dates = [
                (x['trunc_date'].year, x['trunc_date'].month)
                for x in months_with_payment_rows[start:end]
            ]
            if not selected_dates:
                return None, None, count

            from_year = selected_dates[-1][0]
            from_month = selected_dates[-1][1]

            till_year = selected_dates[0][0]
            till_month = selected_dates[0][1] + 1
            if till_month == 13:
                till_month = 1
                till_year += 1

            from_date = datetime.datetime(from_year, from_month, 1, tzinfo=tz)
            till_date = datetime.datetime(till_year, till_month, 1, tzinfo=tz)

        else:
            raise serializers.ValidationError(
                f"Wrong value for 'scope' field, allowed values: "
                f"{', '.join(PaymentRowsSummaryScopes.values())}"
            )

        return from_date, till_date, count

    @staticmethod
    def _get_time_range_strings(
        from_date: datetime.datetime, till_date: datetime.datetime
    ) -> (str, str):

        from_string = from_date.strftime("%Y-%m-%d")

        # remove 1 day from till string, since till goes 1 day ahead for the database search
        till_string = (till_date - datetime.timedelta(days=1)).strftime("%Y-%m-%d")
        return from_string, till_string

    def _get_tile_spans(
        self,
        scope: str,
        from_date: datetime,
        till_date: datetime,
        payment_rows_qs: PaymentRowQuerySet,
        tz: tzfile,
    ) -> t.List[str]:

        tile_spans = []
        day_list = self._get_payment_row_days(payment_rows_qs, tz)

        if scope == PaymentRowsSummaryScopes.CUSTOM:
            from_string, till_string = self._get_time_range_strings(from_date, till_date)
            tile_spans = [f'{from_string} - {till_string}']

        elif scope == PaymentRowsSummaryScopes.DAY:
            # create a list of spans (days) within the calculated date range
            tile_spans = [day.strftime("%Y-%m-%d") for day in day_list]

        elif scope == PaymentRowsSummaryScopes.MONTH:
            # create a list of spans (months) within the calculated date range
            date_list = [(day.year, day.month) for day in day_list]
            date_list = list(dict.fromkeys(date_list))  # remove duplicates
            tile_spans = [f'{month[0]}-{month[1]:02}' for month in date_list]

        return tile_spans

    def _get_payment_rows_in_range(
        self, pos: POS, from_date: datetime, till_date: datetime, query: str
    ) -> PaymentRowQuerySet:

        payment_rows_qs = PaymentRow.objects.filter(
            receipt__transaction__pos=pos,
            receipt__transaction__deleted__isnull=True,
        ).order_by('-created')

        if from_date:
            payment_rows_qs = payment_rows_qs.filter(created__gte=from_date)
        if till_date:
            payment_rows_qs = payment_rows_qs.filter(created__lt=till_date)

        payment_rows_qs = self.filter_payment_rows(payment_rows_qs)
        if query is not None:
            payment_rows_qs = payment_rows_qs.filter(
                build_search_query(
                    query,
                    [
                        'receipt__transaction__customer_data',
                        'receipt__transaction__rows__subbooking__service_name',
                        'receipt__transaction__rows__service_variant__service__name',
                        'id',
                        'receipt__transaction__id',
                    ],
                )
            ).distinct()

        payment_rows_qs = payment_rows_qs.select_related('payment_type').order_by('-created')

        return payment_rows_qs

    @staticmethod
    def _create_tiles(
        tile_spans: t.List[str], scope: str, from_string: str, till_string: str
    ) -> t.List[dict]:

        tiles = []
        for span in tile_spans:
            tile = {
                'span': span,
                'payment_types': [],
                'total': 0,
                'date_from': None,
                'date_till': None,
            }
            if scope == PaymentRowsSummaryScopes.DAY:
                tile['date_from'] = span
                tile['date_till'] = span
            elif scope == PaymentRowsSummaryScopes.MONTH:
                date = datetime.datetime.strptime(span + '-01', '%Y-%m-%d')
                last_day_of_month = calendar.monthrange(date.year, date.month)[1]
                tile['date_from'] = span + '-01'
                tile['date_till'] = span + '-' + str(last_day_of_month)
            elif scope == PaymentRowsSummaryScopes.CUSTOM:
                tile['date_from'] = from_string
                tile['date_till'] = till_string
            tiles.append(tile)

        return tiles

    @staticmethod
    def _add_tile_payment_data(
        tiles: t.List[dict],
        payment_rows_qs: PaymentRowQuerySet,
        scope: str,
        from_string: str,
        till_string: str,
        tz: tzfile,
    ) -> t.List[dict]:

        # create and update payment type summaries based on each payment row
        for payment_row in payment_rows_qs:
            code = payment_row.payment_type.code
            label = payment_row.payment_type.label

            amount = payment_row.amount
            if payment_row.status in NEGATIVE_VALUE_STATUSES:
                amount = -amount
            elif payment_row.status in STATUSES_WITHOUT_MONEY:
                amount = 0

            creation_span = None
            if scope == PaymentRowsSummaryScopes.DAY:
                creation_span = payment_row.created.astimezone(tz).strftime("%Y-%m-%d")
            elif scope == PaymentRowsSummaryScopes.MONTH:
                creation_span = payment_row.created.astimezone(tz).strftime("%Y-%m")
            elif scope == PaymentRowsSummaryScopes.CUSTOM:
                creation_span = f'{from_string} - {till_string}'

            span_tile = next((tile for tile in tiles if tile['span'] == creation_span), None)
            if span_tile is None:
                continue

            span_tile['total'] += amount

            if code not in (payment_type['code'] for payment_type in span_tile['payment_types']):
                span_tile['payment_types'].append({'code': code, 'label': label, 'total': amount})
            else:
                for payment_type in span_tile['payment_types']:
                    if payment_type['code'] == code:
                        payment_type['total'] += amount

        # sort payment types in each tile
        for tile in tiles:
            tile['payment_types'] = sorted(tile['payment_types'], key=lambda d: d['label'])

        return tiles

    @session(login_required=True)
    @using_db_for_reads(READ_ONLY_DB, condition=PaymentRowSummaryUseReadReplicaFlag)
    def get(self, business_id):
        """
        swagger:
            summary: Get lists of payment rows per payment type and time range
            type: PaymentRowsSummaryResponse
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                - name: scope
                  description: the scope of the returned summary, day/month/custom range
                  type: string
                  paramType: query
                  required: True
                - name: date_from
                  description: only payments starting from this date
                  type: string
                  paramType: query
                  format: date
                - name: date_till
                  description: |
                      only payments until this date (inclusive)
                      automatically convert date to full day (adds a day)
                  type: string
                  paramType: query
                  format: date
                - name: page
                  description: results page
                  paramType: query
                  type: integer
                  defaultValue: 1
                  minimum: 1
                - name: per_page
                  description: how many payment rows summaries to return per page
                  paramType: query
                  type: integer
                  minimum: 0
                  defaultValue: 5
                  maximum: 1000
        """
        self.business_with_advanced_staffer(business_id)
        pos = self.get_object_or_404(
            POS.objects.select_related('business'),
            business_id=business_id,
            active=True,
        )

        data = self._prepare_get_arguments()
        tz = pos.business.get_timezone()

        serializer = PaymentRowsSummarySerializer(
            data=data,
            context={'tz': tz},
        )
        data = self.validate_serializer(serializer)
        scope = data['scope']

        from_date, till_date, count = self._get_time_ranges(pos, data, scope, tz)
        if not from_date or not till_date:
            return self.finish_with_json(
                status.HTTP_200_OK,
                {
                    'tiles': [],
                    'scope': scope,
                    'page': data['page'],
                    'per_page': data['per_page'],
                    'count': count,
                },
            )

        query = data.get('query')
        payment_rows_qs = self._get_payment_rows_in_range(pos, from_date, till_date, query)

        from_string, till_string = self._get_time_range_strings(from_date, till_date)

        tile_spans = self._get_tile_spans(scope, from_date, till_date, payment_rows_qs, tz)

        tiles = self._create_tiles(tile_spans, scope, from_string, till_string)
        tiles = self._add_tile_payment_data(
            tiles,
            payment_rows_qs,
            scope,
            from_string,
            till_string,
            tz,
        )

        return self.finish_with_json(
            status.HTTP_200_OK,
            {
                'tiles': tiles,
                'scope': scope,
                'page': data['page'],
                'per_page': data['per_page'],
                'count': count,
            },
        )
