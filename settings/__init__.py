#!/usr/bin/env python
"""Settings are assembled from multiple files.

See docstrings of each files imported in MAGIC SETTINGS IMPORT section.

"""
import os
import warnings

import urllib3
from bo_obs.datadog import enable_datadog, filters
from bo_obs.datadog.enums import DatadogCustomServices, DatadogTags

import lib.monkeypatching
from lib.datadog.filters import GRPCSubdomainErrorFilter, BooksyTeamsSpanInjectorFilter
from lib.utils import str_to_bool


DD_FILTERS = [
    filters.ErrorPropagationDjangoFilter(),
    filters.GRPCErrorFilter(),
    GRPCSubdomainErrorFilter(),
    BooksyTeamsSpanInjectorFilter(),
]
DD_HEALTH_CHECK_AND_WARMUP = str_to_bool(os.getenv('DD_HEALTH_CHECK_AND_WARMUP', 'false'))
DD_DJANGO_INSTRUMENT_SIGNALS = str_to_bool(os.getenv('DD_DJANGO_INSTRUMENT_SIGNALS', 'false'))
if not DD_HEALTH_CHECK_AND_WARMUP:
    DD_FILTERS.append(filters.FilterCoreAPIWarmupAndHealthCheck())


DD_AGENT_ENABLED = enable_datadog(
    filters=DD_FILTERS,
    jinja2_service_name='jinja-templates',
    django_use_legacy_resource_format=True,
)

API_SUBSERVICES = ['search']
# enum for public or private mode
_MODE_API = os.environ.get('MODE_API', 'private_api')
API_PRIVATE = _MODE_API in ('private_api', 'api', 'drf_api')
API_PUBLIC = _MODE_API in ('public_api', 'public')
API_GRPC = _MODE_API == 'grpc_api'
API_DJANGO_ADMIN = _MODE_API in ('django_admin', 'admin')
if not any([API_PRIVATE, API_PUBLIC, API_GRPC, API_DJANGO_ADMIN]):
    raise EnvironmentError('Unrecognized MODE_API')

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# monkeypatching and pinning some libs before using the clients in various settings below
if DD_AGENT_ENABLED:
    lib.monkeypatching.patch_get_redis_connection()
    if DD_DJANGO_INSTRUMENT_SIGNALS:
        lib.monkeypatching.patch_trace_django_signals()

###############################
#### MAGIC SETTINGS IMPORT ####
###############################

# pylint: disable=wrong-import-position,reimported
from settings._django import *
from settings.defaults import *
from settings.sms import *
from settings.local import *
from settings.logging import *
from settings.elasticsearch import *
from settings.images import *
from settings.celery import *
from settings.shell_plus import *
from settings.booksy_auth import *
from settings.booksy_med import *
from settings.structure import *
from settings.performance_tests import *

if API_PUBLIC:
    from settings.rest_public_api import *
elif API_PRIVATE:
    # Private API handles Tornado and DRF, so it needs to load DRF settings
    from settings.drf_api import *
elif API_GRPC:
    from settings.grpc_api import *
elif API_DJANGO_ADMIN:
    from settings.django_admin import *
else:
    raise EnvironmentError('Unrecognized MODE_API')

from settings.adyen import *
from settings.stripe import *
from settings.payments import *
from settings.oauth2 import *
from settings.boost import *
from settings.family_and_friends import *


###############################
####### MONKEYPATCHING ########
###############################
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


# monkeypatch some stuff
lib.monkeypatching.monkeypatch_py3_10_asyncio_lock_loop_deprecation()
lib.monkeypatching.monkeypatch_py3_10_collections()
lib.monkeypatching.psycopg2_time24_hour_extension()
lib.monkeypatching.dateutil_tz_cache()
lib.monkeypatching.structured_values_in_queryset()
lib.monkeypatching.defaultfilters_date_am_pm_monkeypatch()
lib.monkeypatching.requests_user_agent_monkeypatch('Booksy/Core')
lib.monkeypatching.requests_timeout_monkeypatch(default_timeout=REQUESTS_TIMEOUT)
lib.monkeypatching.rest_framework_get_attribute_monkeypatch()
lib.monkeypatching.django_m2m_intermediary_model_monkeypatch()
lib.monkeypatching.appconfigstub_monkeypatch()
lib.monkeypatching.config_freezegun_ignore_list()
lib.monkeypatching.patch_redis_connectionpool_lock()
lib.monkeypatching.patch_celery_eta_implementation()

# disable warnings
warnings.filterwarnings('ignore', module='weasyprint')
warnings.filterwarnings('ignore', module='openpyxl')
warnings.filterwarnings('ignore', module='django_countries')
# ignore warnings from pandas highly used in this modules
warnings.filterwarnings('ignore', module='webapps.booking.timeslots')
warnings.filterwarnings('ignore', module='webapps.schedule.working_hours')
# remove after resolving https://booksy.atlassian.net/browse/SRE-1712
warnings.filterwarnings('ignore', module='celery.platforms', lineno=829)
warnings.filterwarnings(
    "ignore",
    message='Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security.',  # pylint: disable=line-too-long
)
warnings.filterwarnings(
    "ignore",
    message='^Pickled model instance\'s Django version',
    module='django_redis.serializers.pickle',
)
warnings.filterwarnings(
    action="ignore",
    lineno=1207,
    category=RuntimeWarning,
    module="prompt_toolkit.application.application",
)
warnings.filterwarnings(
    action="ignore",
    lineno=892,
    category=RuntimeWarning,
    module="prompt_toolkit.application.application",
)

warnings.filterwarnings(
    action="ignore",
    lineno=414,
    category=RuntimeWarning,
    module="prompt_toolkit.key_binding.key_processor",
)

if DD_AGENT_ENABLED:
    from ddtrace import patch  # pylint: disable=ungrouped-imports

    patch(tornado=True)
