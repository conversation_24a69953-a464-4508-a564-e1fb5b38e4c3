from django.contrib import messages
from django.urls.base import reverse
from mock import call, patch
from model_bakery import baker

from lib.feature_flag.feature.admin import ShowEnforcePasswordResetFlag
from lib.tests.utils import override_eppo_feature_flag
from webapps.admin_extra.tests import DjangoTestCase
from webapps.user.models import User


class TestEnforcePasswordReset(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.login_admin()
        self.user1, self.user2 = baker.make(User, _quantity=2)
        self.url = reverse('admin:enforce_password_reset')

    @patch('webapps.admin_extra.views.password_reset._sync_user_booksy_auth_proxy')
    @patch.object(User, 'delete_all_user_sessions', autospec=True)
    def test_form_submission(self, mock_delete, mock_sync):
        response = self.client.post(self.url, {'user_ids': f'{self.user1.id}\n{self.user2.id}'})

        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, self.url)
        mock_sync.assert_called_with([self.user1.id, self.user2.id])
        mock_delete.assert_has_calls([call(self.user1), call(self.user2)], any_order=True)

    def test_invalid_form_submission(self):
        response = self.client.post(self.url, {'user_ids': 'invalid_id'})

        self.assertEqual(response.status_code, 200)
        self.assertFormError(response, 'form', 'user_ids', 'All User IDs must be valid numbers.')

        user_id = 12334567
        response = self.client.post(self.url, {'user_ids': f'{user_id}'})

        self.assertEqual(response.status_code, 200)
        self.assertFormError(response, 'form', 'user_ids', f"User IDs {user_id} do not exist.")

    @override_eppo_feature_flag({ShowEnforcePasswordResetFlag.flag_name: False})
    def test_feature_disabled(self):
        response = self.client.get(self.url)
        self.assertRedirects(response, reverse('admin:index'))
        messages_list = list(messages.get_messages(response.wsgi_request))
        self.assertTrue(
            any("This feature is currently disabled." in str(message) for message in messages_list)
        )
