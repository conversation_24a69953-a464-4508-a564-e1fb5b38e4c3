from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.contrib import messages
from django.shortcuts import redirect
from django.urls import reverse

from lib.feature_flag.feature.admin import ShowEnforcePasswordResetFlag
from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.password_reset import EnforcePasswordResetForm
from webapps.user.groups import GroupName
from webapps.user.models import User
from webapps.user.tasks.sync import _sync_user_booksy_auth_proxy


class EnforcePasswordResetChangeView(GroupPermissionMixin, FormView):
    """
    Enforce password reset for choosen users.
    """

    MAX_IDS_NBR = 50

    booksy_teams = BooksyTeams.CUSTOMER_ONBOARDING
    groups = GroupName.SECURITY_ADMIN
    permission_required = ()
    form_class = EnforcePasswordResetForm
    success_url = 'enforce_password_reset'
    template_name = 'admin/custom_views/password_reset.html'

    def dispatch(self, request, *args, **kwargs):
        if not ShowEnforcePasswordResetFlag():
            messages.warning(request, "This feature is currently disabled.")
            return redirect(reverse('admin:index'))

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['max_ids_nbr'] = self.MAX_IDS_NBR
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['max_ids_nbr'] = self.MAX_IDS_NBR
        context['country'] = settings.API_COUNTRY
        return context

    def post(self, request, *args, **kwargs):
        form = self.get_form(self.get_form_class())
        if not form.is_valid():
            return self.form_invalid(form)

        user_ids = form.cleaned_data['user_ids']

        users = User.objects.filter(id__in=user_ids)
        users.update(password='!', password_change_required=True)

        _sync_user_booksy_auth_proxy([u.id for u in users])

        for user in users:
            user.delete_all_user_sessions()

        return self.form_valid(form)
