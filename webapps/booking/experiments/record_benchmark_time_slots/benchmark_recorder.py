from dataclasses import dataclass
from datetime import date
from enum import StrEnum
from logging import Logger
from typing import Any

from django.conf import settings
from requests import request

from lib.feature_flag.feature.booking import RecordBenchmarkTimeSlotsMethod
from webapps.booking.experiments.compare_new_slots.compare import (
    generate_and_compare_with_timeslot_service_slots,
)
from webapps.booking.experiments.record_benchmark_time_slots.tasks import (
    send_benchmark_time_slots,
)
from webapps.booking.timeslots.v1.booksy_slots import BooksySlots
from webapps.booking.timeslots.v1.data_sources import WorkingHours, ResourceTimeOffs
from webapps.booking.timeslots.v1.serializers import RequestData

RECORD_BENCHMARK_TIME_SLOTS_API_PATH = "/timeslots/v1/report-benchmark-slots"
REQUEST_TIMEOUT = 5


@dataclass
class BenchmarkTimeSlotsRecorderInput:
    request_data: RequestData
    booksy_slots: BooksySlots
    working_hours: WorkingHours | None
    time_slots_optimization: bool
    time_offs: ResourceTimeOffs | None


class BenchmarkRecordMethod(StrEnum):
    LEGACY = "Legacy"
    SYNCHRONOUS = "Synchronous"
    ASYNCHRONOUS = "Asynchronous"


class BenchmarkTimeSlotsRecorder:
    def __init__(self, logger: Logger) -> None:
        self._logger = logger

    def record(self, record_input: BenchmarkTimeSlotsRecorderInput) -> None:
        request_url = self._request_url()
        request_payload = self._request_payload(
            request_data=record_input.request_data,
            booksy_slots=record_input.booksy_slots,
            working_hours=record_input.working_hours,
            time_slots_optimization=record_input.time_slots_optimization,
            time_offs=record_input.time_offs,
        )

        match record_method := RecordBenchmarkTimeSlotsMethod():
            case BenchmarkRecordMethod.LEGACY.value:
                self._legacy_request(
                    request_data=record_input.request_data,
                    booksy_slots=record_input.booksy_slots,
                    time_slots_optimization=record_input.time_slots_optimization,
                )
            case BenchmarkRecordMethod.SYNCHRONOUS.value:
                self._send_synchronous_request(
                    url=request_url,
                    payload=request_payload,
                )
            case BenchmarkRecordMethod.ASYNCHRONOUS.value:
                self._send_asynchronous_request(
                    url=request_url,
                    payload=request_payload,
                )
            case _:
                self._logger.info(
                    "Benchmark time slots record method %s unrecognized", record_method
                )

    def _request_url(self) -> str:
        return f"http://{settings.TIMESLOT_REST_HOST}{RECORD_BENCHMARK_TIME_SLOTS_API_PATH}"

    def _request_payload(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        self,
        request_data: RequestData,
        booksy_slots: BooksySlots,
        working_hours: WorkingHours | None,
        time_slots_optimization: bool,
        time_offs: ResourceTimeOffs | None,
    ) -> dict[str, Any]:
        return {
            "business_id": request_data.business_id,
            "start": request_data.start_date.strftime(settings.DATE_FORMAT),
            "end": request_data.end_date.strftime(settings.DATE_FORMAT),
            "subbookings": [
                {
                    "service_variant_id": subbooking.service_variant_id,
                    "staffer_id": subbooking.staffer_id if subbooking.staffer_id != -1 else None,
                }
                for subbooking in request_data.subbookings
            ],
            "benchmark_slots": self._transform_benchmark_time_slots(booksy_slots),
            "slots_computation_metadata": {
                "working_hours": (
                    self._transform_working_hours(working_hours)
                    if working_hours is not None
                    else None
                ),
                "time_offs": (
                    self._transform_time_offs(time_offs) if time_offs is not None else None
                ),
                "business_settings": {
                    "slots_optimization_enabled": time_slots_optimization,
                },
            },
        }

    def _legacy_request(
        self,
        request_data: RequestData,
        booksy_slots: BooksySlots,
        time_slots_optimization: bool,
    ) -> None:
        generate_and_compare_with_timeslot_service_slots(
            request_data=request_data,
            booksy_slots=booksy_slots,
            is_slots_optimization_enabled=time_slots_optimization,
        )

    def _send_synchronous_request(
        self,
        url: str,
        payload: dict[str, Any],
    ) -> None:
        request(
            url=url,
            method="POST",
            json=payload,
            timeout=REQUEST_TIMEOUT,
        )

    def _send_asynchronous_request(
        self,
        url: str,
        payload: dict[str, Any],
    ) -> None:
        send_benchmark_time_slots.delay(
            url=url,
            payload=payload,
            timeout=REQUEST_TIMEOUT,
        )

    def _transform_benchmark_time_slots(
        self,
        booksy_slots: BooksySlots,
    ) -> dict[str, list[int]]:
        return {
            str(x["date"]): [y["slot"] for y in x["slots"]]
            for x in booksy_slots.response.get("time_slots", [])
        }

    def _transform_working_hours(
        self, working_hours: WorkingHours
    ) -> list[tuple[int, date, tuple[int, int]]]:
        if working_hours.empty:
            return []

        return [
            (
                int(resource_id),
                timestamp.date().strftime(settings.DATE_FORMAT),
                group[[working_hours.COLUMNS[0], working_hours.COLUMNS[1]]].values.tolist(),
            )
            for (resource_id, timestamp), group in working_hours.groupby(
                level=[working_hours.INDEX[0], working_hours.INDEX[1]]
            )
        ]

    def _transform_time_offs(
        self, time_offs: ResourceTimeOffs
    ) -> list[tuple[int, date, tuple[int, int]]]:
        if time_offs.empty:
            return []
        return [
            (
                int(resource_id),
                timestamp.date().strftime(settings.DATE_FORMAT),
                group[[time_offs.COLUMNS[0], time_offs.COLUMNS[1]]].values.tolist(),
            )
            for (resource_id, timestamp), group in time_offs.groupby(
                level=[time_offs.INDEX[0], time_offs.INDEX[1]]
            )
        ]
