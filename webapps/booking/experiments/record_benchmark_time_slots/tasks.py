from logging import getLogger
from typing import Any

from requests import request

from lib.celery_tools import celery_task

_logger = getLogger("booksy.booking.experiments.record_benchmark_time_slots.tasks")


@celery_task
def send_benchmark_time_slots(
    url: str,
    payload: dict[str, Any],
    timeout: int = 3,
):
    try:
        request(
            url=url,
            method="POST",
            json=payload,
            timeout=timeout,
        )
    except Exception as exception:  # pylint: disable=broad-exception-caught
        _logger.error("Failed to send benchmark time slots request: %s", exception)
