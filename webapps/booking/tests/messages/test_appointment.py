import datetime
from decimal import Decimal

from django.test import TestCase
import pytest

from google.protobuf.json_format import MessageToJson

from webapps.booking.messages.appointment import AppointmentChangedMessage
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import bci_recipe, business_recipe, service_variant_recipe
from webapps.business.enums import PriceType
from webapps.pos.baker_recipes import pos_recipe


class TestAppointmentChanged(TestCase):
    DATETIME = datetime.datetime(2022, 5, 1)

    @pytest.mark.freeze_time(DATETIME)
    def setUp(self) -> None:
        self.business = business_recipe.make()
        self.bci = bci_recipe.make(business=self.business)
        self.appointment = create_appointment(
            total_value=78.21, business=self.business, booked_for=self.bci
        )

    def test_message_no_pos(self):
        self.assertJSONEqual(
            MessageToJson(
                AppointmentChangedMessage(self.appointment, context={'prev_status': 'A'}).message
            ),
            {
                'appointmentId': self.appointment.id,
                'bookedForId': self.bci.id,
                'businessId': self.business.id,
                'bookedFrom': str(int(self.appointment.booked_from.timestamp())),
                'totalValue': '78.21',
                'total': {'priceType': PriceType.DONT_SHOW},
                'previousStatus': 'A',
            },
        )

    def test_message_no_price_defined(self):
        pos_recipe.make(business=self.business)
        self.assertJSONEqual(
            MessageToJson(
                AppointmentChangedMessage(self.appointment, context={'prev_status': 'A'}).message
            ),
            {
                'appointmentId': self.appointment.id,
                'bookedForId': self.bci.id,
                'businessId': self.business.id,
                'bookedFrom': str(int(self.appointment.booked_from.timestamp())),
                'totalValue': '78.21',
                'total': {
                    'discount': '0',
                    'value': '0',
                    'priceType': PriceType.FIXED,
                },
                'previousStatus': 'A',
            },
        )

    def test_message_service_price(self):
        pos_recipe.make(business=self.business)
        subbooking = self.appointment.subbookings[0]

        service_variant = service_variant_recipe.make(price=Decimal(10), type=PriceType.STARTS_AT)
        subbooking.service_variant = service_variant
        subbooking.save(override=True)

        self.assertJSONEqual(
            MessageToJson(
                AppointmentChangedMessage(self.appointment, context={'prev_status': 'A'}).message
            ),
            {
                'appointmentId': self.appointment.id,
                'bookedForId': self.bci.id,
                'businessId': self.business.id,
                'bookedFrom': str(int(self.appointment.booked_from.timestamp())),
                'totalValue': '78.21',
                'total': {
                    'discount': '0.00',
                    'priceType': PriceType.STARTS_AT,
                    'value': '10.00',
                },
                'previousStatus': 'A',
            },
        )
