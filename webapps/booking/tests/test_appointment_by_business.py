# pylint: disable=protected-access
import typing as t
from datetime import (
    date,
    datetime,
    time,
)
from decimal import Decimal
from unittest.mock import patch, PropertyMock

import pytest
import pytz
from dateutil.relativedelta import relativedelta
from dateutil.tz import gettz
from django.conf import settings
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized
from rest_framework import serializers

from webapps.booking.appointment_wrapper import AppointmentWrapper

from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
    AppointmentTypeSM as AT,
    RepeatEndType,
    RepeatType,
    SubbookingServiceVariantMode as SVMode,
    BookingAction,
)
from webapps.booking.exceptions import BookingConflict, DrawForOverbookingException
from webapps.booking.factory.service_questions import QuestionsList
from webapps.booking.models import Appointment
from webapps.booking.serializers.appointment import AppointmentSerializer
from webapps.booking.serializers.booking import CalendarBookingSerializer
from webapps.booking.tests.test_appointment_base import (
    BaseTestAppointment,
    build_appointment_data,
)
from webapps.booking.tests.utils import create_appointment, create_business_appointment_data
from webapps.booking.timeslots.v1.consts import ERROR, INFO
from webapps.booking.tools.tools import iter_leaf_services
from webapps.business.baker_recipes import (
    service_recipe,
    service_variant_recipe,
    traveling_to_client_recipe,
)
from webapps.business.enums import (
    ComboPricing,
    ComboType,
    PriceType,
)
from webapps.business.models import (
    Business,
    ComboMembership,
    Resource,
    Service,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.resources import AnyResource
from webapps.business.service_price import ServicePrice
from webapps.kill_switch.models import KillSwitch
from webapps.reviews.models import Review
from webapps.turntracker.enums import TrackerMode
from webapps.turntracker.models import TrackerSettings, TrackerStaffList
from webapps.user.models import User


@pytest.mark.django_db
class TestAppointmentSimpleCase(BaseTestAppointment):
    def test_appointment_singlebooking_create(self):
        """Simple test case for singlebooking appointment creation."""
        data = build_appointment_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(10, 30)),
            staffer=self.staffer,
        )
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        appt = serializer.save()

        assert appt.appointment_type == AT.SINGLE
        assert appt.booked_from == self._dt_from_hour(time(10, 0))
        assert appt.booked_till == self._dt_from_hour(time(10, 30))
        assert appt.subbookings[0].staffer_id == self.staffer.id
        assert appt.subbookings[0].appliance is None
        assert appt.subbookings[0].autoassign is False
        assert appt.subbookings[0].service_variant_id == self.variant.id

    @patch(
        'webapps.business.models.Business.turntracker_enabled',
        new_callable=PropertyMock,
        return_value=True,
    )
    def test_appointment_singlebooking_create__autoasign_dry_run(self, _settings_mock):
        # add extra staffer; with single staffer Turn Tracker doesn't make sense
        self.create_extra_staffer()

        data = build_appointment_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(10, 30)),
            staffer=AnyResource,
        )
        data['dry_run'] = True
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        serializer.save()

        assert serializer.data['subbookings'][0]['autoassigned_staffer_id'] == self.staffer.id
        assert serializer.data['subbookings'][0]['staffer_id'] == self.staffer.id
        assert serializer.data['subbookings'][0]['autoassign'] is True

    @patch(
        'webapps.business.models.Business.turntracker_enabled',
        new_callable=PropertyMock,
        return_value=True,
    )
    def test_appointment_create_dry_run_keyed_in_payment(self, _settings_mock):
        self.create_extra_staffer()

        data = build_appointment_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(10, 30)),
            staffer=AnyResource,
        )
        data['dry_run'] = True

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()
        self.assertIsNotNone(serializer.data.get('is_deposit_available', None))

    @patch(
        'webapps.business.models.Business.turntracker_enabled',
        new_callable=PropertyMock,
        return_value=True,
    )
    def test_appointment_multibooking_matcher__autoasign_dry_run(self, _settings_mock):
        self._preexisting_booking(
            resources=[self.staffer],
            booked_from=self._dt_from_hour(time(9, 0)),
            booked_till=self._dt_from_hour(time(9, 30)),
            service_variant=self.variant,
        )
        self._preexisting_booking(
            resources=[self.staffer],
            booked_from=self._dt_from_hour(time(15, 0)),
            booked_till=self._dt_from_hour(time(15, 30)),
            service_variant=self.variant,
        )

        self.create_extra_staffer()
        self.variant.add_staffers([self.extra_staffer])

        data = {
            'overbooking': False,
            'dry_run': True,
            'customer': {
                'mode': ACMode.WALK_IN,
            },
            'subbookings': [
                {
                    'booked_from': self._dt_from_hour(time(11, 0)),
                    'booked_till': None,
                    'duration': 30,
                    'service_variant': {
                        'id': self.variant.id,
                        'mode': SVMode.VARIANT,
                    },
                    'staffer_id': AnyResource.id,
                    'appliance_id': None,
                },
                {
                    'booked_from': None,
                    'booked_till': None,
                    'duration': 30,
                    'service_variant': {
                        'id': self.variant.id,
                        'mode': SVMode.VARIANT,
                    },
                    'staffer_id': AnyResource.id,
                    'appliance_id': None,
                },
            ],
        }
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        serializer.save()

        result = serializer.data

        assert result['subbookings'][0]['autoassigned_staffer_id'] == self.extra_staffer.id
        assert result['subbookings'][0]['staffer_id'] == self.extra_staffer.id
        assert result['subbookings'][1]['autoassigned_staffer_id'] == self.extra_staffer.id
        assert result['subbookings'][1]['staffer_id'] == self.extra_staffer.id

    @patch(
        'webapps.business.models.Business.turntracker_enabled',
        new_callable=PropertyMock,
        return_value=True,
    )
    def test_today_appointment_singlebooking_create__autoasign_dry_run(self, _settings_mock):
        # add extra staffer; with single staffer Turn Tracker doesn't make sense
        self.create_extra_staffer()

        tz_datetime = datetime.combine(self.date, time(10, 00)).replace(
            tzinfo=gettz('America/Chicago')
        )
        with freeze_time(tz_datetime):
            baker.make(
                TrackerSettings,
                business_id=self.business.id,
                enabled=True,
                predict_conflicts=True,
                show_revenue=True,
                mode=TrackerMode.STAFF_MODE,
                predict_conflicts_interval=30,
            )
            TrackerStaffList.get_or_create_staff(self.business.id, tz_datetime.date())

            data = build_appointment_data(
                self.variant,
                booked_from=self._dt_from_hour(time(10, 0), specific_date=tz_datetime.date()),
                booked_till=self._dt_from_hour(time(10, 30), specific_date=tz_datetime.date()),
                staffer=AnyResource,
            )
            data['dry_run'] = True
            serializer = AppointmentSerializer(
                instance=None,
                data=data,
                context=self._appointment_context,
            )
            assert serializer.is_valid() is True, serializer.errors
            serializer.save()

            assert serializer.data['subbookings'][0]['autoassigned_staffer_id'] == self.staffer.id
            assert serializer.data['subbookings'][0]['staffer_id'] == self.staffer.id
            assert serializer.data['subbookings'][0]['autoassign'] is True

    @patch(
        'webapps.business.models.Business.turntracker_enabled',
        new_callable=PropertyMock,
        return_value=True,
    )
    def test_today_singlebooking_no_staffer__autoasign_dry_run(self, _settings_mock):
        # add extra staffer; with single staffer Turn Tracker doesn't make sense
        self.create_extra_staffer()

        tz_datetime = datetime.combine(self.date, time(10, 00)).replace(
            tzinfo=gettz('America/Chicago')
        )
        with freeze_time(tz_datetime):
            baker.make(
                TrackerSettings,
                business_id=self.business.id,
                enabled=True,
                predict_conflicts=True,
                show_revenue=True,
                mode=TrackerMode.STAFF_MODE,
                predict_conflicts_interval=30,
            )
            TrackerStaffList.get_or_create_staff(self.business.id, tz_datetime.date())

            service = baker.make(
                Service,
                business=self.business,
                active=True,
            )
            variant = baker.make(
                ServiceVariant,
                service=service,
                active=True,
                duration=relativedelta(minutes=30),
                time_slot_interval=relativedelta(minutes=self.INTERVAL),
            )
            # Staffer is not in TrackerStaffList:
            staffer = baker.make(
                Resource,
                business=self.business,
                staff_user=baker.make(
                    User,
                ),
                active=True,
                visible=True,
                type=Resource.STAFF,
                staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
            )
            service.add_staffers([staffer])

            data = build_appointment_data(
                variant,
                booked_from=self._dt_from_hour(time(10, 0), specific_date=tz_datetime.date()),
                booked_till=self._dt_from_hour(time(10, 30), specific_date=tz_datetime.date()),
                staffer=AnyResource,
            )
            data['dry_run'] = True
            serializer = AppointmentSerializer(
                instance=None,
                data=data,
                context=self._appointment_context,
            )
            assert serializer.is_valid() is True, serializer.errors
            serializer.save()

            assert serializer.data['subbookings'][0]['autoassigned_staffer_id'] is None
            assert serializer.data['subbookings'][0]['staffer_id'] == AnyResource.id
            assert serializer.data['subbookings'][0]['autoassign'] is True

    def test_appointment_single_booking_booked_till_on_midnight(self):
        booked_from = self._dt_from_hour(time(23, 30))
        booked_till = self._dt_from_hour(time(23, 59))

        data = build_appointment_data(
            variant=self.variant,
            booked_from=booked_from,
            booked_till=None,
            staffer=self.staffer,
        )
        data['dry_run'] = True
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        appt = serializer.save()

        assert appt.booked_from == booked_from
        assert appt.booked_till == booked_till

    def test_appointment_set_is_highlighted(self):
        booked_from = self._dt_from_hour(time(11, 15))

        data = build_appointment_data(
            variant=self.variant,
            booked_from=booked_from,
            booked_till=booked_from + self.variant.duration,
            staffer=self.staffer,
        )
        data['dry_run'] = False
        data['subbookings'][0]['is_highlighted'] = True

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()
        assert serializer.data['subbookings'][0]['is_highlighted']

    def test_appointment_set_is_staffer_requested(self):
        booked_from = self._dt_from_hour(time(11, 15))

        data = build_appointment_data(
            variant=self.variant,
            booked_from=booked_from,
            booked_till=booked_from + self.variant.duration,
            staffer=self.staffer,
        )
        data['dry_run'] = False
        data['subbookings'][0]['is_staffer_requested_by_client'] = True

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()
        assert serializer.data['subbookings'][0]['is_staffer_requested_by_client']

    def test_appointment_set_is_staffer_requested_with_autoassign(self):
        booked_from = self._dt_from_hour(time(11, 15))

        data = build_appointment_data(
            variant=self.variant,
            booked_from=booked_from,
            booked_till=booked_from + self.variant.duration,
            staffer=AnyResource,
        )
        data['dry_run'] = False
        data['subbookings'][0]['is_staffer_requested_by_client'] = True

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        with self.assertRaises(serializers.ValidationError):
            serializer.is_valid(raise_exception=True)

    def test_subbooking_resources_keys(self):
        self.service.add_appliances([self.appliance])
        data = build_appointment_data(
            self.variant,
            booked_from=self._dt_from_hour(time(11, 11)),
            booked_till=self._dt_from_hour(time(11, 42)),
            staffer=self.staffer,
            appliance=self.appliance,
        )
        serializer = AppointmentSerializer(
            data=data,
            context=self._appointment_context,
        )

        assert serializer.is_valid(), serializer.errors
        serializer.save()

        service = serializer.data['subbookings'][0]['service']
        assert self.staffer.id in service['staffer_ids']
        assert self.appliance.id in service['appliance_ids']

    def test_appointment_dry_run_duration_from_variant(self):
        data = build_appointment_data(
            variant=self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=None,
            staffer=self.staffer,
        )
        data['dry_run'] = True
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        appt = serializer.save()

        assert appt.booked_from == self._dt_from_hour(time(10, 0))
        assert appt.booked_till == self._dt_from_hour(time(10, 30))

    def test_appointment_dry_run_manual_duration(self):
        data = build_appointment_data(
            variant=self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=None,
            staffer=self.staffer,
        )
        data['subbookings'][0]['duration'] = 90
        data['dry_run'] = True
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        appt = serializer.save()

        assert appt.booked_from == self._dt_from_hour(time(10, 0))
        assert appt.booked_till == self._dt_from_hour(time(11, 30))

    def test_appointment_no_variant_no_appliance_create(self):
        """Test case for booking without variant and appliance."""
        data = build_appointment_data(
            variant='no variant',
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(10, 30)),
            staffer=self.staffer,
            appliance=None,
        )
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        appt = serializer.save()

        assert appt.subbookings[0].staffer_id == self.staffer.id
        assert appt.subbookings[0].autoassign is False
        # there is no service variant
        assert appt.subbookings[0].service_variant_id is None
        assert appt.subbookings[0].service_name == 'no variant'
        # business has appliance
        assert self.appliance.business_id == appt.appointment.business_id
        # but it is not assigned to booking
        assert appt.subbookings[0].appliance is None

    def test_appointment_no_variant_no_appliance_dry_run(self):
        """Test case for booking without variant and appliance."""
        data = build_appointment_data(
            variant='no variant',
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(10, 30)),
            staffer=self.staffer,
            appliance=None,
        )
        data['dry_run'] = True
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        appt = serializer.save()

        assert appt.subbookings[0].staffer_id == self.staffer.id
        assert appt.subbookings[0].autoassign is False
        # there is no service variant
        assert appt.subbookings[0].service_variant_id is None
        assert appt.subbookings[0].service_name == 'no variant'
        # business has appliance
        assert self.appliance.business_id == appt.appointment.business_id
        # but it is not assigned to booking
        assert appt.subbookings[0].appliance is None

    def test__resource_selection_required(self):
        extra_staffer = baker.make(
            Resource,
            business=self.business,
            active=True,
            visible=True,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        )
        self._make_resource_calendar(extra_staffer)

        # outside working hours; any staffer
        data = build_appointment_data(
            variant=self.variant,
            booked_from=self._dt_from_hour(time(7, 0)),
            booked_till=self._dt_from_hour(time(7, 30)),
            staffer=AnyResource,
            appliance=None,
        )
        data['dry_run'] = True

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        serializer.save()
        assert serializer.data['_resource_selection_required'] is True

        # outside working hours: selected staffer
        data['subbookings'][0]['staffer_id'] = self.staffer.id
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        serializer.save()
        assert serializer.data['_resource_selection_required'] is False

        # both staffers booking conflict: any staffer
        create_appointment(
            [
                {
                    'service_variant': self.variant,
                    'booked_from': self._dt_from_hour(time(7, 0)),
                    'booked_till': self._dt_from_hour(time(7, 30)),
                    'staffer': self.staffer,
                }
            ],
            business=self.business,
        )
        create_appointment(
            [
                {
                    'service_variant': self.variant,
                    'booked_from': self._dt_from_hour(time(7, 0)),
                    'booked_till': self._dt_from_hour(time(7, 30)),
                    'staffer': extra_staffer,
                }
            ],
            business=self.business,
        )
        data['subbookings'][0]['staffer_id'] = AnyResource.id
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        serializer.save()
        assert serializer.data['_resource_selection_required'] is True, serializer.data[
            'subbookings'
        ][0]['_availability']

    def test_appointment_singlebooking_bci_other_business(self):
        data = build_appointment_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(10, 30)),
            staffer=self.staffer,
        )
        bci = baker.make(BusinessCustomerInfo)
        data['customer']['mode'] = ACMode.CUSTOMER_CARD
        data['customer']['id'] = bci.id
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is False

    def test_appointment_singlebooking_service_variant_other_business(self):
        shitty_service = baker.make(Service, active=True)
        shitty_variant = baker.make(
            ServiceVariant,
            service=shitty_service,
            active=True,
            duration=relativedelta(minutes=30),
            time_slot_interval=relativedelta(minutes=15),
        )
        data = build_appointment_data(
            shitty_variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(10, 30)),
            staffer=self.staffer,
        )
        data['subbookings'][0]['staffer_id'] = None
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )

        assert serializer.is_valid() is False

    def test_appointment_singlebooking_create_overbooking(self):
        """Simple test case for singlebooking appointment overbooking."""
        # create existing booking on this slot
        self._preexisting_booking(
            resources=[self.staffer],
            booked_from=self._dt_from_hour(time(10, 15)),
            booked_till=self._dt_from_hour(time(10, 45)),
        )
        assert self.staffer.subbookings.count() == 1
        assert self.staffer.subbookings.get().padded_booked_range is not None

        # try to create a conflicting booking
        data = build_appointment_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(10, 30)),
            staffer=self.staffer,
            overbooking=False,
        )
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        with pytest.raises(BookingConflict):
            # BookingConflict should be raised
            serializer.save()

        # use overbooking=True to ignore the conflict and save anyway
        data = build_appointment_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(10, 30)),
            staffer=self.staffer,
            overbooking=True,
        )
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        # no exception raised
        appt = serializer.save()

        assert appt.booked_from == self._dt_from_hour(time(10, 0))
        assert appt.booked_till == self._dt_from_hour(time(10, 30))
        assert (
            self.staffer.subbookings.filter(
                booked_from__gte=self._dt_from_hour(time(10, 0)),
                booked_till__lte=self._dt_from_hour(time(10, 45)),
            ).count()
            == 2
        )

    def test_appointment_inside_gap_hole(self):
        """Create a new appointment inside gap hole of existing appointment."""
        # make pre-existing appointment with gap hole
        self._preexisting_booking(
            resources=[self.staffer],
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(11, 30)),
            service_variant=self.gap_hole_variant,
        )

        # try to create an appointment inside gap hole
        data = build_appointment_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 30)),
            booked_till=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
        )
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        serializer.save()

    def test_appointment_gap_hole_duration_validation(self):
        """Check validation when duration is too short for gap hole."""
        data = build_appointment_data(
            self.gap_hole_variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(10, 30)),
            staffer=self.staffer,
        )
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is False
        assert 'subbookings' in serializer.errors
        assert 'booked_till' in serializer.errors['subbookings'][0]

    def test_appointment_gap_hole_in_multibooking_matcher(self):
        """Check if multibooking matcher finds best match for gap hole."""
        # make pre-existing appointment with gap hole
        self._preexisting_booking(
            resources=[self.staffer],
            booked_from=self._dt_from_hour(time(12, 0)),
            booked_till=self._dt_from_hour(time(12, 30)),
            service_variant=self.variant,
        )

        # try to create a multibooking appointment
        # with second service with gap hole enveloping existing appointment
        data = {
            'overbooking': False,
            'dry_run': True,
            'customer': {
                'mode': ACMode.WALK_IN,
            },
            'subbookings': [
                {
                    'booked_from': self._dt_from_hour(time(11, 0)).strftime(
                        settings.DATETIME_FORMAT
                    ),
                    'booked_till': None,
                    'duration': 30,
                    'service_variant': {
                        'id': self.variant.id,
                        'mode': SVMode.VARIANT,
                    },
                    'staffer_id': self.staffer.id,
                    'appliance_id': None,
                },
                {
                    'booked_from': None,
                    'booked_till': None,
                    'duration': 90,
                    'service_variant': {
                        'id': self.gap_hole_variant.id,
                        'mode': SVMode.VARIANT,
                    },
                    'staffer_id': self.staffer.id,
                    'appliance_id': None,
                },
            ],
        }
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        serializer.save()  # dry run doesn't save

        # serialize dry_run result and send it again!
        result = serializer.data
        result.update(
            {
                'overbooking': False,
                'dry_run': False,
                'customer': {
                    'mode': ACMode.WALK_IN,
                },
            }
        )
        serializer = AppointmentSerializer(
            instance=None,
            data=result,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        appointment = serializer.save()  # this time it is saved

        assert len(appointment.subbookings) == 2
        first, second = appointment.subbookings
        assert first.service_variant_id == self.variant.id
        assert second.service_variant_id == self.gap_hole_variant.id
        assert first.booked_from == self._dt_from_hour(time(11, 0))
        assert first.booked_till == self._dt_from_hour(time(11, 30))
        assert second.booked_from == self._dt_from_hour(time(11, 30))
        assert second.booked_till == self._dt_from_hour(time(13, 00))

    def test_appointment_client_type_business_directly(self):
        booking_data = build_appointment_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(10, 30)),
            staffer=self.staffer,
        )
        bci = baker.make(BusinessCustomerInfo, business=self.business)
        # raise Exception(bci)
        booking_data['customer']['mode'] = ACMode.CUSTOMER_CARD
        booking_data['customer']['id'] = bci.id

        serializer = AppointmentSerializer(
            instance=None,
            data=booking_data,
            context=self._appointment_context,
        )
        serializer.is_valid()
        assert serializer.is_valid() is True

        appt = serializer.save()
        appt.appointment.booked_for.refresh_from_db()
        assert appt.appointment.booked_for.client_type == (
            BusinessCustomerInfo.CLIENT_TYPE__BUSINESS_DIRECTLY
        )

    def test_validate_subbookings_non_empty(self):
        data = {
            'overbooking': False,
            'dry_run': True,
            'subbookings': [],
            'customer': {'mode': ACMode.WALK_IN},
        }
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is False
        assert 'subbookings' in serializer.errors

    def test_validate_subbookings_first_booked_from_required(self):
        data = build_appointment_data(
            variant=self.variant,
            booked_from=None,
            booked_till=None,
            staffer=self.staffer,
        )
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is False
        assert 'subbookings' in serializer.errors

    def test_availability_info_type(self):
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.variant.id,
                    # outside working hours
                    'booked_from': self._dt_from_hour(time(19, 0)),
                    'booked_till': self._dt_from_hour(time(20, 0)),
                    'staffer_id': AnyResource.id,
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        serializer.save()

        result = serializer.data
        availability = result['subbookings'][0]['_availability']

        # for single-staffer service
        assert result['_resource_selection_required'] is False
        assert availability['staffers'][self.staffer.id]['type'] == 'info'
        assert availability['staffers'][AnyResource.id]['type'] == 'error'

        # make service not a single-staffer service
        self.extra_staffer = baker.make(
            Resource,
            business=self.business,
            active=True,
            visible=True,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        )
        self._make_resource_calendar(self.extra_staffer)
        self.variant.add_staffers([self.extra_staffer])

        try:
            del self.business.resource_ids
        except AttributeError:
            pass

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        serializer.save()

        result = serializer.data
        assert result['_resource_selection_required'] is True

    def test_empty_booked_ranges_padded(self):
        data = build_appointment_data(
            variant='custom',
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=None,
            staffer=AnyResource,
        )
        data['dry_run'] = True
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is False

    def test_edit__notify_about_reschedule_500(self):
        KillSwitch.objects.filter(name=KillSwitch.System.AUTO_NOTIFY_ABOUT_RESCHEDULE).update(
            is_killed=True
        )
        appointment = create_appointment()
        appointment_wrapper = AppointmentWrapper(appointment.subbookings)
        data = create_business_appointment_data(
            [],
            dry_run=True,
        )
        data['_notify_about_reschedule'] = False

        serializer = AppointmentSerializer(
            instance=appointment_wrapper,
            data=data,
            context=self._appointment_context,
        )
        assert not serializer.is_valid()
        assert 'subbookings' in serializer.errors

    def test_manual_autoassign(self):
        data = build_appointment_data(
            variant=self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=None,
            staffer=self.staffer,
        )
        data['dry_run'] = True
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()
        assert serializer.data['subbookings'][0]['autoassign'] is False

        data['subbookings'][0]['staffer_id'] = None
        data['subbookings'][0]['autoassigned_staffer_id'] = self.staffer.id
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()
        assert serializer.data['subbookings'][0]['autoassign'] is True
        assert serializer.data['subbookings'][0]['autoassigned_staffer_id'] == self.staffer.id
        assert serializer.data['subbookings'][0]['staffer_id'] == self.staffer.id

        result = serializer.data
        result.update(
            {
                'overbooking': False,
                'dry_run': False,
                'customer': {
                    'mode': ACMode.WALK_IN,
                },
            }
        )
        serializer = AppointmentSerializer(
            instance=None,
            data=result,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is True, serializer.errors
        appointment = serializer.save()  # this time it is saved

        assert appointment.subbookings[0].autoassign is True
        assert appointment.subbookings[0].staffer_id == self.staffer.id
        assert not hasattr(appointment.subbookings[0], 'autoassigned_staffer_id')

    def test_custom_service_any_appliance(self):
        self.appliance.delete()
        assert not self.business.resources.filter(type=Resource.APPLIANCE).exists()
        data = build_appointment_data(
            variant='custom variant',
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            appliance=AnyResource,
        )
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()

    def test_custom_service_no_staffer(self):
        data = build_appointment_data(
            variant='custom variant',
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(11, 0)),
            staffer=None,
        )
        data['dry_run'] = True
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid() is False

    def test_edit_appointment_variant_no_staffers(self):
        appt = create_appointment(
            [
                {
                    'service_variant': self.variant,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                    'staffer': self.staffer,
                }
            ],
            business=self.business,
        )

        self.variant.remove_staffers([self.staffer])

        wrapper = AppointmentWrapper(appt.subbookings)
        serializer = AppointmentSerializer(instance=wrapper, context=self._appointment_context)
        data = serializer.data
        data['dry_run'] = True
        data['overbooking'] = False
        data['_notify_about_reschedule'] = False

        serializer = AppointmentSerializer(
            instance=wrapper,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors

    def test_single_staffer_business_any_resource(self):
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.variant.id,
                    'booked_from': self._dt_from_hour(time(20, 0)),
                    'booked_till': self._dt_from_hour(time(21, 0)),
                    'staffer_id': AnyResource.id,
                },
            ],
            dry_run=True,
        )
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()

        data['dry_run'] = False
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()

    def test_single_appliance_initial_dry_run(self):
        self.create_extra_staffer()
        data = create_business_appointment_data(
            [
                {
                    'booked_from': self._dt_from_hour(time(20, 0)),
                    'booked_till': self._dt_from_hour(time(21, 0)),
                    'staffer_id': AnyResource.id,
                    'appliance_id': AnyResource.id,
                },
            ],
            dry_run=True,
        )
        data['subbookings'][0]['service_variant'] = None
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()
        # don't autoselect single appliance
        assert serializer.data['subbookings'][0]['appliance_id'] == AnyResource.id

    def test_clear_wrong_any_resource(self):
        self.variant.service.set_appliances([self.appliance])
        self.variant.remove_staffers([self.staffer])
        assert not self.variant.staffer_ids
        assert self.variant.appliance_ids

        data = create_business_appointment_data(
            [
                {
                    'booked_from': self._dt_from_hour(time(20, 0)),
                    'booked_till': self._dt_from_hour(time(21, 0)),
                    'staffer_id': AnyResource.id,
                    'appliance_id': self.appliance.id,
                    'service_variant_id': self.variant.id,
                },
            ],
            dry_run=False,
        )
        data['subbookings'][0]['autoassigned_staffer_id'] = AnyResource.id

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        assert serializer.validated_data['subbookings'][0]['staffer'] is None
        appointment = serializer.save()
        assert appointment.subbookings[0].staffer is None

    def test_appointment_actions(self):
        dt = date(2020, 6, 1)
        appointment = create_appointment(
            [
                {
                    'service_variant': self.variant,
                    'booked_from': datetime(dt.year, dt.month, dt.day, 10, 00).replace(
                        tzinfo=pytz.UTC
                    ),
                    'booked_till': datetime(dt.year, dt.month, dt.day, 11, 00).replace(
                        tzinfo=pytz.UTC
                    ),
                    'staffer': self.staffer,
                }
            ],
            business=self.business,
        )
        appointment.payable = True
        appointment.save()

        wrapper = AppointmentWrapper(appointment.subbookings)

        with freeze_time(datetime(dt.year, dt.month, dt.day, 10, 30, tzinfo=pytz.UTC)):
            serializer = AppointmentSerializer(
                instance=wrapper,
                context=self._appointment_context,
            )
            appt = serializer.data
            assert appt['actions'][BookingAction.CANCEL] is True

        with freeze_time(datetime(dt.year, dt.month, dt.day, 11, 10, tzinfo=pytz.UTC)):
            serializer = AppointmentSerializer(
                instance=wrapper,
                context=self._appointment_context,
            )
            appt = serializer.data
            assert appt['actions'][BookingAction.NO_SHOW] is True

        review = baker.make(
            Review,
            business=appointment.business,
            subbooking=appointment.subbookings[0],
            review='KoKoKo',
        )
        with freeze_time(datetime(dt.year, dt.month, dt.day, 11, 10, tzinfo=pytz.UTC)):
            serializer = AppointmentSerializer(
                instance=wrapper,
                context=self._appointment_context,
            )
            appt = serializer.data
            assert appt['actions'][BookingAction.CANCEL] is False
            assert appt['actions'][BookingAction.NO_SHOW] is False

        booking = CalendarBookingSerializer(
            instance=appointment.subbookings[0],
            context={
                'business': appointment.business,
                'staffer': appointment.subbookings[0].staffer,
                'single_category': appointment.business.is_single_category,
            },
        ).data

        assert booking['review'] == review.id


class TestMultipleParametrizedAppointmentScenarios(BaseTestAppointment):
    @parameterized.expand(
        [
            (  # appointment inside gap hole
                [
                    {
                        'booked_from': time(10, 0),
                        'booked_till': time(11, 30),
                        'service_variant': 'gap_hole',
                    }
                ],
                {
                    'booked_from': time(10, 30),
                    'booked_till': time(11, 0),
                    'service_variant': 'default',
                },
                False,
            ),
            (  # gap hole appointment enveloping existing appointment
                [
                    {
                        'booked_from': time(10, 30),
                        'booked_till': time(11, 0),
                        'service_variant': 'default',
                    }
                ],
                {
                    'booked_from': time(10, 0),
                    'booked_till': time(11, 30),
                    'service_variant': 'gap_hole',
                },
                False,
            ),
            (  # gap holes overlapping at the start of existing one
                [
                    {
                        'booked_from': time(10, 30),
                        'booked_till': time(12, 00),
                        'service_variant': 'gap_hole',
                    }
                ],
                {
                    'booked_from': time(10, 0),
                    'booked_till': time(11, 30),
                    'service_variant': 'gap_hole',
                },
                False,
            ),
            (  # gap holes overlapping at the end of existing one
                [
                    {
                        'booked_from': time(10, 0),
                        'booked_till': time(11, 30),
                        'service_variant': 'gap_hole',
                    }
                ],
                {
                    'booked_from': time(10, 30),
                    'booked_till': time(12, 0),
                    'service_variant': 'gap_hole',
                },
                False,
            ),
            (  # gap holes conflicting at the start of existing one
                [
                    {
                        'booked_from': time(11, 15),
                        'booked_till': time(12, 45),
                        'service_variant': 'gap_hole',
                    }
                ],
                {
                    'booked_from': time(10, 0),
                    'booked_till': time(11, 30),
                    'service_variant': 'gap_hole',
                },
                True,
            ),
            (  # gap holes conflicting at the end of existing one
                [
                    {
                        'booked_from': time(10, 0),
                        'booked_till': time(11, 30),
                        'service_variant': 'gap_hole',
                    }
                ],
                {
                    'booked_from': time(11, 15),
                    'booked_till': time(12, 45),
                    'service_variant': 'gap_hole',
                },
                True,
            ),
            (  # gap holes conflicting at both ends
                [
                    {
                        'booked_from': time(10, 0),
                        'booked_till': time(11, 30),
                        'service_variant': 'gap_hole',
                    }
                ],
                {
                    'booked_from': time(10, 15),
                    'booked_till': time(11, 45),
                    'service_variant': 'gap_hole',
                },
                True,
            ),
        ]
    )
    def test_appointment_scenario(self, existing, create, conflict):
        # make some pre-existing appointments
        for data in existing:
            self._preexisting_booking(
                resources=[self.staffer],
                booked_from=self._dt_from_hour(data['booked_from']),
                booked_till=self._dt_from_hour(data['booked_till']),
                service_variant=self.variants[data['service_variant']],
            )

        # try to create a new appointment using the whole infrastructure
        data = build_appointment_data(
            self.variants[create['service_variant']],
            booked_from=self._dt_from_hour(create['booked_from']),
            booked_till=self._dt_from_hour(create['booked_till']),
            staffer=self.staffer,
            overbooking=False,
        )
        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        if conflict:
            with pytest.raises(BookingConflict):
                serializer.save()
        else:
            serializer.save()

        assert self.staffer.subbookings.count() == len(existing) + int(not conflict)


@pytest.mark.django_db
class TestAppointmentTravelingInfoCase(BaseTestAppointment):
    @staticmethod
    def _make_business_mobile(business: Business, traveling_only: t.Optional['bool'] = False):
        traveling_to_client_recipe.make(
            business=business,
            traveling_only=traveling_only,
        )

    @staticmethod
    def _make_service_mobile(service: Service):
        service.is_traveling_service = True
        service.save()

    def test_business_not_traveling_service_not_traveling(self):
        serializer = self.appointment_serializer()
        assert serializer.is_valid()

    def appointment_serializer(self, **kwargs) -> AppointmentSerializer:
        data = build_appointment_data(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            booked_till=self._dt_from_hour(time(10, 30)),
            staffer=self.staffer,
        )
        if kwargs:
            data.update(kwargs)

        return AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )

    def test_business_combined_service_traveling(self):
        self._make_business_mobile(self.business)
        self._make_service_mobile(self.variant.service)

        serializer = self.appointment_serializer()

        assert not serializer.is_valid()
        assert serializer.errors['traveling']
        assert serializer.errors['traveling'][0].code == 'required'

    def test_business_combined_service_not_traveling(self):
        self._make_business_mobile(self.business)

        serializer = self.appointment_serializer()
        assert serializer.is_valid(), serializer.errors

    def test_business_traveling_only_service_traveling(self):
        self._make_business_mobile(self.business, traveling_only=True)
        self._make_service_mobile(self.variant.service)

        serializer = self.appointment_serializer()

        assert not serializer.is_valid()
        assert serializer.errors['traveling']
        assert serializer.errors['traveling'][0].code == 'required'

    def test_business_traveling_only_service_not_traveling(self):
        self._make_business_mobile(self.business, traveling_only=True)

        serializer = self.appointment_serializer()

        assert not serializer.is_valid()
        assert serializer.errors['traveling']
        assert serializer.errors['traveling'][0].code == 'required'

    def test_business_not_none_traveling(self):
        serializer = self.appointment_serializer(dry_run=True, traveling={})
        assert serializer.is_valid()


@pytest.mark.django_db
class CreateAppointmentComboTestCase(BaseTestAppointment):
    def setUp(self):
        super().setUp()

        self.extra_staffer = baker.make(
            Resource,
            business=self.business,
            active=True,
            visible=True,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        )
        self._make_resource_calendar(self.extra_staffer)

        self.combo_children = [
            service_variant_recipe.make(
                service=service_recipe.make(
                    business=self.business, questions=QuestionsList([f'Question {x}'])
                ),
                duration=relativedelta(minutes=10 * x),
            )
            for x in range(1, 4)
        ]
        self.combo = service_variant_recipe.make(
            service=service_recipe.make(business=self.business, combo_type=ComboType.SEQUENCE),
            combo_pricing=ComboPricing.CUSTOM,
        )
        for i, child in enumerate(self.combo_children):
            baker.make(
                ComboMembership,
                combo=self.combo,
                child=child,
                order=i,
                gap_time=relativedelta(),
                price=Decimal('10.00'),
                type=PriceType.FIXED,
            )
            child.service.add_staffers([self.staffer])

        for variant in self.combo_children[1:]:
            variant.service.add_staffers([self.extra_staffer])

    def test_combo_appointment_simple(self):
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        appt = serializer.save()

        assert len(appt.subbookings) == 1
        subbooking = appt.subbookings[0]
        assert len(subbooking.combo_children) == 3
        assert subbooking.booked_from == self._dt_from_hour(time(10, 0))
        assert subbooking.booked_till == self._dt_from_hour(time(11, 0))
        self.assertEqual(
            appt.total,
            ServicePrice(
                Decimal('30.00'),
                PriceType.FIXED,
                should_display_price_types=PriceType.should_display().union({PriceType.VARIES}),
            ),
        )
        self.assertListEqual(
            list(appt.service_questions),
            [
                'Question 1',
                'Question 2',
                'Question 3',
            ],
        )

        result = serializer.data
        assert result['_resource_selection_required'] is False
        assert result['subbookings'][0]['service']['combo_type'] == self.combo.service.combo_type

        result.update({'dry_run': False, 'overbooking': False})

        serializer = AppointmentSerializer(
            instance=None,
            data=result,
            context=self._appointment_context,
        )

        assert serializer.is_valid(), serializer.errors
        appt = serializer.save()

        # refresh from db
        appt = Appointment.objects.get(pk=appt.appointment.id)

        assert len(appt.subbookings) == 1
        assert len(appt.subbookings[0].combo_children) == 3

        children = appt.subbookings[0].combo_children
        assert children[0].booked_from == self._dt_from_hour(time(10))
        assert children[0].booked_till == self._dt_from_hour(time(10, 10))
        assert children[0].staffer is not None

        assert children[1].booked_from == self._dt_from_hour(time(10, 10))
        assert children[1].booked_till == self._dt_from_hour(time(10, 30))
        assert children[1].staffer is not None

        assert children[2].booked_from == self._dt_from_hour(time(10, 30))
        assert children[2].booked_till == self._dt_from_hour(time(11))
        assert children[2].staffer is not None

        subbooking = appt.subbookings[0]
        assert subbooking.booked_from == self._dt_from_hour(time(10, 0))
        assert subbooking.booked_till == self._dt_from_hour(time(11))

    def test_combo_appointment_expanded_with_unused_appliance(self):
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                    'combo_children': [
                        {
                            'service_variant_id': child.id,
                            'staffer_id': self.staffer.id,
                            'appliance_id': -1,
                        }
                        for child in self.combo_children
                    ],
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()

        result = serializer.data
        result.update({'dry_run': False, 'overbooking': False})

        serializer = AppointmentSerializer(
            instance=None,
            data=result,
            context=self._appointment_context,
        )

        assert serializer.is_valid(), serializer.errors
        appt = serializer.save()

        assert appt

    def test_combo_appointment_staffer_not_performing_service(self):
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                    'combo_children': [
                        {
                            'service_variant_id': child.id,
                            'staffer_id': self.staffer.id,
                        }
                        for child in self.combo_children
                    ],
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        appt = serializer.save()
        children = appt.subbookings[0].combo_children
        assert children[0].staffer == self.staffer
        assert self.extra_staffer.id not in children[0]._availability['staffers']

        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                    'combo_children': [
                        {
                            'service_variant_id': child.id,
                            'staffer_id': self.extra_staffer.id,
                        }
                        for child in self.combo_children
                    ],
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        appt = serializer.save()
        children = appt.subbookings[0].combo_children
        assert children[0].staffer == self.extra_staffer
        assert children[0]._availability['staffers'][self.extra_staffer.id]['ok'] is True

        # copy combo_children expanded in matcher
        data['subbookings'][0]['combo_children'] = serializer.data['subbookings'][0][
            'combo_children'
        ]
        data['dry_run'] = False

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        appt = serializer.save()
        appt.appointment.refresh_from_db()

        children = appt.subbookings[0].combo_children
        assert children[0].staffer == self.extra_staffer

    def test_draw_for_overbooking_exception(self):
        create_appointment(
            [
                {
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                    'staffer': self.staffer,
                }
            ],
            business=self.business,
        )
        create_appointment(
            [
                {
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                    'staffer': self.extra_staffer,
                }
            ],
            business=self.business,
        )

        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                    'combo_children': [
                        {
                            'service_variant_id': child.id,
                            'staffer_id': AnyResource.id,
                        }
                        for child in self.combo_children
                    ],
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()

        result = serializer.data
        combo_children = result['subbookings'][0]['combo_children']
        for child in combo_children:
            assert child['_availability']['staffers'][-1]['ok'] is False
        assert result['_resource_selection_required'] is True

        free_from = self._dt_from_hour(time(11)).strftime(settings.DATETIME_FORMAT)
        assert (
            combo_children[1]['_availability']['staffers'][self.extra_staffer.id]['free_from']
            == free_from
        )

        result['dry_run'] = False
        result['overbooking'] = True

        serializer = AppointmentSerializer(
            instance=None,
            data=result,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        with self.assertRaises(DrawForOverbookingException):
            serializer.save()

    def test_augment_wait_time(self):
        first = self.combo.combo_children_through.first()
        # set service_variant gap_time to 5
        first.child.gap_time = relativedelta(minutes=5)
        first.child.save()
        # set service_variant in combo gap_time to different value
        first.gap_time = relativedelta(minutes=10)
        first.save()

        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()
        appt = serializer.data
        children = appt['subbookings'][0]['combo_children']
        # make sure children uses gap_time from combo
        assert children[1]['wait_time'] == {'minutes': 10}

    def test_change_combo_appointment_to_single_appointment(self):
        appointment = create_appointment(
            [
                {
                    'service_variant_id': self.combo.id,
                    'combo_children': [
                        {'service_variant_id': child.id} for child in self.combo_children
                    ],
                }
            ]
        )
        assert appointment.subbookings[0].combo_children

        appointment_wrapper = AppointmentWrapper(appointment.subbookings)
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo_children[0].id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                }
            ],
            dry_run=True,
        )
        data['_notify_about_reschedule'] = True
        assert not data['subbookings'][0].get('combo_children')

        serializer = AppointmentSerializer(
            instance=appointment_wrapper,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        validated_data = serializer.validated_data
        assert not validated_data['subbookings'][0].get('combo_children')

        appointment = serializer.save()
        result_data = serializer.data
        assert not result_data['subbookings'][0].get('combo_children')

    def test_change_combo_appointment_to_repeating_single_appointment(self):
        expected_service_variant_id = self.combo_children[0].id

        appointment = create_appointment(
            [
                {
                    'service_variant_id': self.combo.id,
                    'combo_children': [
                        {'service_variant_id': child.id} for child in self.combo_children
                    ],
                },
            ]
        )
        appointment_wrapper = AppointmentWrapper(appointment.subbookings)
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': expected_service_variant_id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                }
            ],
            dry_run=False,
            new_repeating={
                'repeat': RepeatType.EVERY_WEEK,
                'end_type': RepeatEndType.AFTER_N_BOOKINGS,
                'repeat_number': 2,
            },
        )
        data['subbookings'][0]['id'] = appointment.subbookings[0].id
        data['_notify_about_reschedule'] = False

        serializer = AppointmentSerializer(
            instance=appointment_wrapper,
            data=data,
            context=self._appointment_context,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        self.assertEqual(len(serializer.validated_data['subbookings']), 1)
        self.assertFalse(serializer.validated_data['subbookings'][0].get('combo_children'))

        serializer.save()
        self.assertEqual(len(serializer.data['subbookings']), 1)
        self.assertListEqual(serializer.data['subbookings'][0]['combo_children'], [])

        appointment = Appointment.objects.get(id=appointment.id)
        self.assertEqual(len(appointment.subbookings), 1)
        self.assertEqual(appointment.subbookings[0].service_variant_id, expected_service_variant_id)

    def test_change_ex_multibooking_to_repeating_single_appointment(self):
        expected_service_variant_id = self.combo_children[0].id

        appointment = create_appointment(
            [
                {
                    'service_variant_id': self.combo.id,
                    'combo_children': [
                        {'service_variant_id': child.id} for child in self.combo_children
                    ],
                },
                {
                    'service_variant_id': expected_service_variant_id,
                },
            ]
        )
        appointment_wrapper = AppointmentWrapper(appointment.subbookings)
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': expected_service_variant_id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                },
            ],
            dry_run=False,
            new_repeating={
                'repeat': RepeatType.EVERY_WEEK,
                'end_type': RepeatEndType.AFTER_N_BOOKINGS,
                'repeat_number': 2,
            },
        )
        expected_subbooking_id = appointment.subbookings[1].id
        data['subbookings'][0]['id'] = expected_subbooking_id
        data['_notify_about_reschedule'] = False

        serializer = AppointmentSerializer(
            instance=appointment_wrapper,
            data=data,
            context=self._appointment_context,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        self.assertEqual(len(serializer.validated_data['subbookings']), 1)
        self.assertFalse(serializer.validated_data['subbookings'][0].get('combo_children'))

        serializer.save()
        self.assertEqual(len(serializer.data['subbookings']), 1)
        self.assertListEqual(serializer.data['subbookings'][0]['combo_children'], [])

        appointment = Appointment.objects.get(id=appointment.id)
        self.assertEqual(len(appointment.subbookings), 1)
        self.assertEqual(appointment.subbookings[0].service_variant_id, expected_service_variant_id)

    def test_parallel_draw_for_overbooking_exception(self):
        self.combo.service.combo_type = ComboType.PARALLEL
        self.combo.service.save()

        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                    'combo_children': [
                        {
                            'service_variant_id': child.id,
                            'staffer_id': AnyResource.id,
                        }
                        for child in self.combo_children
                    ],
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()

        result = serializer.data
        subbooking = result['subbookings'][0]
        child = subbooking['combo_children'][0]
        assert subbooking['_availability']['staffers'][-1]['ok'] is False

        assert child['_availability']['staffers'][-1]['ok'] is False
        assert child['_availability']['staffers'][-1]['message'] == 'Select staffer manually'
        result['dry_run'] = False
        result['overbooking'] = True

        serializer = AppointmentSerializer(
            instance=None,
            data=result,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        with self.assertRaises(DrawForOverbookingException):
            serializer.save()

        for child in result['subbookings'][0]['combo_children']:
            child['staffer_id'] = self.staffer.id

        serializer = AppointmentSerializer(
            instance=None,
            data=result,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        assert serializer.save()

    def _matcher_required_parallel_data(self, through_1, through_2):
        # prepare parallel combo
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                    'combo_children': [
                        {
                            'service_variant_id': through_1.child_id,
                            'staffer_id': AnyResource.id,
                        },
                        {
                            'service_variant_id': through_2.child_id,
                            'staffer_id': AnyResource.id,
                        },
                    ],
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()

        return serializer.data

    @patch(
        'webapps.business.models.Business.turntracker_enabled',
        new_callable=PropertyMock,
        return_value=True,
    )
    def test_matcher_combo_parallel_autoassign_staffers(self, _settings_mock):
        self.combo.service.combo_type = ComboType.PARALLEL
        self.combo.service.save()

        self.extra_staffer = baker.make(
            Resource,
            business=self.business,
            active=True,
            visible=True,
            type=Resource.STAFF,
        )
        self._make_resource_calendar(self.extra_staffer)

        self.combo.combo_children_through.first().delete()
        through_1, through_2 = self.combo.combo_children_through.all()
        through_1.child.service.set_staffers([self.staffer])
        through_2.child.service.set_staffers([self.staffer, self.extra_staffer])

        result = self._matcher_required_parallel_data(through_1, through_2)

        child1 = result['subbookings'][0]['combo_children'][0]
        child2 = result['subbookings'][0]['combo_children'][1]

        assert child1['_availability']['staffers'][-1]['ok'] is True
        assert child2['_availability']['staffers'][-1]['ok'] is True

        assert child1['_availability']['staffers'][self.staffer.id]['ok'] is True
        assert child2['_availability']['staffers'][self.extra_staffer.id]['ok'] is True

        assert child1['autoassigned_staffer_id'] == self.staffer.id
        assert child2['autoassigned_staffer_id'] == self.extra_staffer.id

    def test_matcher_required_parallel(self):
        # prepare parallel combo
        self.combo.service.combo_type = ComboType.PARALLEL
        self.combo.service.save()

        # with 2 children; first with single staffer
        self.combo.combo_children_through.first().delete()
        through_1, through_2 = self.combo.combo_children_through.all()
        through_1.child.service.set_staffers([self.staffer])

        result = self._matcher_required_parallel_data(through_1, through_2)

        child1 = result['subbookings'][0]['combo_children'][0]
        child2 = result['subbookings'][0]['combo_children'][1]

        assert child1['_availability']['staffers'][-1]['ok'] is True
        assert child2['_availability']['staffers'][-1]['ok'] is True

        assert child1['_availability']['staffers'][self.staffer.id]['ok'] is True

        assert child2['_availability']['staffers'][self.staffer.id]['ok'] is False
        assert child2['_availability']['staffers'][self.staffer.id]['message'] == (
            'Required for another parallel service'
        )

    def test_matcher_selected_parallel(self):
        # prepare parallel combo
        self.combo.service.combo_type = ComboType.PARALLEL
        self.combo.service.save()

        # with 2 children
        self.combo.combo_children_through.first().delete()
        through_1, through_2 = self.combo.combo_children_through.all()

        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                    'combo_children': [
                        {
                            'service_variant_id': through_1.child_id,
                            'staffer_id': self.staffer.id,
                        },
                        {
                            'service_variant_id': through_2.child_id,
                            'staffer_id': AnyResource.id,
                        },
                    ],
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()

        result = serializer.data
        child1 = result['subbookings'][0]['combo_children'][0]
        child2 = result['subbookings'][0]['combo_children'][1]

        assert child1['_availability']['staffers'][-1]['ok'] is True
        assert child2['_availability']['staffers'][-1]['ok'] is True

        assert child1['_availability']['staffers'][self.staffer.id]['ok'] is True

        assert child2['_availability']['staffers'][self.staffer.id]['ok'] is False
        assert child2['_availability']['staffers'][self.staffer.id]['message'] == (
            'Selected for another parallel service'
        )

    def test_combo_resource_selection_required(self):
        first_child = self.combo_children[0]
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(19, 0)),
                    'booked_till': self._dt_from_hour(time(20, 0)),
                    'combo_children': [
                        {
                            'service_variant_id': child.id,
                            'staffer_id': (
                                self.staffer.id if child is first_child else AnyResource.id
                            ),
                        }
                        for child in self.combo_children
                    ],
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()

        result = serializer.data
        assert result['_resource_selection_required'] is True

        combo = result['subbookings'][0]['combo_children']
        assert combo[0]['_availability']['staffers'][self.staffer.id]['type'] == INFO
        assert combo[0]['_availability']['staffers'][AnyResource.id]['type'] == ERROR

    def test_combo_appointment_availability_free_from(self):
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(9, 0)),
                    'booked_till': self._dt_from_hour(time(10, 0)),
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()

        result = serializer.data
        combo = result['subbookings'][0]['combo_children']
        assert (
            combo[0]['_availability']['staffers'][self.staffer.id]['message']
            == 'Available at 10:00 AM'
        )

    def test_combo_in_multi_appointment(self):
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                },
                {
                    'service_variant_id': self.variant.id,
                    'booked_from': self._dt_from_hour(time(11)),
                    'booked_till': self._dt_from_hour(time(12)),
                },
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()
        result = serializer.data
        combo = result['subbookings'][0]['combo_children']
        assert combo[0]['_availability']['staffers'][self.staffer.id]['ok'] is True

        result['dry_run'] = False
        result['overbooking'] = False

        serializer = AppointmentSerializer(
            data=result,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        appointment = serializer.save()
        assert len(appointment.subbookings) == 2

        for subbooking in iter_leaf_services(appointment.subbookings):
            assert subbooking.resources.count() > 0, f'staffer: {subbooking.staffer}'
            assert subbooking.staffer == self.staffer
            assert subbooking.staffer_id == self.staffer.id

    def test_combo_parallel_in_multi_appointment(self):
        self.combo.service.combo_type = ComboType.PARALLEL
        self.combo.service.save()

        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.variant.id,
                    'booked_from': self._dt_from_hour(time(10)),
                    'booked_till': self._dt_from_hour(time(11)),
                },
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': None,
                    'booked_till': None,
                },
                {
                    'service_variant_id': self.variant.id,
                    'booked_from': None,
                    'booked_till': None,
                },
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        appointment = serializer.save()

        assert appointment.subbookings[1].booked_from == appointment.subbookings[0].booked_till
        assert appointment.subbookings[2].booked_from == appointment.subbookings[1].booked_till

    def test_combo_appointment_preferred_staffer(self):
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                    'combo_children': [
                        {
                            'service_variant_id': child.id,
                            'staffer_id': self.staffer.id,
                            'appliance_id': -1,
                        }
                        for child in self.combo_children
                    ],
                    'staffer_id': self.staffer.id,
                    'appliance_id': -1,
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        serializer.save()

        result = serializer.data
        result['dry_run'] = False
        result['overbooking'] = False

        serializer = AppointmentSerializer(
            instance=None,
            data=result,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        appt = serializer.save()
        assert appt.subbookings[0].staffer is None
        assert appt.subbookings[0].appliance is None

    def test_combo_with_service_variants_per_staffer(self):
        """
        In case staffers are set directly for combo,
        ensure `service_variant` won't be changed to no-variant mode.
        """
        self.combo.set_staffers([self.extra_staffer])

        serializer = AppointmentSerializer(
            instance=None,
            data=create_business_appointment_data(
                [
                    {
                        'service_variant_id': self.combo.id,
                        'booked_from': self._dt_from_hour(time(10, 0)),
                        'booked_till': self._dt_from_hour(time(11, 0)),
                        'staffer_id': self.staffer.id,
                    }
                ],
                dry_run=True,
            ),
            context=self._appointment_context,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()

        subbooking_data = serializer.data['subbookings'][0]
        self.assertEqual(subbooking_data['service_variant']['mode'], SVMode.VARIANT)
        self.assertEqual(subbooking_data['service_variant']['id'], self.combo.id)

    def test_combo_appointment_same_service_diff_price(self):
        service_variant = service_variant_recipe.make(
            service=service_recipe.make(business=self.business),
            duration=relativedelta(minutes=10),
        )
        combo_parent = service_variant_recipe.make(
            service=service_recipe.make(business=self.business, combo_type=ComboType.SEQUENCE),
            combo_pricing=ComboPricing.CUSTOM,
        )

        service_variant.service.add_staffers([self.staffer, self.extra_staffer])

        baker.make(
            ComboMembership,
            combo=combo_parent,
            child=service_variant,
            order=1,
            price=Decimal('50.00'),
            type=PriceType.FIXED,
        )
        baker.make(
            ComboMembership,
            combo=combo_parent,
            child=service_variant,
            order=2,
            price=None,
            type=PriceType.FREE,
        )

        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': combo_parent.id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                }
            ],
            dry_run=True,
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        assert serializer.is_valid(), serializer.errors
        appt = serializer.save()

        assert len(appt.subbookings) == 1
        subbooking = appt.subbookings[0]
        assert len(subbooking.combo_children) == 2
        self.assertEqual(
            appt.total,
            ServicePrice(
                Decimal('50.00'),
                PriceType.FIXED,
                should_display_price_types=PriceType.should_display().union({PriceType.VARIES}),
            ),
        )

    def test_appointment_set_is_highlighted__combo(self):
        data = create_business_appointment_data(
            [
                {
                    'service_variant_id': self.combo.id,
                    'booked_from': self._dt_from_hour(time(10, 0)),
                    'booked_till': self._dt_from_hour(time(11, 0)),
                    'staffer_id': self.staffer.id,
                }
            ],
            dry_run=False,
        )
        data['subbookings'][0]['is_highlighted'] = True

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self._appointment_context,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()
        self.assertTrue(
            all(
                child_data['is_highlighted']
                for child_data in serializer.data['subbookings'][0]['combo_children']
            )
        )
