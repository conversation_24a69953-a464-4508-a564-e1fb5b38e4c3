import datetime

import pytest
from django.test import TestCase
from django.utils import timezone
from freezegun import freeze_time
from model_bakery import baker
from pytz import UTC

from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import RepeatType, RepeatEndType
from webapps.booking.models import RepeatingBooking, SubBooking, BookingChange
from webapps.booking.tests.utils import create_appointment
from webapps.user.tools import get_system_user


@pytest.mark.freeze_time(datetime.datetime(2022, 9, 27, 14, 0, tzinfo=UTC))
class RepeatingAddBookingChangeTestCase(TestCase):
    """Test the updated dates are correctly applied on new BookingChange objects."""

    @classmethod
    def setUpTestData(cls):
        cls.repeating_created_at = datetime.datetime(2022, 3, 10, 10, 10, tzinfo=UTC)
        repeating_type = RepeatType.EVERY_MONTH
        with freeze_time(cls.repeating_created_at):
            cls.repeating = baker.make(
                RepeatingBooking,
                repeat=repeating_type,
                end_type=RepeatEndType.NEVER,
                repeat_number=None,
            )
            booked_from = datetime.datetime.now(datetime.timezone.utc)
            delta = RepeatingBooking.DELTAS[repeating_type]

            for _ in range(5):
                create_appointment(
                    [
                        dict(
                            booked_from=booked_from,
                            booked_till=booked_from + datetime.timedelta(minutes=30),
                        ),
                    ],
                    repeating=cls.repeating,
                )
                booked_from = booked_from + delta

    def test_bookingchange_for_appointment(self):
        booking = SubBooking.objects.last()

        appt = booking.appointment
        self.assertEqual(appt.updated, self.repeating_created_at)

        appt.save()  # trigger auto now

        self.assertGreater(appt.updated, self.repeating_created_at)

        BookingChange.add(
            appt,
            changed_by=BookingChange.BY_SYSTEM,
            changed_user=get_system_user(),
            metadata={
                'action': 'test update Appointment',
            },
        )

        for change in BookingChange.objects.all():
            self.assertEqual(change.created, appt.updated)

    def test_bookingchange_for_wrapper(self):
        booking = SubBooking.objects.last()

        appt = booking.appointment
        self.assertEqual(appt.updated, self.repeating_created_at)

        appt.save()  # trigger auto now

        self.assertGreater(appt.updated, self.repeating_created_at)

        wrapper = AppointmentWrapper([booking])
        BookingChange.add(
            wrapper,
            changed_by=BookingChange.BY_SYSTEM,
            changed_user=get_system_user(),
            metadata={
                'action': 'test update AppointmentWrapper',
            },
        )

        for change in BookingChange.objects.all():
            self.assertEqual(change.created, appt.updated)

    def test_bookingchange_for_subbooking(self):
        booking = SubBooking.objects.last()
        self.assertEqual(booking.updated, self.repeating_created_at)

        SubBooking.objects.filter(id=booking.id).update(resolved_price=10)  # trigger auto now
        booking.refresh_from_db()

        self.assertGreater(booking.updated, self.repeating_created_at)

        BookingChange.add(
            booking,
            changed_by=BookingChange.BY_SYSTEM,
            changed_user=get_system_user(),
            metadata={
                'action': 'test update SubBooking',
            },
        )
        for change in BookingChange.objects.all():
            self.assertEqual(change.created, booking.updated)


@pytest.mark.django_db
def test_bookingchange_custom_created_date():
    appointment = create_appointment()

    change_created_at = timezone.now() - datetime.timedelta(hours=5)

    BookingChange.add(
        appointment,
        changed_by=BookingChange.BY_SYSTEM,
        changed_user=get_system_user(),
        metadata={
            'action': 'test custom Created_date',
        },
        change_created_at=change_created_at,
    )
    assert BookingChange.objects.first().created == change_created_at


@pytest.mark.django_db
def test_bookingchange_fields():
    appointment = create_appointment()
    BookingChange.add(
        appointment,
        changed_by=BookingChange.BY_SYSTEM,
        changed_user=get_system_user(),
        metadata={
            'action': 'test bookingchange fields',
        },
    )
    bc = BookingChange.objects.first()
    assert bc.appointment_id == appointment.id
    assert bc.subbooking_id == appointment.bookings.first().id
    assert bc.business_id == appointment.business_id
