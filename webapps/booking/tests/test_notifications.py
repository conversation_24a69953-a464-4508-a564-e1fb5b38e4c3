import datetime
import unittest
from unittest.mock import patch

import pytest
import pytz
from django.test import override_settings
from django.utils import translation
from django.utils.crypto import get_random_string
from model_bakery import baker
from parameterized import parameterized

from lib.deeplink.deeplinks_ms.grpc.clients import ServerUnavailableDeeplinkGRPC
from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.experiment.booking import SendSMSInNoShowConfirmationExperiment
from lib.test_utils import create_subbooking
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.events import (
    appointment_no_show_by_business_event,
    appointment_status_changed_by_customer_event,
)
from webapps.booking.models import Appointment, BookingSources
from webapps.booking.notifications.business_ready import (
    BusinessReadyForAppointmentNotification,
    CustomerNoShowNotification,
    NoShowConfirmationNotification,
)
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import bci_recipe, service_recipe, service_variant_recipe
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.consts import ANDROID, WEB
from webapps.metrics.tests.baker_recipes import business_recipe
from webapps.notification.channels import Channel, EmailChannel, PushChannel, SMSChannel
from webapps.notification.enums import NotificationTarget
from webapps.notification.models import Reciever, UserNotification
from webapps.user.baker_recipes import customer_user
from webapps.user.models import User, UserProfile


class BusinessAppointmentBaseTestCase(unittest.TestCase):
    def setUp(self):
        super().setUp()
        owner_user = baker.make(User)
        business = baker.make(Business, owner=owner_user)
        self.bci_user: User = baker.make(User, cell_phone='600 600 600')
        baker.make(
            UserProfile, user=self.bci_user, profile_type=UserProfile.Type.CUSTOMER, language='pl'
        )
        self.bci_user.refresh_from_db()
        bci = baker.make(BusinessCustomerInfo, business=business, user=self.bci_user)

        booking, _, _ = create_subbooking(
            business=business,
            booking_kws=dict(
                booked_for=bci,
                booked_from=tznow(),
                booked_till=tznow() + datetime.timedelta(minutes=30),
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.ACCEPTED,
            ),
        )

        self.appointment = AppointmentWrapper.get_by_appointment_id(
            booking.appointment_id,
            business_id=business.id,
            prefetch_all=False,
        )


@pytest.mark.django_db
class BusinessAppointmentNotificationsTests(BusinessAppointmentBaseTestCase):
    def test_business_ready_for_appointment_notification(self):
        assert self.bci_user.get_language(UserProfile.Type.CUSTOMER) == 'pl'
        notification = BusinessReadyForAppointmentNotification(self.appointment)
        assert notification.context
        push_content = PushChannel(notification).get_content()
        assert push_content
        sms_content = SMSChannel(notification).get_content()
        assert sms_content

    def test_customer_no_show_notification(self):
        assert self.bci_user.get_language(UserProfile.Type.CUSTOMER) == 'pl'
        notification = CustomerNoShowNotification(self.appointment)
        assert notification.context

        email_content = EmailChannel(notification).get_content()
        assert email_content


@pytest.mark.django_db
class NoShowConfirmationNotificationTests(unittest.TestCase):
    MESSAGE_VARIANT_A = "What happened? Let us know why you couldn't make it to your appointment."
    MESSAGE_VARIANT_B = "You missed your appointment. Let us know what happened."
    TEST_DEEPLINK = "https://lhkw.test-app.link/CBU6J73vOLb"
    SMS_CHARACTER_LIMIT = 160

    def setUp(self):
        super().setUp()

        self.business = business_recipe.make(name='Short name', time_zone_name='Europe/Warsaw')
        user = customer_user.make(cell_phone='600 600 600')
        user_notification = baker.make(
            UserNotification,
            type=UserNotification.PUSH_NOTIFICATION,
            profile=user.customer_profile,
        )
        baker.make(
            Reciever,
            device=Reciever.ANDROID,
            identifier=get_random_string(160),
            customer_notifications=user_notification,
        )
        bci = bci_recipe.make(business=self.business, user=user)

        service_variant = service_variant_recipe.make(
            service=service_recipe.make(business=self.business),
        )

        booking_source = baker.make(
            BookingSources, name=ANDROID, app_type=BookingSources.CUSTOMER_APP
        )
        self.appointment = create_appointment(
            [
                {'service_variant': service_variant},
            ],
            status=Appointment.STATUS.NOSHOW,
            business=self.business,
            source=booking_source,
        )
        self.appointment.booked_for = bci
        self.appointment.booked_from = datetime.datetime(2020, 8, 11, 10, 30, tzinfo=pytz.UTC)
        self.appointment.booked_till = datetime.datetime(2020, 8, 11, 10, 45, tzinfo=pytz.UTC)
        self.appointment.save()

    def test_get_target(self):
        notification = NoShowConfirmationNotification(self.appointment)
        target = notification.get_target()
        self.assertEqual(NotificationTarget.NO_SHOW_CONFIRMATION.value, target.type)
        self.assertEqual(self.appointment.id, target.id)

    def test_content_variants(self):
        self._test_push_content(ExperimentVariants.VARIANT_A, self.MESSAGE_VARIANT_A)
        self._test_push_content(ExperimentVariants.VARIANT_B, self.MESSAGE_VARIANT_B)

        self._test_sms_content(
            ExperimentVariants.VARIANT_A,
            self._get_expected_sms_content(self.MESSAGE_VARIANT_A[:-1], self.business.name),
        )
        self._test_sms_content(
            ExperimentVariants.VARIANT_B,
            self._get_expected_sms_content(self.MESSAGE_VARIANT_B[:-1], self.business.name),
        )

    @translation.override(None)
    def _test_push_content(self, experiment_variant, expected_content):
        notification = NoShowConfirmationNotification(
            self.appointment, experiment_variant=experiment_variant
        )
        push_content = PushChannel(notification).get_content()
        self.assertEqual(expected_content, push_content)

    @translation.override(None)
    def _test_sms_content(self, experiment_variant, expected_content):
        notification = NoShowConfirmationNotification(
            self.appointment, experiment_variant=experiment_variant
        )
        notification.deeplink = self.TEST_DEEPLINK
        sms_content = notification.get_sms_content()
        self.assertEqual(expected_content, sms_content)

    def test_get_sms_content_short_business_name(self):
        notification = NoShowConfirmationNotification(
            self.appointment, experiment_variant=ExperimentVariants.VARIANT_A
        )
        notification.deeplink = self.TEST_DEEPLINK
        sms_content = notification.get_sms_content()

        expected_content = self._get_expected_sms_content(
            self.MESSAGE_VARIANT_A[:-1], self.business.name
        )
        self.assertEqual(expected_content, sms_content)
        self.assertLess(len(sms_content), self.SMS_CHARACTER_LIMIT)

    def test_get_sms_content_really_short_business_name(self):
        self.business.name = 'SS'
        self.business.save()

        notification = NoShowConfirmationNotification(
            self.appointment, experiment_variant=ExperimentVariants.VARIANT_A
        )
        notification.deeplink = self.TEST_DEEPLINK
        sms_content = notification.get_sms_content()

        expected_content = self._get_expected_sms_content(
            self.MESSAGE_VARIANT_A[:-1], self.business.name
        )
        self.assertEqual(expected_content, sms_content)
        self.assertLess(len(sms_content), self.SMS_CHARACTER_LIMIT)

    def test_get_sms_content_long_business_name(self):
        self.business.name = 'Really really long business name'
        self.business.save()

        notification = NoShowConfirmationNotification(
            self.appointment, experiment_variant=ExperimentVariants.VARIANT_A
        )
        additional_test_characters = "x" * 12
        notification.deeplink = f'{self.TEST_DEEPLINK}{additional_test_characters}'
        sms_content = notification.get_sms_content()

        expected_content = self._get_expected_sms_content(self.MESSAGE_VARIANT_A[:-1], 'Reall...')
        self.assertEqual(f'{expected_content}{additional_test_characters}', sms_content)
        self.assertEqual(self.SMS_CHARACTER_LIMIT, len(sms_content))

    def test_get_sms_content_skip_long_business_name(self):
        self.business.name = 'Really really long business name'
        self.business.save()

        notification = NoShowConfirmationNotification(
            self.appointment, experiment_variant=ExperimentVariants.VARIANT_A
        )
        additional_test_characters = "x" * 13
        notification.deeplink = f'{self.TEST_DEEPLINK}{additional_test_characters}'
        sms_content = notification.get_sms_content()

        expected_content = self._get_expected_sms_content(self.MESSAGE_VARIANT_A[:-1], '')
        self.assertEqual(f'{expected_content}{additional_test_characters}', sms_content)
        self.assertLess(len(sms_content), self.SMS_CHARACTER_LIMIT)

    def _get_expected_sms_content(self, message_variant, business_name):
        return (
            f'{message_variant}: August 11, 2020, 12:30 PM - {business_name} {self.TEST_DEEPLINK}'
        )

    def test_do_not_skip(self):
        notification = NoShowConfirmationNotification(self.appointment)
        self.assertEqual((False, None), notification.should_skip_with_plea())

    def test_skip_notification(self):
        self.appointment.status = Appointment.STATUS.FINISHED
        self.appointment.save()
        notification = NoShowConfirmationNotification(self.appointment)
        skip, _ = notification.should_skip_with_plea()
        self.assertTrue(skip)

    @override_eppo_feature_flag({SendSMSInNoShowConfirmationExperiment.flag_name: False})
    def test_send_sms_channel_disabled(self):
        notification = NoShowConfirmationNotification(self.appointment)
        channels = [channel(notification) for channel in notification.channels]
        channel_selector = notification.channel_selector(notification)

        channel_recipients = channel_selector.get_recipients_by_channel(
            channels, notification.maybe_remove_recipients(notification.resolved_recipients)
        )
        self.assertEqual(1, len(channel_recipients))
        self.assertSetEqual({Channel.Type.PUSH}, set(channel_recipients.keys()))

        with (
            patch('webapps.notification.channels.PushChannel.send') as mock_push_send,
            patch('webapps.notification.channels.SMSChannel.send') as mock_sms_send,
        ):
            notification.send()

        mock_push_send.assert_called_once()
        mock_sms_send.assert_not_called()

    @override_eppo_feature_flag({SendSMSInNoShowConfirmationExperiment.flag_name: True})
    def test_send_sms_channel_enabled(self):
        notification = NoShowConfirmationNotification(self.appointment)
        channels = [channel(notification) for channel in notification.channels]
        channel_selector = notification.channel_selector(notification)

        channel_recipients = channel_selector.get_recipients_by_channel(
            channels, notification.maybe_remove_recipients(notification.resolved_recipients)
        )
        self.assertEqual(2, len(channel_recipients))
        self.assertSetEqual({Channel.Type.PUSH, Channel.Type.SMS}, set(channel_recipients.keys()))

        with (
            patch('webapps.notification.channels.PushChannel.send') as mock_push_send,
            patch('webapps.notification.channels.SMSChannel.send') as mock_sms_send,
        ):
            notification.send()

        mock_push_send.assert_called_once()
        mock_sms_send.assert_called_once()

    @override_eppo_feature_flag({SendSMSInNoShowConfirmationExperiment.flag_name: True})
    def test_send_sms_channel_enabled_but_removed(self):
        booking_source = self.appointment.source
        booking_source.name = WEB
        booking_source.save()

        self.appointment.refresh_from_db()
        notification = NoShowConfirmationNotification(self.appointment)
        channels = [channel(notification) for channel in notification.channels]
        channel_selector = notification.channel_selector(notification)

        channel_recipients = channel_selector.get_recipients_by_channel(
            channels, notification.maybe_remove_recipients(notification.resolved_recipients)
        )
        self.assertEqual(1, len(channel_recipients))
        self.assertSetEqual({Channel.Type.PUSH}, set(channel_recipients.keys()))

        with (
            patch('webapps.notification.channels.PushChannel.send') as mock_push_send,
            patch('webapps.notification.channels.SMSChannel.send') as mock_sms_send,
        ):
            notification.send()

        mock_push_send.assert_called_once()
        mock_sms_send.assert_not_called()

    @override_eppo_feature_flag({SendSMSInNoShowConfirmationExperiment.flag_name: True})
    def test_send_sms_channel_enabled_but_grpc_error(self):
        notification = NoShowConfirmationNotification(self.appointment)
        channels = [channel(notification) for channel in notification.channels]
        channel_selector = notification.channel_selector(notification)

        with patch(
            'webapps.booking.notifications.business_ready.get_or_generate_deeplink',
            side_effect=ServerUnavailableDeeplinkGRPC('error'),
        ):
            channel_recipients = channel_selector.get_recipients_by_channel(
                channels, notification.maybe_remove_recipients(notification.resolved_recipients)
            )
        self.assertEqual(1, len(channel_recipients))
        self.assertSetEqual({Channel.Type.PUSH}, set(channel_recipients.keys()))

        with (
            patch(
                'webapps.booking.notifications.business_ready.get_or_generate_deeplink',
                side_effect=ServerUnavailableDeeplinkGRPC('error'),
            ),
            patch('webapps.notification.channels.PushChannel.send') as mock_push_send,
            patch('webapps.notification.channels.SMSChannel.send') as mock_sms_send,
        ):
            notification.send()

        mock_push_send.assert_called_once()
        mock_sms_send.assert_not_called()


@pytest.mark.django_db
class BusinessNotificationsActionsTests(BusinessAppointmentBaseTestCase):
    @parameterized.expand(
        [
            (True, True, 1),
            (True, False, 0),
            (False, True, 0),
            (False, False, 0),
        ]
    )
    @patch('webapps.business.notifications.prepayments.ManualNoShowSuggestion.send')
    def test_appointment_no_show_by_business_event(
        self,
        pay_by_app,
        prepayments,
        expected_call_count,
        mock,
    ):
        with override_settings(
            POS__PAY_BY_APP=pay_by_app,
            POS__PREPAYMENTS=prepayments,
        ):
            appointment_no_show_by_business_event.send(self.appointment)

            assert mock.call_count == expected_call_count

    @parameterized.expand(
        [
            (True, True, 1),
            (True, False, 0),
            (False, True, 0),
            (False, False, 0),
        ]
    )
    @patch(
        'webapps.booking.notifications.appointment_changed.AppointmentLateCancelNotification.send'
    )
    @patch('webapps.business.notifications.prepayments.LateCancellationSuggestion.schedule')
    def test_appointment_status_changed_by_customer_event__canceled(
        self,
        pay_by_app,
        prepayments,
        late_cancellation_expected_call_count,
        late_cancellation_mock,
        appointment_late_cancel_mock,
    ):
        with override_settings(
            POS__PAY_BY_APP=pay_by_app,
            POS__PREPAYMENTS=prepayments,
        ):
            appointment_status_changed_by_customer_event.send(
                self.appointment,
                status=Appointment.STATUS.CANCELED,
                previous_status=Appointment.STATUS.UNCONFIRMED,
            )

            assert appointment_late_cancel_mock.call_count == 1
            assert late_cancellation_mock.call_count == late_cancellation_expected_call_count

    @parameterized.expand(
        [
            (True, True, 1),
            (True, False, 0),
            (False, True, 0),
            (False, False, 0),
        ]
    )
    @patch('webapps.booking.notifications.appointment_changed.AppointmentNoShowNotification.send')
    @patch('webapps.business.notifications.prepayments.ClientNoShowSuggestion.schedule')
    def test_appointment_status_changed_by_customer_event__no_show(
        self,
        pay_by_app,
        prepayments,
        client_no_show_expected_call_count,
        client_no_show_mock,
        appointment_no_show_mock,
    ):
        with override_settings(
            POS__PAY_BY_APP=pay_by_app,
            POS__PREPAYMENTS=prepayments,
        ):
            appointment_status_changed_by_customer_event.send(
                self.appointment,
                status=Appointment.STATUS.NOSHOW,
                previous_status=Appointment.STATUS.UNCONFIRMED,
            )

            assert appointment_no_show_mock.call_count == 1
            assert client_no_show_mock.call_count == client_no_show_expected_call_count
