from datetime import timedelta

from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _

from lib.booksy_sms import get_reasonable_send_datetime
from webapps.business.cache import get_cached_invite_deeplink
from webapps.notification.base import BaseNotification, PushTarget, SchedulePlanner
from webapps.notification.channels import SMSChannel, PushChannel, EmailChannel
from webapps.notification.base import Channel
from webapps.notification.enums import NotificationCategory
from webapps.notification.recipients import (
    BusinessSender,
    NotificationCustomerUser,
)
from webapps.user.models import User


class CustomerInviteSchedulePlanner(SchedulePlanner):
    def __init__(self, notification):
        super().__init__(notification)
        if not hasattr(notification, 'business'):
            raise RuntimeError('Business is missing')  # pylint: disable=broad-exception-raised
        self.business = notification.business

    def get_time(self, days_delay: int = None):  # pylint: disable=arguments-differ
        tz_file = self.business.get_timezone()
        send_time = get_reasonable_send_datetime(
            timezone=tz_file._long_name  # pylint: disable=protected-access
        )
        if days_delay:
            send_time = send_time + timedelta(days=days_delay)
        return send_time


class CustomerInviteProcessMixin:
    sender = BusinessSender
    category = NotificationCategory.INVITATION
    recipients = (NotificationCustomerUser,)
    identity_invitation_type: str = NotImplementedError
    schedule_planner = CustomerInviteSchedulePlanner
    customer_card = None

    def __init__(self, business, **parameters):
        super().__init__(business, **parameters)
        self.business = business
        self.user_id = parameters['user_id']
        self.staffer_invite = parameters['staffer_invite']
        self.staffer_id = parameters['staffer_id']

    @property
    def identity_suffix(self) -> str:
        return f'user_id={self.customer.id}:business_id={self.business.id}:invite_process'

    @property
    def identity_prefix(self):
        if self.staffer_invite and self.staffer_id:
            return (
                f'invitation:staffer_{self.identity_invitation_type}:staffer_id={self.staffer_id}'
            )
        return f'invitation:invitation_{self.identity_invitation_type}'

    @property
    def identity(self):
        return f'{self.notif_type},{self.identity_prefix}:{self.identity_suffix}'

    @cached_property
    def customer(self):
        return User.objects.get(pk=self.user_id)


class CustomerPushInviteNotificationBase(CustomerInviteProcessMixin, BaseNotification):
    channels = (PushChannel,)
    identity_invitation_type = NotImplementedError
    push_template = _('{business_name_short} is waiting for you! Book online now with Booksy.')

    def get_context(self):
        return {
            'business_name_short': self.business.short_name,
            'branchio_url': self.business.get_push_invite_mp_deeplink(
                staffer_invite=self.staffer_invite
            ),
        }

    def get_target(self) -> PushTarget:
        return PushTarget(type='show_business', id=self.business.id)

    def add_following_notification_schedule_id(self, notification_schedule_id):
        if self.parameters.get('following_notification_schedule_ids') is None:
            self.parameters['following_notification_schedule_ids'] = []
        self.parameters['following_notification_schedule_ids'].append(notification_schedule_id)

    def has_any_push_recipients(self) -> bool:
        channel = PushChannel(self)
        channel_selector = self.channel_selector(self)
        channel_recipients = channel_selector.get_recipients_by_channel(
            [channel], self.maybe_remove_recipients(self.resolved_recipients)
        )

        return bool(channel_recipients[channel.type])


class CustomerPushInviteNotification(CustomerPushInviteNotificationBase):
    identity_invitation_type = 'push'


class CustomerRepeatedPushInviteNotification(CustomerPushInviteNotificationBase):
    identity_invitation_type = 'repeated_push'


class EmailInviteNotification(CustomerInviteProcessMixin, BaseNotification):
    channels = (EmailChannel,)
    email_template_name = 'invitation/customer_invitation_email'
    identity_invitation_type = 'email'

    def get_context(self):
        return {
            'business': self.business,
            'customer_name': self.parameters['customer_name'],
            'staffer_invite': self.staffer_invite,
        }


class SmsInviteNotification(CustomerInviteProcessMixin, BaseNotification):
    channels = (SMSChannel,)
    sms_template = _(
        'You can now book your appointments with {business_name_short} 24/7 '
        'using Booksy: {branchio_url}\nSee you soon!'
    )
    identity_invitation_type = 'sms'

    def get_sms_content(self):
        return self.sms_template.format(**self.get_context())

    def get_context(self):
        branchio_url = get_cached_invite_deeplink(
            business_id=self.business.id,
            channel=Channel.Type.SMS,
            staffer_invite=self.staffer_invite,
        )

        return {
            'business_name_short': self.business.short_name,
            'branchio_url': branchio_url,
        }
