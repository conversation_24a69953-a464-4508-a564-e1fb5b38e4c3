import elasticsearch_dsl as dsl

from lib.searchables.searchables import (
    FunctionScore,
    Searchable,
    V,
)
from webapps.business.searchables.business import BusinessInsertSingleImages
from webapps.business.searchables.business.search_engine import VisibleBusinessSearchable
from webapps.business.searchables.business.sub_searchables import GenderBasedBusinessSearchable

MIN_INSPIRATION_PHOTOS = 4


# pylint: disable=use-dict-literal
class BusinessInsertOnlyCoverImage(Searchable):
    class Meta:
        bool_param = 'must'

    insert_images = dsl.query.Bool(
        must=(
            dsl.query.HasChild(
                type='image',
                query=dsl.query.Term(is_cover_photo=True),
                inner_hits=dict(
                    name='images.cover',
                    size=1,
                    _source=dict(excludes=['join', 'location']),
                    sort=dict(order='asc', created='desc'),
                ),
            ),
        )
    )


class BusinessInsertCoverAndInspirationsImages(Searchable):
    insert_images = dsl.query.Bool(
        must=[
            dsl.query.HasChild(
                type='image',
                query=dsl.query.Term(is_cover_photo=True),
                inner_hits=dict(
                    name='images.cover',
                    size=1,
                    _source=dict(excludes=['join', 'location']),
                    sort=dict(order='asc', created='desc'),
                ),
            ),
            dsl.query.HasChild(
                type='image',
                query=dsl.query.Term(category='inspiration'),
                inner_hits=dict(
                    name='images.inspiration',
                    size=MIN_INSPIRATION_PHOTOS,
                    _source=dict(excludes=['join', 'location']),
                    sort=dict(order='asc', created='desc'),
                ),
                min_children=MIN_INSPIRATION_PHOTOS,
            ),
        ]
    )


class SelectedForYouSimpleSearchable(Searchable):
    insert_single_images = BusinessInsertOnlyCoverImage()
    business_categories = dsl.query.Terms(business_categories__id=V('business_categories'))
    visible = VisibleBusinessSearchable()
    min_treatments = dsl.query.Range(
        treatment_count=dict(gte=V('min_treatment_count')),
    )
    gender_range = GenderBasedBusinessSearchable()
    location_filter = dsl.query.GeoDistance(
        distance=V('get_location_max_km_distance'),
        business_location__coordinate=V('location_geo'),
    )
    active = dsl.query.Range(active_from=dict(gte=V('get_max_active_days')))
    scoring = FunctionScore(
        functions=[
            dsl.function.Exp(
                active_from={
                    'origin': 'now',
                    'scale': '14d',
                    'offset': '14d',
                    'decay': 0.5,
                },
                weight=3,
            ),
            dsl.function.Gauss(
                business_location__coordinate={
                    'origin': V('location_geo'),
                    'scale': '10km',
                    'decay': 0.5,
                },
            ),
        ],
        score_mode='multiply',
    )

    @staticmethod
    def get_location_max_km_distance(data):
        max_km_distance = data.get('max_km_distance', 50)
        return f'{max_km_distance}km'

    @staticmethod
    def get_max_active_days(data):
        max_active_days = data.get('max_active_days', 300)
        return f'now-{max_active_days}d'


class SelectedForYouSearchable(Searchable):
    insert_single_images = BusinessInsertSingleImages()
    business_categories = dsl.query.Terms(business_categories__id=V('business_categories'))
    visible = VisibleBusinessSearchable()
    min_treatments = dsl.query.Range(
        treatment_count=dict(gte=V('min_treatment_count')),
    )
    cover_photo = dsl.query.Bool(
        must=dsl.query.HasChild(
            type='image',
            query=dsl.query.Term(is_cover_photo=True),
        )
    )
    gender_range = GenderBasedBusinessSearchable()
    location_filter = dsl.query.GeoDistance(
        distance=V('get_location_max_km_distance'), business_location__coordinate=V('location_geo')
    )
    active = dsl.query.Range(active_from=dict(gte=V('get_max_active_days')))
    scoring = FunctionScore(
        functions=[
            dsl.function.Exp(
                active_from={
                    'origin': 'now',
                    'scale': '14d',
                    'offset': '14d',
                    'decay': 0.5,
                },
                weight=3,
            ),
            dsl.function.Gauss(
                business_location__coordinate={
                    'origin': V('location_geo'),
                    'scale': '10km',
                    'decay': 0.5,
                },
            ),
        ],
        score_mode='multiply',
    )

    @staticmethod
    def get_location_max_km_distance(data):
        max_km_distance = data.get('max_km_distance', 50)
        return f'{max_km_distance}km'

    @staticmethod
    def get_max_active_days(data):
        max_active_days = data.get('max_active_days', 300)
        return f'now-{max_active_days}d'


# remove this class with ff and move this attrs to SelectedForYouSearchable
class SelectedForYouSearchableV2(Searchable):
    old = SelectedForYouSearchable()
    excluded = dsl.query.Bool(must_not=dsl.query.Terms(id=V('excluded', default=[])))


# remove this class with ff and move this attrs to SelectedForYouSearchable
# this name is intentional to know which class is final
class SelectedForYouSearchableV3(Searchable):
    old = SelectedForYouSearchable()
    excluded = dsl.query.Bool(must_not=dsl.query.Terms(id=V('excluded', default=[])))
    boost = dsl.query.Term(promoted=V('is_boost'))


class SelectedForYourPortfolioImagesExperimentSearchable(Searchable):
    insert_single_images = BusinessInsertCoverAndInspirationsImages()

    business_categories = dsl.query.Terms(business_categories__id=V('business_categories'))
    visible = VisibleBusinessSearchable()
    min_treatments = dsl.query.Range(
        treatment_count=dict(gte=V('min_treatment_count')),
    )
    gender_range = GenderBasedBusinessSearchable()
    location_filter = dsl.query.GeoDistance(
        distance=V('get_location_max_km_distance'),
        business_location__coordinate=V('location_geo'),
    )
    active = dsl.query.Range(active_from=dict(gte=V('get_max_active_days')))
    scoring = FunctionScore(
        functions=[
            dsl.function.Exp(
                active_from={
                    'origin': 'now',
                    'scale': '14d',
                    'offset': '14d',
                    'decay': 0.5,
                },
                weight=3,
            ),
            dsl.function.Gauss(
                business_location__coordinate={
                    'origin': V('location_geo'),
                    'scale': '10km',
                    'decay': 0.5,
                },
            ),
        ],
        score_mode='multiply',
    )

    @staticmethod
    def get_location_max_km_distance(data):
        max_km_distance = data.get('max_km_distance', 50)
        return f'{max_km_distance}km'

    @staticmethod
    def get_max_active_days(data):
        max_active_days = data.get('max_active_days', 300)
        return f'now-{max_active_days}d'
