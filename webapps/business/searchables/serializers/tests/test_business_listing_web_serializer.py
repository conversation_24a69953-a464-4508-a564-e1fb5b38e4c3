import copy
from unittest.mock import Mock, patch

from lib.feature_flag.feature.business import LimitServicesBusinessListingWebFlag
from lib.tests.utils import override_eppo_feature_flag
from webapps.business.searchables.serializers.business import (
    BusinessListingWebHitSerializer,
    ServiceCategoryHitSerializer,
)

# Test data fixtures
BUSINESS_DATA_FIXTURES = [
    {
        'id': 1,
        'name': 'Business with 3 services in first category',
        'service_categories': [
            {
                'id': 1,
                'name': 'Hair Services',
                'order': 1,
                'show_first': True,
                'services': [
                    {'id': 1, 'name': 'Haircut'},
                    {'id': 2, 'name': 'Hair Wash'},
                    {'id': 3, 'name': 'Hair Color'},
                ],
            },
            {
                'id': 2,
                'name': 'Nail Services',
                'order': 2,
                'show_first': False,
                'services': [
                    {'id': 4, 'name': 'Manicure'},
                    {'id': 5, 'name': 'Pedicure'},
                ],
            },
            {
                'id': 3,
                'name': 'Facial Services',
                'order': 3,
                'show_first': False,
                'services': [
                    {'id': 6, 'name': 'Basic Facial'},
                ],
            },
        ],
    },
    {
        'id': 2,
        'name': 'Business with services across multiple categories',
        'service_categories': [
            {
                'id': 1,
                'name': 'Hair Services',
                'order': 1,
                'show_first': True,
                'services': [
                    {'id': 1, 'name': 'Haircut'},
                    {'id': 2, 'name': 'Hair Wash'},
                ],
            },
            {
                'id': 2,
                'name': 'Nail Services',
                'order': 2,
                'show_first': False,
                'services': [
                    {'id': 3, 'name': 'Manicure'},
                    {'id': 4, 'name': 'Pedicure'},
                ],
            },
            {
                'id': 3,
                'name': 'Facial Services',
                'order': 3,
                'show_first': False,
                'services': [
                    {'id': 5, 'name': 'Basic Facial'},
                ],
            },
        ],
    },
    {
        'id': 3,
        'name': 'Business with fewer than 3 services total',
        'service_categories': [
            {
                'id': 1,
                'name': 'Hair Services',
                'order': 1,
                'show_first': True,
                'services': [
                    {'id': 1, 'name': 'Haircut'},
                ],
            },
            {
                'id': 2,
                'name': 'Nail Services',
                'order': 2,
                'show_first': False,
                'services': [
                    {'id': 2, 'name': 'Manicure'},
                ],
            },
        ],
    },
    {'id': 4, 'name': 'Business with empty categories', 'service_categories': []},
    {
        'id': 5,
        'name': 'Business with no service_categories field',
    },
    {
        'id': 6,
        'name': 'Business with categories but no services',
        'service_categories': [
            {'id': 1, 'name': 'Hair Services', 'order': 1, 'show_first': True, 'services': []},
            {'id': 2, 'name': 'Nail Services', 'order': 2, 'show_first': False, 'services': []},
        ],
    },
]


def create_mock_instance(business_data):
    """Helper function to create a mock instance with the given business data."""
    mock_instance = Mock()
    mock_instance.to_dict.return_value = business_data
    return mock_instance


def get_serialized_result(business_data):
    """Helper function to get serialized result for given business data."""
    mock_instance = create_mock_instance(business_data)
    serializer = BusinessListingWebHitSerializer()

    with patch.object(
        BusinessListingWebHitSerializer.__bases__[0],
        'to_representation',
        return_value=business_data,
    ):
        with patch.object(serializer, 'update_promoted_status'):
            with patch.object(serializer, 'clean_fields'):
                return serializer.to_representation(mock_instance)


def assert_service_categories_structure(result, expected_categories):
    """
    Helper function to assert service categories structure.

    Args:
        result: The serialized result
        expected_categories: List of dicts with category_id, category_name, service_ids
    """
    categories = result['service_categories']
    assert len(categories) == len(expected_categories)

    for i, expected in enumerate(expected_categories):
        category = categories[i]
        assert category['id'] == expected['category_id']
        assert category['name'] == expected['category_name']
        assert len(category['services']) == len(expected['service_ids'])

        for j, expected_service_id in enumerate(expected['service_ids']):
            assert category['services'][j]['id'] == expected_service_id


def assert_total_services_count(result, expected_count):
    """Helper to assert total services count across all categories."""
    total_services = sum(len(cat['services']) for cat in result['service_categories'])
    assert total_services == expected_count


class TestBusinessListingWebHitSerializer:
    """Test that BusinessListingWebHitSerializer serializes business data correctly."""

    def test_service_categories_field_uses_regular_serializer(self):
        """Test that service_categories field uses regular ServiceCategoryHitSerializer."""
        serializer = BusinessListingWebHitSerializer()
        service_categories_field = serializer.fields['service_categories']

        assert isinstance(service_categories_field, type(ServiceCategoryHitSerializer(many=True)))
        assert service_categories_field.many is True

    @override_eppo_feature_flag({LimitServicesBusinessListingWebFlag.flag_name: True})
    def test_flag_enabled_limits_services_within_single_category(self):
        """Test limiting services when all services are in one category."""
        business_data = copy.deepcopy(BUSINESS_DATA_FIXTURES[0])
        result = get_serialized_result(business_data)

        # Should return only the first category with 3 services
        expected_categories = [
            {'category_id': 1, 'category_name': 'Hair Services', 'service_ids': [1, 2, 3]}
        ]
        assert_service_categories_structure(result, expected_categories)
        assert_total_services_count(result, 3)

    @override_eppo_feature_flag({LimitServicesBusinessListingWebFlag.flag_name: True})
    def test_flag_enabled_limits_services_across_categories(self):
        """Test limiting services when they span multiple categories."""
        business_data = copy.deepcopy(BUSINESS_DATA_FIXTURES[1])
        result = get_serialized_result(business_data)

        # Should return 2 categories with first 3 services total
        expected_categories = [
            {'category_id': 1, 'category_name': 'Hair Services', 'service_ids': [1, 2]},
            {'category_id': 2, 'category_name': 'Nail Services', 'service_ids': [3]},
        ]
        assert_service_categories_structure(result, expected_categories)
        assert_total_services_count(result, 3)

    @override_eppo_feature_flag({LimitServicesBusinessListingWebFlag.flag_name: True})
    def test_flag_enabled_handles_fewer_than_limit_services(self):
        """Test handling when business has fewer than limit services."""
        business_data = copy.deepcopy(BUSINESS_DATA_FIXTURES[2])
        result = get_serialized_result(business_data)

        # Should return all categories since we have fewer than 3 services total
        expected_categories = [
            {'category_id': 1, 'category_name': 'Hair Services', 'service_ids': [1]},
            {'category_id': 2, 'category_name': 'Nail Services', 'service_ids': [2]},
        ]
        assert_service_categories_structure(result, expected_categories)
        assert_total_services_count(result, 2)

    @override_eppo_feature_flag({LimitServicesBusinessListingWebFlag.flag_name: True})
    def test_flag_enabled_handles_edge_cases(self):
        """Test edge cases with feature flag enabled."""
        # Empty categories
        empty_categories_data = copy.deepcopy(BUSINESS_DATA_FIXTURES[3])
        result = get_serialized_result(empty_categories_data)
        assert result['service_categories'] == []

        # Missing service_categories field
        missing_categories_data = copy.deepcopy(BUSINESS_DATA_FIXTURES[4])
        result = get_serialized_result(missing_categories_data)
        assert 'service_categories' not in result

        # Categories with no services
        no_services_data = copy.deepcopy(BUSINESS_DATA_FIXTURES[5])
        result = get_serialized_result(no_services_data)
        assert result['service_categories'] == []

    @override_eppo_feature_flag({LimitServicesBusinessListingWebFlag.flag_name: False})
    def test_flag_disabled_returns_all_services(self):
        """Test that all services are returned when feature flag is disabled."""
        business_data = copy.deepcopy(BUSINESS_DATA_FIXTURES[0])
        result = get_serialized_result(business_data)

        # Should return all original service categories unchanged
        expected_categories = [
            {'category_id': 1, 'category_name': 'Hair Services', 'service_ids': [1, 2, 3]},
            {'category_id': 2, 'category_name': 'Nail Services', 'service_ids': [4, 5]},
            {'category_id': 3, 'category_name': 'Facial Services', 'service_ids': [6]},
        ]
        assert_service_categories_structure(result, expected_categories)
        assert_total_services_count(result, 6)

    @override_eppo_feature_flag({LimitServicesBusinessListingWebFlag.flag_name: False})
    def test_flag_disabled_preserves_multi_category_structure(self):
        """Test that multi-category structure is preserved when flag is disabled."""
        business_data = copy.deepcopy(BUSINESS_DATA_FIXTURES[1])
        result = get_serialized_result(business_data)

        expected_categories = [
            {'category_id': 1, 'category_name': 'Hair Services', 'service_ids': [1, 2]},
            {'category_id': 2, 'category_name': 'Nail Services', 'service_ids': [3, 4]},
            {'category_id': 3, 'category_name': 'Facial Services', 'service_ids': [5]},
        ]
        assert_service_categories_structure(result, expected_categories)
        assert_total_services_count(result, 5)
