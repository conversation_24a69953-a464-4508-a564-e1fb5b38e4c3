import datetime

import pytest
from django.test import TestCase
from google.protobuf.json_format import MessageT<PERSON><PERSON>son

from mock import patch

from lib.feature_flag.feature import LoyaltyProgramFlag
from lib.tests.utils import override_feature_flag
from lib.tools import UTC

from webapps.business.baker_recipes import bci_recipe, bci_no_user_recipe, bci_with_profile_photo
from webapps.business.messages.bci import BasicCustomerDataChangedMessage
from webapps.images.baker_recipes import photo_recipe


@override_feature_flag({LoyaltyProgramFlag.flag_name: True})
@pytest.mark.django_db
@pytest.mark.parametrize(
    'attribute, value',
    [
        pytest.param('first_name', 'Ada', id='first_name'),
        pytest.param('last_name', 'Lovelace', id='last_name'),
        pytest.param('birthday', '1815-12-10', id='birthday'),
        pytest.param('visible_in_business', False, id='visible_in_business'),
        pytest.param('email', '<EMAIL>', id='email'),
        pytest.param('cell_phone', '****** 111 111', id='cell_phone'),
        pytest.param('deleted', '1852-11-27T00:00:00Z', id='deleted'),
    ],
)
@patch('webapps.business.messages.bci.BasicCustomerDataChangedMessage')
def test_business_customer_info_publisher_called(message_mock, attribute, value):
    bci = bci_recipe.make()
    setattr(bci, attribute, value)
    bci.save()
    message_mock.assert_called_with(bci)


@override_feature_flag({LoyaltyProgramFlag.flag_name: True})
@pytest.mark.django_db
@patch('webapps.business.messages.bci.BasicCustomerDataChangedMessage')
def test_business_customer_info_publisher_called_after_photo(message_mock):
    bci = bci_recipe.make()
    bci.photo = photo_recipe.make()
    bci.save()
    message_mock.assert_called_with(bci)


@override_feature_flag({LoyaltyProgramFlag.flag_name: True})
@pytest.mark.django_db
@patch('webapps.business.messages.bci.BasicCustomerDataChangedMessage')
def test_business_customer_info_publisher_called_after_user_update(message_mock):
    bci = bci_recipe.make()
    bci.user.first_name = 'Ada'
    bci.user.save()
    bci.refresh_from_db()
    message_mock.assert_called_with(bci)


class TestBasicCustomerDataChangedMessage(TestCase):
    @pytest.mark.freeze_time(datetime.datetime(2022, 5, 1))
    def test_message_no_user(self):
        bci = bci_no_user_recipe.make()
        self.assertJSONEqual(
            MessageToJson(BasicCustomerDataChangedMessage(bci).message),
            {
                'bciId': bci.id,
                'visibleInBusiness': True,
                'created': str(int(bci.created.timestamp())),
                'email': bci.email,
                'cellPhone': bci.cell_phone,
                'businessId': bci.business_id,
            },
        )

    @pytest.mark.freeze_time(datetime.datetime(2022, 5, 1))
    def test_message_user_data_override_by_bci(self):
        bci = bci_recipe.make(
            birthday=datetime.date(year=1791, month=12, day=26),
            photo=photo_recipe.make(),
            first_name="Charles",
            last_name="Babbage",
            email='<EMAIL>',
            cell_phone='****** 222 222',
        )
        self.assertJSONEqual(
            MessageToJson(BasicCustomerDataChangedMessage(bci).message),
            {
                'bciId': bci.id,
                'birthday': '1791-12-26',
                'firstName': 'Charles',
                'lastName': 'Babbage',
                'visibleInBusiness': True,
                'photoUrl': bci.photo.url,
                'created': str(int(bci.created.timestamp())),
                'email': '<EMAIL>',
                'cellPhone': '****** 222 222',
                'userId': bci.user_id,
                'businessId': bci.business_id,
            },
        )

    @pytest.mark.freeze_time(datetime.datetime(2022, 5, 1))
    def test_message_user_data(self):
        bci = bci_with_profile_photo.make()
        self.assertJSONEqual(
            MessageToJson(BasicCustomerDataChangedMessage(bci).message),
            {
                'bciId': bci.id,
                'firstName': 'TestFirstName',
                'lastName': 'TestLastName',
                'visibleInBusiness': True,
                'photoUrl': bci.user.customer_profile.photo.url,
                'created': str(int(bci.created.timestamp())),
                'email': bci.email,
                'cellPhone': bci.cell_phone,
                'userId': bci.user_id,
                'businessId': bci.business_id,
            },
        )

    @pytest.mark.freeze_time(datetime.datetime(2022, 5, 1, tzinfo=UTC))
    def test_message_after_soft_delete(self):
        bci = bci_no_user_recipe.make()
        bci.soft_delete()
        self.assertJSONEqual(
            MessageToJson(BasicCustomerDataChangedMessage(bci).message),
            {
                'bciId': bci.id,
                'visibleInBusiness': True,
                'created': str(int(bci.created.timestamp())),
                'email': bci.email,
                'cellPhone': bci.cell_phone,
                'deleted': str(int(bci.deleted.timestamp())),
                'businessId': bci.business_id,
            },
        )
