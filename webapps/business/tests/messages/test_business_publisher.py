from django.test import TestCase
from google.protobuf.json_format import Message<PERSON><PERSON><PERSON><PERSON>
from mock import patch
import pytest

from lib.feature_flag.feature import LoyaltyProgramFlag
from lib.tests.utils import override_feature_flag

from webapps.business.baker_recipes import business_recipe, region_recipe
from webapps.business.messages.business import BusinessCardChangedMessage


@override_feature_flag({LoyaltyProgramFlag.flag_name: True})
@pytest.mark.django_db
@pytest.mark.parametrize(
    'attribute, value',
    [
        pytest.param('name', 'Royal Astronomical Society', id='name'),
        pytest.param('region', None, id='region'),
        pytest.param('address', 'Piccadilly', id='address'),
        pytest.param('address2', 'W1J 0BQ', id='address2'),
        pytest.param('city', 'London', id='city'),
        pytest.param('time_zone_name', 'Europe/Paris', id='time_zone_name'),
    ],
)
@override_feature_flag({LoyaltyProgramFlag.flag_name: True})
@patch('django.db.transaction.on_commit')
def test_business_publisher_called(on_commit_mock, attribute, value):
    business = business_recipe.make(region=region_recipe.make())
    setattr(business, attribute, value)
    business.save()
    assert on_commit_mock.call_args[0][0].__name__ == "publish"  # pylint: disable=protected-access


class TestBusinessCardChangeMessage(TestCase):
    def test_message(self):
        business = business_recipe.make(time_zone_name='America/Chicago')
        business.city = 'Chicago'
        business.save()
        self.assertJSONEqual(
            MessageToJson(BusinessCardChangedMessage(business).message),
            {
                'id': business.id,
                'name': business.name,
                'locationName': business.get_location_name(with_city=True),
                'timeZoneName': 'America/Chicago',
            },
        )
