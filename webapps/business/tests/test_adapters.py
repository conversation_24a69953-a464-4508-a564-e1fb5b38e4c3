from decimal import Decimal

import pytest
from django.db import transaction
from django.http import Http404

from webapps.business.adapters import (
    AccessControlAdapter,
    CancellationReasonAdapter,
    ServiceVariantAdapter,
)
from webapps.business.baker_recipes import (
    appliance_recipe,
    business_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
)
from webapps.business.enums import (
    AutoCancellationReasonType,
    CancellationReasonType,
    CancellationType,
    StaffAccessLevels,
)
from webapps.business.models import (
    Business,
    CancellationReason,
    ServiceVariant,
    Resource,
)
from webapps.business.ports.cancellation_reason import CancellationReasonNotFound, ReasonWhy
from webapps.business.ports.service_variant import ServiceVariant as ServiceVariantData
from webapps.user.models import User
from webapps.user.baker_recipes import business_user as business_user_recipe

# pylint: disable=redefined-outer-name


@pytest.fixture
def business() -> Business:
    return business_recipe.make(owner__email="<EMAIL>")


@pytest.mark.django_db
class TestCancellationReasonAdapterGetReasonWhy:
    system_reason = CancellationReasonType.FAKE
    business_reason = AutoCancellationReasonType.DIFFICULT

    @pytest.fixture
    @transaction.atomic
    def cancellation_reason(self, business: Business) -> CancellationReason:
        reason = CancellationReason.objects.create(
            business=business,
            cancellation_reason=self.system_reason,
            business_cancellation_reason=self.business_reason,
        )
        return reason

    @pytest.fixture
    @staticmethod
    def sut() -> CancellationReasonAdapter:
        return CancellationReasonAdapter()

    @pytest.mark.parametrize(
        "cancellation_type,expected_why",
        [
            pytest.param(CancellationType.AUTOMATIC, system_reason, id="system"),
            pytest.param(CancellationType.MADE_BY_BUSINESS, business_reason, id="business"),
        ],
    )
    def test_gets_reason_why_for_churn(
        self,
        business: Business,
        cancellation_reason: CancellationReason,
        cancellation_type: CancellationType,
        expected_why: AutoCancellationReasonType | CancellationReasonType,
        sut: CancellationReasonAdapter,
    ):
        cancellation_reason.churn_type = cancellation_type
        cancellation_reason.save()

        reason_why = sut.get_reason_why(cancellation_reason.id)

        assert reason_why == ReasonWhy(
            business=ReasonWhy.Business(
                id=business.id,
                owner_email=business.owner.email,
            ),
            cancellation_type=cancellation_type,
            cancellation_why=expected_why,
        )

    def test_when_reason_not_exists_then_raises(
        self,
        sut: CancellationReasonAdapter,
        any_reason_id: int = 123,
        message: str = "123",
    ):
        with pytest.raises(CancellationReasonNotFound, match=message):
            sut.get_reason_why(any_reason_id)

    @pytest.mark.parametrize(
        "cancellation_type",
        [
            pytest.param(CancellationType.AUTOMATIC, id="system"),
            pytest.param(CancellationType.MADE_BY_BUSINESS, id="business"),
        ],
    )
    def test_when_reason_has_no_why_then_why_is_none(
        self,
        cancellation_reason: CancellationReason,
        cancellation_type: CancellationType,
        sut: CancellationReasonAdapter,
    ):
        cancellation_reason.churn_type = cancellation_type
        cancellation_reason.cancellation_reason = None
        cancellation_reason.business_cancellation_reason = None
        cancellation_reason.save()

        reason_why = sut.get_reason_why(cancellation_reason.id)

        assert reason_why.cancellation_why is None


@pytest.mark.django_db
class TestServiceVariantAdapterGetAllWithPriceById:
    @pytest.fixture
    @transaction.atomic
    def service_variant(self, business: Business) -> ServiceVariant:
        service = service_recipe.make(business=business)
        _service_variant = service_variant_recipe.make(service=service, price=Decimal(69))

        return _service_variant

    @pytest.fixture
    def service_variant_adapter(self) -> ServiceVariantAdapter:
        return ServiceVariantAdapter()

    def test_get_all_with_price_by_id_returns_active_service_variant_with_price(
        self,
        business: Business,
        service_variant: ServiceVariant,
        service_variant_adapter: ServiceVariantAdapter,
    ):
        non_existing_service_variant_id = -2137
        sv_with_price = service_variant_adapter.get_all_with_price_by_id(
            [service_variant.id, non_existing_service_variant_id]
        )

        assert sv_with_price == [
            ServiceVariantData(
                id=service_variant.id, business_id=business.id, price=service_variant.price
            )
        ]

    def test_when_no_active_services_returns_empty_list(
        self,
        business: Business,
        service_variant_adapter: ServiceVariantAdapter,
    ):
        ServiceVariant.objects.filter(service__business_id=business.id).delete()
        sv_with_price = service_variant_adapter.get_all_with_price_by_id([1, 2])

        assert len(sv_with_price) == 0


@pytest.mark.django_db
class TestAccessControlAdapter:
    @staticmethod
    @pytest.fixture
    def owner_resource(business: Business) -> Resource:
        return staffer_recipe.make(
            business=business,
            staff_user=business.owner,
            staff_access_level=StaffAccessLevels.OWNER.value,
        )

    @staticmethod
    @pytest.fixture
    def staffer_resource(business: Business) -> Resource:
        return staffer_recipe.make(business=business)

    @staticmethod
    @pytest.fixture
    def appliance_resource(business: Business) -> Resource:
        return appliance_recipe.make(business=business)

    @staticmethod
    @pytest.fixture
    def business_other() -> Business:
        return business_recipe.make()

    @staticmethod
    @pytest.fixture
    def staffer_user() -> User:
        return business_user_recipe.make(email="<EMAIL>")

    @staticmethod
    @pytest.fixture
    def sut() -> AccessControlAdapter:
        return AccessControlAdapter()

    def test_user_is_business_staff_accepts_owner_of_business(
        self,
        owner_resource: Resource,
        sut: AccessControlAdapter,
    ):
        assert sut.user_is_business_staff(
            business_id=owner_resource.business.id,
            staffer_id=owner_resource.id,
        )

    def test_user_is_business_staff_accepts_owner_of_inactive_business(
        self,
        owner_resource: Resource,
        sut: AccessControlAdapter,
    ):
        owner_resource.business.active = False
        owner_resource.business.save()

        assert sut.user_is_business_staff(
            business_id=owner_resource.business.id,
            staffer_id=owner_resource.id,
        )

    @pytest.mark.parametrize("access_level", Resource.STAFF_ACCESS_LEVELS_NOT_OWNER)
    def test_user_is_business_staff_accepts_staffer_of_business(
        self,
        staffer_resource: Resource,
        sut: AccessControlAdapter,
        access_level: str,
    ):
        staffer_resource.staff_access_level = access_level
        staffer_resource.save()

        assert sut.user_is_business_staff(
            business_id=staffer_resource.business.id,
            staffer_id=staffer_resource.id,
        )

    @pytest.mark.parametrize("access_level", Resource.STAFF_ACCESS_LEVELS_NOT_OWNER)
    def test_user_is_business_staff_accepts_inactive_staffer_of_business(
        self,
        staffer_resource: Resource,
        sut: AccessControlAdapter,
        access_level: str,
    ):
        staffer_resource.staff_access_level = access_level
        staffer_resource.active = False
        staffer_resource.save()

        assert sut.user_is_business_staff(
            business_id=staffer_resource.business.id,
            staffer_id=staffer_resource.id,
        )

    @pytest.mark.parametrize("access_level", Resource.STAFF_ACCESS_LEVELS_NOT_OWNER)
    def test_user_is_business_staff_accepts_staffer_backed_by_user_of_business(
        self,
        staffer_resource: Resource,
        staffer_user: User,
        sut: AccessControlAdapter,
        access_level: str,
    ):
        staffer_resource.staff_user = staffer_user
        staffer_resource.staff_access_level = access_level
        staffer_resource.save()

        assert sut.user_is_business_staff(
            business_id=staffer_resource.business.id,
            staffer_id=staffer_resource.id,
        )

    def test_user_is_business_staff_rejects_appliance_resources(
        self,
        appliance_resource: Resource,
        sut: AccessControlAdapter,
    ):
        assert not sut.user_is_business_staff(
            business_id=appliance_resource.business.id,
            staffer_id=appliance_resource.id,
        )

    @pytest.mark.parametrize("access_level", list(StaffAccessLevels))
    def test_user_is_business_staff_rejects_other_business_staffer(
        self,
        business_other: Business,
        staffer_resource: Resource,
        sut: AccessControlAdapter,
        access_level: StaffAccessLevels,
    ):
        business = staffer_resource.business
        staffer_resource.business = business_other
        staffer_resource.staff_access_level = access_level.value
        staffer_resource.save()

        assert not sut.user_is_business_staff(
            business_id=business.id,
            staffer_id=staffer_resource.id,
        )

    def test_user_is_business_staff_raises_when_business_not_exists(
        self,
        owner_resource: Resource,
        sut: AccessControlAdapter,
    ):
        business_id = owner_resource.business.id
        owner_resource.business.delete()

        with pytest.raises(Http404):
            sut.user_is_business_staff(
                business_id=business_id,
                staffer_id=owner_resource.id,
            )

    def test_user_is_business_staff_raises_when_resource_not_exists(
        self,
        staffer_resource: Resource,
        sut: AccessControlAdapter,
    ):
        business = staffer_resource.business
        staffer_id = staffer_resource.id
        staffer_resource.delete()

        with pytest.raises(Http404):
            sut.user_is_business_staff(
                business_id=business.id,
                staffer_id=staffer_id,
            )
