from datetime import (
    datetime,
    timedelta,
)
from uuid import uuid4

import mock

import pytest
from django import test
from django.core.exceptions import NON_FIELD_ERRORS
from django.test.utils import override_settings
from django.utils.translation import gettext as _
from freezegun import freeze_time
from mock import patch
from model_bakery import baker

from country_config.enums import Country
from lib.test_utils import (
    create_subbooking,
)
from lib.tools import tznow
from webapps.adyen.consts import oper_result
from webapps.adyen.models import Capture
from webapps.booking.models import (
    Appointment,
    RepeatingBooking,
    SubBooking,
)
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import (
    business_recipe,
    category_recipe,
    region_recipe,
)
from webapps.business.enums import BusinessCategoryEnum, CustomData
from webapps.business.models import (
    Business,
    Resource,
    Service,
)
from webapps.business.models.category import BusinessCategory
from webapps.consents.models import ConsentForm
from webapps.ecommerce.enums import EcommercePermissionsEnum
from webapps.ecommerce.models import EcommercePermission
from webapps.pos.enums import (
    POSPlanPaymentTypeEnum,
    PaymentProviderEnum,
    PaymentTypeEnum,
    receipt_status,
)
from webapps.pos.models import (
    POS,
    PaymentRow,
    PaymentType,
    POSPlan,
    Receipt,
    Transaction,
)
from webapps.profile_setup.models import ProfileSetupProgress
from webapps.structure.models import Region


POS_PLANS = [
    {
        'min_txn_num': 0,
        'provision': 0.025,
        'txn_fee': 0.2,
        'fee_currency': 'USD',
        'currency_locale': 'en_US',
    },
    {
        'min_txn_num': 2,
        'provision': 0.0245,
        'txn_fee': 0.2,
        'fee_currency': 'USD',
        'currency_locale': 'en_US',
    },
    {
        'min_txn_num': 4,
        'provision': 0.024,
        'txn_fee': 0.2,
        'fee_currency': 'USD',
        'currency_locale': 'en_US',
    },
]


@pytest.mark.django_db
@pytest.mark.parametrize(
    'txns_num, plan_idx',
    [
        (0, 0),
        (1, 0),
        (2, 1),
        (3, 1),
        (4, 2),
        (5, 2),
    ],
)
def test_get_pos_plan(txns_num, plan_idx, settings):
    # use pytest-django fixture for settings
    settings.POS__PLANS = POS_PLANS
    settings.API_COUNTRY = Country.US

    plan_models = [
        baker.make(
            POSPlan,
            min_txn_num=plan['min_txn_num'],
            provision=plan['provision'],
            txn_fee=plan['txn_fee'],
            individual=False,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        )
        for plan in POS_PLANS
    ]

    # create business, pos, transactions
    business = baker.make(Business)
    pos = baker.make(POS, business=business)
    txns = []
    if txns_num > 0:
        txns = baker.make(Transaction, pos=pos, _quantity=txns_num)

    payment_type = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=pos)

    # create receipts
    for txn in txns:
        pnref = uuid4().hex

        receipt = baker.make(
            Receipt,
            status_code=receipt_status.PAYMENT_SUCCESS,
            payment_type=payment_type,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()
        baker.make(
            PaymentRow,
            amount=txn.total,
            receipt=receipt,
            status=receipt_status.PAYMENT_SUCCESS,
            payment_type=payment_type,
            pnref=pnref,
            provider=PaymentProviderEnum.ADYEN_PROVIDER,
        )

        baker.make(
            Capture,
            reference=pnref,
            oper_result=oper_result.SUCCESS,
        )

    # create failed transaction and receipt
    pnref = uuid4().hex
    failed_txn = baker.make(Transaction, pos=pos)
    receipt = baker.make(
        Receipt,
        status_code=receipt_status.PAYMENT_FAILED,
        payment_type=payment_type,
        transaction=failed_txn,
    )
    failed_txn.latest_receipt = receipt
    failed_txn.save()

    baker.make(
        PaymentRow,
        amount=failed_txn.total,
        receipt=receipt,
        status=receipt_status.PAYMENT_FAILED,
        payment_type=payment_type,
        pnref=pnref,
        provider=PaymentProviderEnum.ADYEN_PROVIDER,
    )

    baker.make(
        Capture,
        reference=pnref,
        oper_result=oper_result.FAILED,
    )

    # create old transaction
    with freeze_time(tznow() - timedelta(days=32)):
        failed_txn = baker.make(Transaction, pos=pos)
        receipt = baker.make(
            Receipt,
            status_code=receipt_status.PAYMENT_SUCCESS,
            payment_type=payment_type,
            transaction=failed_txn,
        )
        failed_txn.latest_receipt = receipt
        failed_txn.save()

        baker.make(
            PaymentRow,
            amount=failed_txn.total,
            receipt=receipt,
            status=receipt_status.PAYMENT_SUCCESS,
            payment_type=payment_type,
            pnref=pnref,
            provider=PaymentProviderEnum.ADYEN_PROVIDER,
        )

        baker.make(
            Capture,
            reference=pnref,
            oper_result=oper_result.FAILED,
        )

    pos.recalculate_pos_plans()
    # get plan
    plan = business.get_pos_plan(POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT)
    assert plan == plan_models[plan_idx]


@pytest.mark.django_db
def test_get_pos_plan_no_pos(settings):
    settings.POS__PLANS = POS_PLANS
    settings.API_COUNTRY = Country.US
    business = baker.prepare(Business)
    plan = baker.make(POSPlan, min_txn_num=0)
    baker.make(POSPlan, min_txn_num=10)
    assert business.get_pos_plan(POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT) == plan


@pytest.mark.django_db
def test_set_timezone():
    business = baker.make(Business, time_zone_name='America/New_York')
    old_timezone = business.get_timezone()
    booked = datetime.now(tz=business.get_timezone()) + timedelta(minutes=15)
    booking, _, _ = create_subbooking(business=business)
    booking.appointment.repeating = repeating = baker.make(
        RepeatingBooking,
        repeat_till=booked + timedelta(days=7),
    )
    Appointment.objects.update(
        booked_from=booked,
        booked_till=booked,
        repeating=repeating,
    )

    new_region = baker.make(Region, time_zone_name='America/Chicago')
    new_timezone = new_region.gettz()

    expected_booked_from = booking.booked_from.astimezone(old_timezone).replace(tzinfo=new_timezone)
    expected_repeat_till = booking.appointment.repeating.repeat_till.astimezone(
        old_timezone
    ).replace(tzinfo=new_timezone)

    business.set_timezone(new_timezone, shift_bookings=True)
    assert business.get_timezone() == new_timezone
    assert business.time_zone_name == new_region.time_zone_name

    booking = SubBooking.objects.get(id=booking.id)
    assert booking.booked_from == expected_booked_from.astimezone(booking.booked_from.tzinfo)
    assert booking.appointment.repeating.repeat_till == (
        expected_repeat_till.astimezone(booking.appointment.repeating.repeat_till.tzinfo)
    )


@pytest.mark.django_db
def test_set_timezone_in_multibooking():
    business = baker.make(Business, time_zone_name='America/New_York')
    tz = business.get_timezone()
    booked_from = datetime.now(tz=tz) + timedelta(minutes=15)
    booking0, booking1 = create_appointment(
        [
            {
                'booked_from': booked_from,
                'booked_till': booked_from + timedelta(minutes=30),
            },
            {
                'booked_from': booked_from + timedelta(minutes=30),
                'booked_till': booked_from + timedelta(minutes=60),
            },
        ],
        business=business,
    ).subbookings

    new_region = baker.make(Region, time_zone_name='America/Chicago')
    new_tz = new_region.gettz()
    exp_b0_from = booking0.booked_from.astimezone(tz).replace(tzinfo=new_tz)
    exp_b0_till = booking0.booked_till.astimezone(tz).replace(tzinfo=new_tz)
    exp_b1_from = booking1.booked_from.astimezone(tz).replace(tzinfo=new_tz)
    exp_b1_till = booking1.booked_till.astimezone(tz).replace(tzinfo=new_tz)

    business.set_timezone(new_tz, shift_bookings=True)
    assert business.get_timezone() == new_tz
    assert business.time_zone_name == new_region.time_zone_name

    booking0.refresh_from_db()
    booking1.refresh_from_db()

    assert booking0.booked_from == exp_b0_from
    assert booking0.booked_till == exp_b0_till
    assert booking1.booked_from == exp_b1_from
    assert booking1.booked_till == exp_b1_till
    assert booking0.appointment_id == booking1.appointment_id
    appointment = booking0.appointment
    appointment.refresh_from_db()
    assert appointment.booked_from == exp_b0_from
    assert appointment.booked_till == exp_b1_till


@pytest.mark.django_db
@mock.patch('webapps.business.tasks.shift_bookings_to_timezone_task.delay')
def test_task_not_shifting_bookings_past_or_created_after_tz_change(mock_delay):
    business = baker.make(Business, time_zone_name='America/New_York')
    old_timezone = business.get_timezone()

    # Create a past appointment (should NOT be included)
    past_booked = datetime.now(tz=business.get_timezone()) - timedelta(hours=1)
    past_booking, _, _ = create_subbooking(business=business)
    past_booking.appointment.booked_from = past_booked
    past_booking.appointment.booked_till = past_booked + timedelta(minutes=45)
    past_booking.appointment.save()

    # Create a future appointment (should be included)
    future_booked = datetime.now(tz=business.get_timezone()) + timedelta(hours=1)
    future_booking, _, _ = create_subbooking(business=business)
    future_booking.appointment.booked_from = future_booked
    future_booking.appointment.booked_till = future_booked + timedelta(minutes=45)
    future_booking.appointment.save()

    # An appointment created in the future (should NOT be included)
    future_created, _, _ = create_subbooking(business=business)
    future_created.appointment.created = future_booked + timedelta(days=45)
    future_created.appointment.booked_from = future_booked
    future_created.appointment.booked_till = future_booked + timedelta(minutes=45)
    future_created.appointment.save()

    new_region = baker.make(Region, time_zone_name='America/Chicago')
    new_timezone = new_region.gettz()

    business.set_timezone(new_timezone, shift_bookings=True)

    assert business.get_timezone() == new_timezone
    assert business.time_zone_name == new_region.time_zone_name

    # pylint: disable=protected-access
    mock_delay.assert_called_once_with(
        business_id=business.id,
        old_timezone_name=old_timezone._long_name,
        new_timezone_name=new_timezone._long_name,
        tz_change_date=mock.ANY,
        appointment_ids=[future_booking.appointment.id],
    )


BUSINESS_VALIDATION_ERRORS_DICT = {
    'region': {
        'code': 'invalid',
        'field': 'region',
        'type': 'validation',
        'description': _('Active business require region.'),
    },
    'services': {
        'code': 'invalid',
        'field': NON_FIELD_ERRORS,
        'type': 'validation',
        'description': _('Active business require at least one service.'),
    },
    'resources': {
        'code': 'invalid',
        'field': NON_FIELD_ERRORS,
        'type': 'validation',
        'description': _('Active business require at least one staffer or resource.'),
    },
    'missing_categories': {
        'code': 'invalid',
        'field': 'categories',
        'type': 'validation',
        'description': _('Active business require at least one category.'),
    },
    'missing_primary_category': {
        'code': 'invalid',
        'field': 'primary_category',
        'type': 'validation',
        'description': _('Active business require primary category.'),
    },
    'different_primary_category': {
        'code': 'invalid',
        'field': 'primary_category',
        'type': 'validation',
        'description': _('Primary category must be one of business categories.'),
    },
}


@pytest.mark.django_db
def test_business_validation_success(business_with_categories):
    business = business_with_categories
    zip_reg = baker.make(Region, name='90210', type=Region.Type.ZIP)
    business.region = zip_reg
    baker.make(Service, business=business)
    baker.make(Resource, business=business)
    assert [] == business.validate()


@pytest.mark.django_db
def test_business_validation_missing_region(business_with_missing_region):
    business = business_with_missing_region
    assert business.region is None

    baker.make(Service, business=business)
    baker.make(Resource, business=business)

    expected = [BUSINESS_VALIDATION_ERRORS_DICT['region']]
    assert expected == business.validate()


@pytest.mark.django_db
def test_business_validation_missing_services(business_with_categories):
    business = business_with_categories
    zip_reg = baker.make(Region, name='90210', type=Region.Type.ZIP)
    business.region = zip_reg
    baker.make(Resource, business=business)

    expected = [BUSINESS_VALIDATION_ERRORS_DICT['services']]
    assert expected == business.validate()


@pytest.mark.django_db
def test_business_validation_missing_resources(business_with_categories):
    business = business_with_categories
    zip_reg = baker.make(Region, name='90210', type=Region.Type.ZIP)
    business.region = zip_reg
    baker.make(Service, business=business)

    expected = [BUSINESS_VALIDATION_ERRORS_DICT['resources']]
    assert expected == business.validate()


@pytest.mark.django_db
def test_business_validation_missing_categories():
    business = baker.make(Business, categories=[])
    zip_reg = baker.make(Region, name='90210', type=Region.Type.ZIP)
    business.region = zip_reg
    baker.make(Service, business=business)
    baker.make(Resource, business=business)
    expected = [BUSINESS_VALIDATION_ERRORS_DICT['missing_categories']]
    assert expected == business.validate()


@pytest.mark.django_db
def test_business_validation_valid_signature(business_with_categories):
    business = business_with_categories
    zip_reg = baker.make(Region, name='90210', type=Region.Type.ZIP)
    business.region = zip_reg
    baker.make(Service, business=business)
    baker.make(Resource, business=business)
    assert [] == business.validate()


@pytest.mark.django_db
def test_business_validation_missing_primary_category(business_with_categories):
    business = business_with_categories
    business.primary_category = None
    zip_reg = baker.make(Region, name='90210', type=Region.Type.ZIP)
    business.region = zip_reg
    baker.make(Service, business=business)
    baker.make(Resource, business=business)
    expected = [BUSINESS_VALIDATION_ERRORS_DICT['missing_primary_category']]
    assert expected == business.validate()


@pytest.mark.django_db
def test_business_validation_different_primary_category(business_with_categories):
    business = business_with_categories
    business.primary_category = baker.make(BusinessCategory)
    zip_reg = baker.make(Region, name='90210', type=Region.Type.ZIP)
    business.region = zip_reg
    baker.make(Service, business=business)
    baker.make(Resource, business=business)
    expected = [BUSINESS_VALIDATION_ERRORS_DICT['different_primary_category']]
    assert expected == business.validate()


@pytest.mark.django_db
def test_create_covid19_consent_form():
    business = baker.make(Business)
    assert ConsentForm.objects.filter(business=business).exists() is True


@pytest.mark.django_db
@override_settings(LANGUAGE_CODE='abcd')
def test_create_custom_language_covid19_consent_form():
    business = baker.make(Business)
    assert ConsentForm.objects.filter(business=business).exists() is False


@pytest.mark.django_db
@override_settings(API_COUNTRY=Country.PL)
def test_create_resource_from_owner():
    user = baker.make('user.User')
    business = baker.make(Business, owner=user)
    resource = business.create_resource_from_owner()
    assert resource
    assert EcommercePermission.objects.filter(
        resource=resource,
        permission=EcommercePermissionsEnum.STORE_AVAILABLE,
    ).exists()


@pytest.mark.django_db
@pytest.mark.parametrize(
    'category_internal_name, expected_value',
    [
        (BusinessCategoryEnum.BARBERS, False),
        (BusinessCategoryEnum.THERAPY, True),
    ],
)
@override_settings(PHYSIOTHERAPY_COUNTRIES=['xx'], API_COUNTRY='xx')
def test_business_patient_file_enabled(category_internal_name, expected_value):
    category = category_recipe.make(internal_name=category_internal_name)
    business = business_recipe.make(
        categories=[category],
        custom_data={CustomData.PHYSIOTHERAPY_ENABLED: True},
    )
    assert business.patient_file_enabled is expected_value

    business._prefetched_objects_cache = {}  # pylint: disable=protected-access
    del business.patient_file_enabled  # clear cached property
    assert business.patient_file_enabled is expected_value

    business = Business.objects.filter(id=business.id).prefetch_related('categories').first()
    assert business.patient_file_enabled is expected_value


@pytest.mark.django_db
@patch('webapps.business.models.Business.update_all_subdomains_deeplinks')
class TestBusinessSave(test.TestCase):
    def test_bussiness_name_change_updates_deeplinks(self, mocked_update_deeplinks):
        business = baker.make(Business, name='old name')
        with self.captureOnCommitCallbacks(execute=True):
            business.name = 'new name'
            business.save()
        mocked_update_deeplinks.assert_called_once_with()

    def test_should_update_deeplinks_after_primary_category_change(self, mocked_update_deeplinks):
        business = business_recipe.make(primary_category=baker.make(BusinessCategory))
        another_category = baker.make(BusinessCategory)
        with self.captureOnCommitCallbacks(execute=True):
            business.primary_category = another_category
            business.save()
        mocked_update_deeplinks.assert_called_once_with()

    def test_should_update_deeplinks_after_region_change(self, mocked_update_deeplinks):
        business = business_recipe.make()
        another_region = region_recipe.make()
        with self.captureOnCommitCallbacks(execute=True):
            business.region = another_region
            business.save()
        mocked_update_deeplinks.assert_called_once_with()

    def test_should_update_deeplink_after_seo_region_change(self, mocked_update_deeplinks):
        business = business_recipe.make(seo_region=region_recipe.make())
        another_region = region_recipe.make()
        with self.captureOnCommitCallbacks(execute=True):
            business.seo_region = another_region
            business.save()
        mocked_update_deeplinks.assert_called_once_with()


@pytest.mark.django_db
def test_business_not_published_no_profile_setup_progress():
    business: Business = business_recipe.make(hidden_in_search=True)
    assert not business.not_published


@pytest.mark.django_db
def test_business_not_published_business_with_profile_setup():
    business: Business = business_recipe.make(hidden_in_search=True)
    baker.make(ProfileSetupProgress, business=business)
    assert business.not_published


@pytest.mark.django_db
def test_business_not_published_business_in_setup():
    business: Business = business_recipe.make(hidden_in_search=False, status=Business.Status.SETUP)
    baker.make(ProfileSetupProgress, business=business)
    assert business.not_published


@pytest.mark.django_db
def test_business_not_published_completed():
    business: Business = business_recipe.make(hidden_in_search=True, status=Business.Status.PAID)
    baker.make(ProfileSetupProgress, business=business, completed=True)
    assert not business.not_published


@pytest.mark.django_db
def test_business_not_published_business_in_setup_no_profile_setup():
    business: Business = business_recipe.make(hidden_in_search=False, status=Business.Status.SETUP)
    assert not business.not_published


@pytest.mark.django_db
def test_business_not_published_business_not_hidden_in_search():
    business: Business = business_recipe.make(hidden_in_search=False)
    baker.make(ProfileSetupProgress, business=business)
    assert not business.not_published


@pytest.mark.django_db
def test_business_not_mypp_completed():
    business: Business = business_recipe.make(hidden_in_search=False)
    baker.make(ProfileSetupProgress, business=business)
    assert not business.was_published_after_profile_setup


@pytest.mark.django_db
def test_business_mypp_completed():
    business: Business = business_recipe.make(hidden_in_search=False)
    baker.make(ProfileSetupProgress, business=business, completed=True)
    assert business.was_published_after_profile_setup


@pytest.mark.django_db
def test_business_mypp_completed_no_profile_setup_progress():
    business: Business = business_recipe.make(hidden_in_search=False)
    assert not business.was_published_after_profile_setup
