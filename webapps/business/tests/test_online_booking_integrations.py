import unittest

import pytest
from model_bakery import baker

from webapps.business.enums import FacebookFBEConnect
from webapps.business.models import Business
from webapps.business.online_booking_integrations import (
    FacebookIntegration,
    IntegrationStatus,
)


@pytest.mark.django_db
class OnlineBookingIntegrationTests(unittest.TestCase):

    def setUp(self) -> None:
        super().setUp()
        self.business: Business = baker.make(
            Business,
            active=True,
            visible=True,
            status=Business.Status.PAID,
            address='Some address',
        )
        self.business.set_facebook_fbe_connection_data(FacebookFBEConnect(enabled=True))

    def test_facebook_status(self):
        facebook = FacebookIntegration(self.business)
        assert facebook.status == IntegrationStatus.ACTIVE

        self.business.remove_facebook_fbe_connection()
        facebook = FacebookIntegration(self.business)
        assert facebook.status == IntegrationStatus.DISABLED
