from django.utils.translation import gettext_lazy as _

from lib.enums import StrChoicesEnum


class ConsentAction(StrChoicesEnum):
    AGREE = 'AGREE', _('Agree')
    DISAGREE = 'DISAGREE', _('Disagree')
    ASK_ME_LATER = 'ASK_ME_LATER', _('Ask me later')


class ConsentFieldType(StrChoicesEnum):
    STRING = 'STRING', 'String'
    BOOLEAN = 'BOOLEAN', 'Boolean (checkbox)'


class ConsentIcon(StrChoicesEnum):
    SUCCESS = 'success', 'Success'
    FAILED = 'failed', 'Failed'
    INFO = 'info', 'Info'
    WARNING = 'warning', 'Warning'


class USUserInputKYCEntityType(StrChoicesEnum):
    INDIVIDUAL = 'individual', 'Individual'
    COMPANY = 'company', 'Company'


class ConsentCode(StrChoicesEnum):
    FRANCE_MIGRATE_TO_STRIPE = 'FRANCE_MIGRATE_TO_STRIPE', 'Migrate to Stripe on France'
    FRANCE_MIGRATE_TO_STRIPE_RETRY = (
        'FRANCE_MIGRATE_TO_STRIPE_RETRY',
        'Migrate to Stripe on France retry',
    )
    POLAND_MIGRATE_TO_STRIPE = (  # deprecated
        'POLAND_MIGRATE_TO_STRIPE',
        'Migrate from Adyen to Stripe in Poland',
    )
    POLAND_MIGRATE_TO_STRIPE_V2 = (  # deprecated
        'POLAND_MIGRATE_TO_STRIPE_V2',
        'Migrate from Adyen to Stripe in Poland (version 2)',
    )
    POLAND_MIGRATE_TO_STRIPE_V3 = (  # deprecated
        'POLAND_MIGRATE_TO_STRIPE_V3',
        'Migrate from Adyen to Stripe in Poland (version 3 - 27.05)',
    )
    POLAND_MIGRATE_TO_STRIPE_V4 = (  # deprecated
        'POLAND_MIGRATE_TO_STRIPE_V4',
        'Migrate from Adyen to Stripe in Poland (version 4 - 03.06)',
    )
    POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL = (
        'POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL',
        'Onboard to Stripe - Individuals (peselowcy)',
    )
    POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL_V2 = (
        'POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL_V2',
        'Onboard to Stripe - Individuals (peselowcy) V2',
    )
    POLAND_ONBOARD_TO_STRIPE_JDG = (
        'POLAND_ONBOARD_TO_STRIPE_JDG',
        'Onboard to Stripe - JDGs (from CEIDG)',
    )
    POLAND_ONBOARD_TO_STRIPE_JDG_V2 = (
        'POLAND_ONBOARD_TO_STRIPE_JDG_V2',
        'Onboard to Stripe - JDGs (from CEIDG) V2',
    )
    POLAND_ONBOARD_TO_STRIPE_KRS = (
        'POLAND_ONBOARD_TO_STRIPE_KRS',
        'Onboard to Stripe - Companies (from KRS)',
    )
    POLAND_ONBOARD_TO_STRIPE_KRS_V2 = (
        'POLAND_ONBOARD_TO_STRIPE_KRS_V2',
        'Onboard to Stripe - Companies (from KRS) V2',
    )
    STRIPE_ACCOUNT_RESTRICTED_SOON = (
        'STRIPE_ACCOUNT_RESTRICTED_SOON',
        'Stripe account will be restricted soon',
    )
    US_USER_INPUT_KYC = (
        'US_USER_INPUT_KYC',
        'User Input (Effortless) KYC in US (Stripe)',
    )
    GB_MIGRATE_TO_STRIPE = 'GB_MIGRATE_TO_STRIPE', 'Migrate from Adyen to Stripe in Great Britain'

    # test consents
    TEST_POLAND_MIGRATE_TO_STRIPE = (  # deprecated
        'TEST_POLAND_MIGRATE_TO_STRIPE',
        'Migrate to Stripe in Poland (TEST)',
    )
    SIMPLE_TEST_CONSENT = 'SIMPLE_TEST_CONSENT', 'Simple test consent'
    COMPLEX_TEST_CONSENT = 'COMPLEX_TEST_CONSENT', 'Complex test consent'
    CUSTOM_TEST_CONSENT = 'CUSTOM_TEST_CONSENT', 'Custom test consent (webview)'
    DAC7_PESEL = 'DAC7_PESEL_CUSTOM', 'DAC7 PESEL consent'
    DAC7_PESEL_FORCE = 'DAC7_PESEL_CUSTOM_FORCE', 'DAC7 PESEL consent [force]'
    DAC7_BIRTHDAY = 'DAC7_BIRTHDAY_CUSTOM', 'DAC7 birthday consent'
    DAC7_BIRTHDAY_FORCE = 'DAC7_BIRTHDAY_CUSTOM_FORCE', 'DAC7 birthday consent [force]'
    DAC7_COMPANY_PL = 'DAC7_COMPANY_PL_CUSTOM', 'DAC7 company PL consent'
    DAC7_COMPANY_PL_FORCE = 'DAC7_COMPANY_PL_CUSTOM_FORCE', 'DAC7 company PL consent [force]'
    DAC7_COMPANY_PL_FORCE_V2 = (
        'DAC7_COMPANY_PL_CUSTOM_FORCE_V2',
        'DAC7 company PL consent v2 [force]',
    )
    DAC7_COMPANY_ES = 'DAC7_COMPANY_ES_CUSTOM', 'DAC7 company ES consent'
    DAC7_COMPANY_ES_FORCE = 'DAC7_COMPANY_ES_CUSTOM_FORCE', 'DAC7 company ES consent [force]'
    DAC7_COMPANY_ES_FORCE_V2 = (
        'DAC7_COMPANY_ES_CUSTOM_FORCE_V2',
        'DAC7 company ES consent v2 [force]',
    )
    DAC7_COMPANY_FR = 'DAC7_COMPANY_FR_CUSTOM', 'DAC7 company FR consent'
    DAC7_COMPANY_FR_FORCE = 'DAC7_COMPANY_FR_CUSTOM_FORCE', 'DAC7 company FR consent [force]'
    DAC7_COMPANY_FR_FORCE_V2 = (
        'DAC7_COMPANY_FR_CUSTOM_FORCE_V2',
        'DAC7 company FR consent v2 [force]',
    )
    SEMI_EFFORTLESS_KYC = 'SEMI_EFFORTLESS_KYC', 'Semi Effortless KYC consent'
    SEMI_EFFORTLESS_KYC_ES = 'SEMI_EFFORTLESS_KYC_ES', 'Semi Effortless KYC consent ES'
    DAC7_IE = 'DAC7_IE_CUSTOM', 'DAC7 IE consent'
