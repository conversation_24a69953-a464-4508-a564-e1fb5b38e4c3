import datetime
from dataclasses import asdict

from lib.business_consents.events import (
    account_holder_siren_consent_accepted_event,
    account_holder_nip_consent_accepted_event,
    account_holder_pesel_consent_accepted_event,
    user_input_kyc_consent_accepted_event,
)
from lib.payment_providers.entities import (
    AccountHolderSirenEntity,
    AccountHolderNIPEntity,
    AccountHolderPESELEntity,
    USUserInputKYCEntity,
)

from webapps.business_consents.enums import ConsentCode, ConsentAction
from webapps.business_consents.models import BusinessConsent


def account_holder_siren_consent_accepted_event_handler(consent: BusinessConsent):
    consent.refresh_from_db()
    if consent.decision != ConsentAction.AGREE:
        return

    account_holder_siren_consent_accepted_event.send(
        asdict(
            AccountHolderSirenEntity(
                business_id=consent.business_id,
                siren=consent.payload['siren'],
            )
        ),
    )


def account_holder_pesel_consent_accepted_event_handler(consent: BusinessConsent):
    consent.refresh_from_db()
    if consent.decision != ConsentAction.AGREE:
        return

    dob = datetime.datetime.strptime(consent.payload['date_of_birth'], "%Y-%m-%d").date()
    account_holder_pesel_consent_accepted_event.send(
        asdict(
            AccountHolderPESELEntity(
                business_id=consent.business_id,
                date_of_birth=dob,
                first_name=consent.payload['first_name'],
                last_name=consent.payload['last_name'],
            )
        ),
    )


def account_holder_jdg_consent_accepted_event_handler(consent: BusinessConsent):
    consent.refresh_from_db()
    if consent.decision != ConsentAction.AGREE:
        return

    dob = datetime.datetime.strptime(consent.payload['date_of_birth'], "%Y-%m-%d").date()
    account_holder_nip_consent_accepted_event.send(
        asdict(
            AccountHolderNIPEntity(
                business_id=consent.business_id,
                date_of_birth=dob,
                nip=consent.payload['nip'],
            )
        ),
    )


def account_holder_krs_consent_accepted_event_handler(consent: BusinessConsent):
    consent.refresh_from_db()
    if consent.decision != ConsentAction.AGREE:
        return

    account_holder_nip_consent_accepted_event.send(
        asdict(
            AccountHolderNIPEntity(
                business_id=consent.business_id,
                nip=consent.payload['nip'],
                date_of_birth=None,
            )
        ),
    )


def stripe_account_restricted_soon_handler(consent: BusinessConsent):
    consent.refresh_from_db()
    if consent.decision != ConsentAction.ASK_ME_LATER:
        # if user took action, we don't do anything
        return

    # otherwise, we will remind him again later;
    # visibility will be turned back by celery task in ~1 day
    # task: consent_stripe_account_restricted_soon_turn_back_visibility
    consent.visible = False
    consent.save(update_fields=['visible'])


def us_user_input_kyc_handler(consent: BusinessConsent):
    consent.refresh_from_db()
    if consent.decision != ConsentAction.AGREE:
        return

    consent_data = consent.payload
    user_input_kyc_consent_accepted_event.send(
        asdict(
            USUserInputKYCEntity(
                business_id=consent.business_id,
                type=consent_data['type'],
                filling_ip=consent.filling_ip,
                filling_date=consent.filling_date,
                first_name=consent_data.get('first_name'),
                last_name=consent_data.get('last_name'),
                company_name=consent_data.get('company_name'),
            )
        ),
    )


CONSENT_EVENT_HANDLERS_MAP = {
    ConsentCode.FRANCE_MIGRATE_TO_STRIPE: account_holder_siren_consent_accepted_event_handler,
    ConsentCode.FRANCE_MIGRATE_TO_STRIPE_RETRY: account_holder_siren_consent_accepted_event_handler,
    ConsentCode.POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL: (
        account_holder_pesel_consent_accepted_event_handler
    ),
    ConsentCode.POLAND_ONBOARD_TO_STRIPE_JDG: account_holder_jdg_consent_accepted_event_handler,
    ConsentCode.POLAND_ONBOARD_TO_STRIPE_KRS: account_holder_krs_consent_accepted_event_handler,
    ConsentCode.POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL_V2: (
        account_holder_pesel_consent_accepted_event_handler
    ),
    ConsentCode.POLAND_ONBOARD_TO_STRIPE_JDG_V2: account_holder_jdg_consent_accepted_event_handler,
    ConsentCode.POLAND_ONBOARD_TO_STRIPE_KRS_V2: account_holder_krs_consent_accepted_event_handler,
    ConsentCode.STRIPE_ACCOUNT_RESTRICTED_SOON: stripe_account_restricted_soon_handler,
    ConsentCode.US_USER_INPUT_KYC: us_user_input_kyc_handler,
}
