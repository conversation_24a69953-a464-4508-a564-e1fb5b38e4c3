import hashlib

from django.contrib.admin.models import LogEntry
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q, Prefetch, OuterRef
from django.utils.encoding import smart_bytes

from lib.events import lazy_event_receiver
from lib.tools import tznow
from webapps.booking.models import Appointment, AppointmentTraveling
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.consents.models import Consent
from webapps.family_and_friends.enums import RelationshipType

from webapps.family_and_friends.events import (
    delete_family_and_friends_data_for_user,
    send_email_with_invitation,
    send_push_with_invitation,
    send_sms_with_invitation,
)
from webapps.family_and_friends.models import (
    MemberInvitation,
    MemberProfile,
    BCIRelation,
    MemberRelations,
    MemberBusinessCustomerInfo,
)
from webapps.family_and_friends.notifications import (
    MemberInvitationEmailNotification,
    MemberInvitationPushNotification,
    MemberInvitationSmsNotification,
)
from webapps.family_and_friends.tasks.member_business_customer_info import (
    bump_related_to_member_bci_documents,
)
from webapps.pop_up_notification.models import (
    FamilyAndFriendsInvitationNotification,
    FamilyAndFriendsInvitationResponseNotification,
    FamilyAndFriendsUnlinkNotification,
)
from webapps.user.models import User
from webapps.user.utils import cancel_appointment, choose_delete_user_strategy, hash_field


def delete_admin_logs_for_member_profile(member: MemberProfile):
    member_ct = ContentType.objects.get_for_model(MemberProfile)
    relation_ct = ContentType.objects.get_for_model(MemberRelations)
    member_bci_ct = ContentType.objects.get_for_model(MemberBusinessCustomerInfo)
    relation_ids = [
        relation.id
        for relation in MemberRelations.objects.filter(Q(parent=member) | Q(member=member))
    ]
    member_bci_ids = [
        member_bci.id for member_bci in MemberBusinessCustomerInfo.objects.filter(member=member)
    ]
    LogEntry.objects.filter(content_type=member_ct, object_id=member.id).delete()
    LogEntry.objects.filter(content_type=relation_ct, object_id__in=relation_ids).delete()
    LogEntry.objects.filter(content_type=member_bci_ct, object_id__in=member_bci_ids).delete()


def delete_photos_from_bcis(member: MemberProfile):
    photos_ids_to_delete = list(
        BusinessCustomerInfo.objects.filter(memberbusinesscustomerinfo__member=member).values_list(
            'photo_id', flat=True
        )
    )
    choose_delete_user_strategy().delete_photos(photos_ids_to_delete)


@lazy_event_receiver(send_email_with_invitation)
def send_email_with_invitation_receiver(member_invitation: MemberInvitation, **kwargs):
    MemberInvitationEmailNotification(None, member_invitation, **kwargs).send()


@lazy_event_receiver(send_sms_with_invitation)
def send_sms_with_invitation_receiver(member_invitation: MemberInvitation, **kwargs):
    MemberInvitationSmsNotification(None, member_invitation, **kwargs).send()


@lazy_event_receiver(send_push_with_invitation)
def send_push_with_invitation_receiver(
    member_invitation: MemberInvitation, notification_id: int, user_id: int, **kwargs
):
    MemberInvitationPushNotification(
        None, member_invitation, user_id, notification_id, **kwargs
    ).send()


# pylint: disable=too-many-branches
@lazy_event_receiver(delete_family_and_friends_data_for_user)
def delete_family_and_friends_data_for_user_receiver(user_id, **kwargs):
    deleted_member = (
        MemberProfile.objects.prefetch_related(
            Prefetch(
                'members',
                queryset=MemberProfile.objects.relationship_type(
                    [
                        Q(member=OuterRef('pk')),
                        Q(parent__user_profile__user__id=user_id),
                    ]
                ),
            ),
            'parents',
        )
        .filter(user_profile__user__id=user_id)
        .first()
    )
    if not deleted_member:
        return

    delete_admin_logs_for_member_profile(deleted_member)

    deleted_member.photo = None
    deleted_member.save()

    for member in deleted_member.members.filter(user_profile__isnull=True):
        member.first_name = 'deleted'
        member.last_name = ''
        member.birthday = None
        member.email = None
        member.cell_phone = None
        member.photo = None
        if member.relationship_type == RelationshipType.PET:
            member.additional_data = {}
        elif member.relationship_type == RelationshipType.VEHICLE:
            id_md5 = hashlib.md5(smart_bytes(member.id)).hexdigest()
            member.additional_data = {'registration_number': f'{id_md5}_deleted_vehicle'}
        member.save()
        delete_admin_logs_for_member_profile(member)

        delete_photos_from_bcis(member)

    deleted_member.refresh_from_db()

    bump_related_to_member_bci_documents(deleted_member.id)

    for member in deleted_member.members.all():
        deleted_member.unlink_child(member)
    for parent in deleted_member.parents.all():
        deleted_member.unlink_parent(parent)

    bci_relations = BCIRelation.objects.filter(
        Q(parent_bci__memberprofile__id=deleted_member.id)
        | Q(member_bci__memberprofile__id=deleted_member.id)
    )
    bci_relations__inactive_members = bci_relations.filter(member_bci__user__isnull=True)
    bci_relations__active_members = bci_relations.filter(member_bci__user__isnull=False)

    user = User.objects.get(id=user_id)
    for bci_relation in bci_relations__inactive_members:
        appointments = Appointment.objects.filter(
            booked_for__id=bci_relation.member_bci.id, booked_from__gte=tznow()
        ).exclude(status=Appointment.STATUS.CANCELED)
        for appointment in appointments:
            cancel_appointment(appointment, user=user)
        member_bci = bci_relation.member_bci
        member_bci.visible_in_business = False
        member_bci.soft_delete()
        Appointment.objects.filter(booked_for__id=member_bci.id).update(
            customer_name=None,
            customer_email=None,
            customer_phone=None,
        )
        for appointment_traveling in AppointmentTraveling.objects.filter(
            appointments__booked_for__id=member_bci.id
        ).all():
            appointment_traveling.address_line_1 = hash_field(appointment_traveling.address_line_1)
            appointment_traveling.address_line_2 = hash_field(appointment_traveling.address_line_2)
            appointment_traveling.apartment_number = ""
            appointment_traveling.city = ""
            appointment_traveling.zipcode = ""
            appointment_traveling.latitude = None
            appointment_traveling.longitude = None
            appointment_traveling.save()
        member_bci.transactions.update(customer_data='')
        for consent in Consent.objects.filter(customer=member_bci):
            choose_delete_user_strategy().clear_consent(consent, {'email_prefix': ''})

    for bci_relation in bci_relations__active_members:
        bci_relation.delete()

    clear_notifications(deleted_member)


def clear_notifications(deleted_member: MemberProfile):
    additional_filters = {'used': False, 'valid_till__gt': tznow()}
    for notification in FamilyAndFriendsInvitationNotification.objects.filter(
        parent=deleted_member,
        **additional_filters,
    ):
        notification.soft_delete()

    for notification in FamilyAndFriendsInvitationResponseNotification.objects.filter(
        member=deleted_member,
        **additional_filters,
    ):
        notification.soft_delete()

    for notification in FamilyAndFriendsUnlinkNotification.objects.filter(
        trigger_profile=deleted_member,
        **additional_filters,
    ):
        notification.soft_delete()
