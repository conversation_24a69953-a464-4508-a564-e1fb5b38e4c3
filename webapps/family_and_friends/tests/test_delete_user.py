from unittest.mock import patch
import mock
import pytest
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls.base import reverse
from model_bakery import baker

from lib.email.client import IterableClient
from lib.feature_flag.feature import ForgetUserEmailGDPRFlag
from lib.tests.utils import override_feature_flag
from lib.tools import datetimeinfinity

from webapps.admin_extra.tests import DjangoTestCase
from webapps.booking.models import AppointmentTraveling
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import bci_recipe, bci_no_user_recipe, vehicle_member_bci_recipe
from webapps.business.elasticsearch.business_customer import BusinessCustomerDocument
from webapps.consents.models import Consent
from webapps.consents.storages import consents_storage
from webapps.family_and_friends.baker_recipes import (
    member_recipe,
    relation_recipe,
    active_bci_relation_recipe,
    inactive_member_recipe,
    inactive_bci_relation_recipe,
)
from webapps.family_and_friends.enums import RelationshipType
from webapps.family_and_friends.models import MemberRelations
from webapps.family_and_friends.tests.utils import (
    create_appointment_for_member,
    add_child,
    send_invite,
)
from webapps.pop_up_notification.enums import FamilyAndFriendsInvitationResponseType
from webapps.pop_up_notification.models import (
    FamilyAndFriendsInvitationResponseNotification,
    FamilyAndFriendsInvitationNotification,
    FamilyAndFriendsUnlinkNotification,
)
from webapps.pos.baker_recipes import transaction_recipe
from webapps.user.baker_recipes import user_recipe, customer_user, customer_user_with_photo
from webapps.user.models import User


def mock_storage_save(*args, **kwargs):
    return 'mock-image.png'


class TestDeleteUserFromAdmin(DjangoTestCase):
    def setUp(self):
        self.admin = user_recipe.make(
            is_superuser=True,
            superuser=True,
            is_staff=True,
        )
        self.login_admin(self.admin)
        self.customer = customer_user_with_photo.make(cell_phone='111 222 333')
        self.member = member_recipe.make(
            user_profile=self.customer.customer_profile,
            cell_phone=self.customer.cell_phone,
            email=self.customer.email,
            photo=self.customer.customer_profile.photo,
        )

        self.members = []
        for _ in range(5):
            child = member_recipe.make()
            self.members.append(child)
            add_child(parent=self.member, child=child)
            send_invite(parent=self.member, child=child)
        relation_recipe.make(member=self.member)

        self.url_delete = reverse('admin:delete_customer_user', args=[self.customer.id])

    def create_inactive_member(self):
        inactive_member = inactive_member_recipe.make(first_name='Zbyszko')
        add_child(self.member, inactive_member)
        return inactive_member

    def create_notifications(self):
        noti = FamilyAndFriendsInvitationResponseNotification(
            user=self.member.parents.first().user_profile.user,
            member=self.member,
            valid_till=datetimeinfinity(),
            used=False,
            invitation_status=FamilyAndFriendsInvitationResponseType.ACCEPTED,
        )
        noti.save()

        noti = FamilyAndFriendsInvitationResponseNotification(
            user=customer_user.make(),
            member=self.member,
            valid_till=datetimeinfinity(),
            used=False,
            invitation_status=FamilyAndFriendsInvitationResponseType.REJECTED,
        )
        noti.save()

        noti = FamilyAndFriendsInvitationResponseNotification(
            user=customer_user.make(),
            member=self.member,
            valid_till=datetimeinfinity(),
            used=False,
            invitation_status=FamilyAndFriendsInvitationResponseType.EXPIRED,
        )
        noti.save()

    def create_member_bci(self):
        member_bci = bci_recipe.make(
            user=self.member.user_profile.user,
            first_name=self.customer.first_name,
            last_name=self.customer.last_name,
            cell_phone=self.customer.cell_phone,
            email=self.customer.email,
        )
        self.member.bcis.add(member_bci)
        business = member_bci.business
        return member_bci, business

    def create_bcis(self):
        member_bci, business = self.create_member_bci()
        self.parent = member_recipe.make(email='<EMAIL>')
        self.member.parents.add(self.parent)
        parent_bci = bci_recipe.make(
            user=self.parent.user_profile.user, first_name='parent', business=business
        )
        self.parent.bcis.add(parent_bci)
        active_bci_relation_recipe.make(parent_bci=parent_bci, member_bci=member_bci)

        self.inactive_child = inactive_member_recipe.make()
        self.member.members.add(self.inactive_child)
        inactive_bci = bci_no_user_recipe.make(
            first_name='inactive',
            last_name='child',
            business=business,
            cell_phone='',
            email='',
        )
        self.inactive_child.bcis.add(inactive_bci)
        inactive_bci_relation_recipe.make(parent_bci=member_bci, member_bci=inactive_bci)

        self.active_child = member_recipe.make(email='<EMAIL>')
        self.member.members.add(self.active_child)
        active_bci = bci_recipe.make(first_name='active', last_name='child', business=business)
        self.active_child.bcis.add(active_bci)
        active_bci_relation_recipe.make(parent_bci=member_bci, member_bci=active_bci)

    def create_appointments(self):
        self.create_bcis()
        member_bci = self.member.bcis.last()
        create_appointment_for_member(self.parent.bcis.first(), member_bci)
        create_appointment_for_member(member_bci, self.active_child.bcis.first())
        appointment = create_appointment_for_member(member_bci, self.inactive_child.bcis.first())
        traveling = AppointmentTraveling.objects.create(
            address_line_1='344 Clinton St',
            address_line_2='Apt 3B',
            apartment_number='3B',
            city='Metropolis',
            zipcode='00000',
            longitude=0.0,
            latitude=0.0,
        )
        appointment.traveling = traveling
        appointment.save()
        bci = bci_recipe.make(user=self.member.user_profile.user, first_name='current')
        create_appointment([{}, {}], business=member_bci.business, booked_for=bci)

    @pytest.mark.usefixtures('switch_on_new_login_fixture')
    @pytest.mark.patch_booksy_auth_sync(use=False)
    @patch('webapps.session.booksy_auth.BooksyAuthClient._make_request')
    @patch.object(IterableClient, 'forget_email_gdpr')
    @override_feature_flag({ForgetUserEmailGDPRFlag.flag_name: True})
    @patch('service.other.helper.CloudfrontS3Helper.clean_cloudfront_cache')
    def test_deleted_user_data(self, _mock_clean_patch, forget_email_mock, auth_mock):
        inactive_member = self.create_inactive_member()
        self.assertIsNotNone(self.member.photo)

        with self.captureOnCommitCallbacks(execute=True):
            resp = self.client.get(self.url_delete, follow=True)
        self.assertIn('deleted succesfully', resp.content.decode('utf-8'))

        self.customer.refresh_from_db()
        self.assertFalse(self.customer.cell_phone)
        self.assertTrue(self.customer.email.endswith(f'@{User.DELETED_USER_DOMAIN}'))
        self.assertTrue(self.customer.username.startswith('deleted_user_'))

        self.member.refresh_from_db()
        self.assertFalse(self.member.cell_phone)
        self.assertIsNone(self.member.photo)
        self.assertTrue(self.member.email.endswith(f'@{User.DELETED_USER_DOMAIN}'))
        self.assertEqual(0, self.member.parents.count())
        self.assertEqual(0, self.member.members.count())

        inactive_member.refresh_from_db()
        self.assertEqual('deleted', inactive_member.first_name)
        self.assertEqual(forget_email_mock.call_count, 1)

        self.assertEqual(len(auth_mock.call_args_list), 3)
        (args_0, _), (args_1, _), (args_2, _) = auth_mock.call_args_list
        self.assertEqual(args_0[0], 'delete_account')
        self.assertEqual(args_0[2], dict(country_user_id=self.customer.id))
        self.assertEqual(args_1[0], 'delete_account')
        self.assertEqual(args_1[2], dict(country_user_id=self.customer.id))
        self.assertEqual(args_2[0], 'delete_all_user_sessions')
        self.assertEqual(args_2[2], dict(country_user_id=self.customer.id))

    @patch.object(IterableClient, 'forget_email_gdpr')
    @override_feature_flag({ForgetUserEmailGDPRFlag.flag_name: False})
    @patch('service.other.helper.CloudfrontS3Helper.clean_cloudfront_cache')
    def test_delete_user_data_flag_disabled(self, _mock_clean_patch, forget_email_mock):
        self.create_inactive_member()
        with self.captureOnCommitCallbacks(execute=True):
            self.client.get(self.url_delete, follow=True)

        self.assertEqual(forget_email_mock.call_count, 0)

    @patch('service.other.helper.CloudfrontS3Helper.clean_cloudfront_cache')
    def test_notifications_for_deleted_user(self, _mock_clean_patch):
        self.create_notifications()
        with self.captureOnCommitCallbacks(execute=True):
            resp = self.client.get(self.url_delete, follow=True)
        self.assertIn('deleted succesfully', resp.content.decode('utf-8'))

        self.assertEqual(6, FamilyAndFriendsUnlinkNotification.all_objects.count())
        self.assertEqual(
            0,
            FamilyAndFriendsInvitationNotification.objects.filter(deleted__isnull=True).count(),
        )

        def assert_soft_deleted_invitation_with_status(invitation_status):
            self.assertEqual(
                1,
                FamilyAndFriendsInvitationResponseNotification.all_objects.filter(
                    deleted__isnull=False,
                    invitation_status=invitation_status,
                ).count(),
            )

        assert_soft_deleted_invitation_with_status(FamilyAndFriendsInvitationResponseType.REJECTED)
        assert_soft_deleted_invitation_with_status(FamilyAndFriendsInvitationResponseType.ACCEPTED)
        assert_soft_deleted_invitation_with_status(FamilyAndFriendsInvitationResponseType.EXPIRED)

    @patch('service.other.helper.CloudfrontS3Helper.clean_cloudfront_cache')
    def test_deleted_user_with_members_and_bcis(self, _mock_clean_patch):
        self.create_bcis()

        member_bci = self.member.bcis.first()
        child_bci = self.inactive_child.bcis.first()

        with self.captureOnCommitCallbacks(execute=True):
            self.client.get(self.url_delete, follow=True)
        self.assertEqual(8, FamilyAndFriendsUnlinkNotification.all_objects.count())

        self.inactive_child.refresh_from_db()
        member_bci.refresh_from_db()
        child_bci.refresh_from_db()

        member_bci_doc = member_bci.get_document(refresh=True)
        child_bci_doc = child_bci.get_document(refresh=True)

        self.assertIsNotNone(self.inactive_child.deleted)
        self.assertEqual(1, self.inactive_child.bcis.count())
        self.assertEqual(1, member_bci.members.count())
        self.assertEqual(0, member_bci.parents.count())
        self.assertEqual(f'Deleted user [{child_bci.id}]', child_bci.full_name)
        self.assertEqual('', child_bci.email)
        self.assertEqual('', child_bci.cell_phone)
        self.assertEqual(0, child_bci.members.count())
        self.assertEqual(1, child_bci.parents.count())
        self.assertFalse(child_bci.visible_in_business)
        self.assertIsNotNone(child_bci.deleted)

        self.assertTrue(
            any(
                i.email.endswith(f'@{User.DELETED_USER_DOMAIN}')
                for i in member_bci_doc.family_and_friends
            )
        )
        self.assertIsNone(child_bci_doc.customer_profile)
        self.assertIn(f'@{User.DELETED_USER_DOMAIN}', child_bci_doc.business_customer.email)
        self.assertEqual('', child_bci_doc.business_customer.cell_phone)
        self.assertEqual(f'Deleted user [{child_bci_doc.id}]', child_bci_doc.merged_data.full_name)

    @patch('service.other.helper.CloudfrontS3Helper.clean_cloudfront_cache')
    def test_deleted_user_with_bci_relation_without_members(self, _mock_clean_patch):
        self.create_bcis()  # add bcis in two different businesses
        self.create_bcis()

        # after unlinking members, the relation between parent-member cards in business still exists
        inactive_bci = bci_no_user_recipe.make(email='<EMAIL>')
        inactive_member_recipe.make(bcis=[inactive_bci])
        active_bci = bci_recipe.make(email='<EMAIL>')
        member_recipe.make(bcis=[active_bci])
        customer_bci = self.member.bcis.first()
        customer_bci.members.add(inactive_bci)
        customer_bci.members.add(active_bci)
        self.assertEqual(4, customer_bci.members.count())

        with self.captureOnCommitCallbacks(execute=True):
            self.client.get(self.url_delete, follow=True)
        customer_bci.refresh_from_db()
        inactive_bci.refresh_from_db()
        active_bci.refresh_from_db()
        self.assertEqual(2, customer_bci.members.count())
        self.assertFalse(inactive_bci.visible_in_business)
        self.assertTrue(active_bci.visible_in_business)

    @patch('service.other.helper.CloudfrontS3Helper.clean_cloudfront_cache')
    def test_deleted_user_with_members_appointments(self, _mock_clean_patch):
        self.create_bcis()
        customer_bci = self.member.bcis.first()
        inactive_bci = self.inactive_child.bcis.first()
        create_appointment_for_member(customer_bci, inactive_bci)
        MemberRelations.objects.get(parent=self.member, member=self.inactive_child).delete()

        with self.captureOnCommitCallbacks(execute=True):
            self.client.get(self.url_delete, follow=True)
        inactive_bci.refresh_from_db()
        self.assertFalse(inactive_bci.visible_in_business)
        self.assertEqual(None, inactive_bci.appointments.first().customer_name)

    @patch('service.other.helper.CloudfrontS3Helper.clean_cloudfront_cache')
    def test_deleted_user_with_members_and_appointments(self, _mock_clean_patch):
        self.create_appointments()

        customer_bci = self.member.bcis.first()
        customer_bci_doc = customer_bci.get_document(refresh=True)

        child_bci = self.inactive_child.bcis.first()
        child_bci_doc = child_bci.get_document(refresh=True)
        child_appointment = child_bci.appointments.first()

        self.assertEqual('', child_appointment.customer_phone)
        self.assertEqual(None, child_appointment.customer_email)

        self.assertEqual(
            customer_bci_doc.merged_data.cell_phone, child_bci_doc.merged_data.cell_phone
        )
        self.assertEqual(customer_bci_doc.merged_data.email, child_bci_doc.merged_data.email)
        self.assertEqual('inactive child', child_bci_doc.merged_data.full_name)

        with self.captureOnCommitCallbacks(execute=True):
            self.client.get(self.url_delete, follow=True)

        child_appointment.refresh_from_db()
        self.assertEqual(None, child_appointment.customer_name)
        self.assertEqual('', child_appointment.customer_phone)
        self.assertIsNone(child_appointment.traveling.longitude)
        self.assertIsNone(child_appointment.traveling.latitude)
        self.assertEqual(
            '9dab41511689c228e1d98250aced5eaa9ee6ac805975144bcd68006a1748d71b',
            child_appointment.traveling.address_line_1,
        )
        self.assertEqual(
            '6f697c2e94dfe1714be330d1bd8b24ebe617bb4f8ca23b0894059a09c4fc86ed',
            child_appointment.traveling.address_line_2,
        )
        self.assertEqual('', child_appointment.traveling.apartment_number)
        self.assertEqual('', child_appointment.traveling.city)
        self.assertEqual('', child_appointment.traveling.zipcode)

    @patch('service.other.helper.CloudfrontS3Helper.clean_cloudfront_cache')
    def test_delete_vehicle(self, _mock_clean_patch):
        member_bci, business = self.create_member_bci()
        vehicle_bci = vehicle_member_bci_recipe.make(business=business)
        vehicle = inactive_member_recipe.make(
            bcis=[vehicle_bci],
            additional_data={
                'registration_number': 'BMT 216A',
                'manufacturer': 'Aston Martin',
                'model': 'DB5',
                'year': 1964,
                'vin_number': 'DP5/216/1',
                'additional_info': 'from the older one',
            },
        )
        add_child(self.member, vehicle, RelationshipType.VEHICLE)
        member_bci.members.add(
            vehicle_bci, through_defaults={'relationship_type': RelationshipType.VEHICLE}
        )
        with self.captureOnCommitCallbacks(execute=True):
            self.client.get(self.url_delete, follow=True)

        vehicle.refresh_from_db()
        self.assertSetEqual({'registration_number'}, set(vehicle.additional_data.keys()))

        BusinessCustomerDocument.reindex([vehicle_bci.id])
        vehicle_bci_doc: BusinessCustomerDocument = vehicle_bci.get_document(refresh=True)
        vehicle_merged_data = vehicle_bci_doc.merged_data
        self.assertIn('deleted', vehicle_merged_data.full_name)

        returned_additional_data = vehicle_merged_data.type_data.additional_data
        self.assertEqual({'registration_number': mock.ANY}, returned_additional_data)
        self.assertIn('_deleted_vehicle', returned_additional_data['registration_number'])

    @patch('service.other.helper.CloudfrontS3Helper.clean_cloudfront_cache')
    def test_deleted_user_with_members_transactions(self, _mock_clean_patch):
        self.create_bcis()
        inactive_bci = self.inactive_child.bcis.first()
        active_bci = self.active_child.bcis.first()
        inactive_transaction = transaction_recipe.make(
            customer_card=inactive_bci, customer_data='inactive child, parent phone, parent email'
        )
        active_transaction = transaction_recipe.make(
            customer_card=active_bci, customer_data='active child, phone, email'
        )

        with self.captureOnCommitCallbacks(execute=True):
            self.client.get(self.url_delete, follow=True)
        inactive_transaction.refresh_from_db()
        active_transaction.refresh_from_db()
        self.assertEqual(inactive_transaction.customer_data, '')
        self.assertEqual(active_transaction.customer_data, 'active child, phone, email')

    @staticmethod
    def mocked_file(filename):
        return SimpleUploadedFile(filename, b'file content')

    @patch.object(consents_storage, '_save', mock_storage_save)
    @patch('service.other.helper.CloudfrontS3Helper.clean_cloudfront_cache')
    def test_deleted_user_with_members_consents(self, _mock_clean_patch):
        self.create_bcis()
        inactive_bci = self.inactive_child.bcis.first()
        active_bci = self.active_child.bcis.first()
        consent_data = {
            'customer_full_name': 'Edwin Jarvis',
            'customer_cell_phone': '****** 111 111',
            'customer_address': '890 Fifth Avenue, Manhattan, New York City, NY, USA',
        }
        inactive_consent = baker.make(
            Consent,
            customer=inactive_bci,
            **consent_data,
        )
        active_consent = baker.make(
            Consent,
            customer=active_bci,
            **consent_data,
        )
        inactive_consent.signature = self.mocked_file('signature.png')
        inactive_consent.sign()
        active_consent.signature = self.mocked_file('signature.png')
        active_consent.sign()
        with self.captureOnCommitCallbacks(execute=True):
            self.client.get(self.url_delete, follow=True)
        inactive_consent.refresh_from_db()
        active_consent.refresh_from_db()
        self.assertEqual(
            inactive_consent.customer_full_name,
            '4aacace313a553e1a361883ca52c5d911566675dee5e7e3d72f29ccb8e9a4271',
        )
        self.assertEqual(active_consent.customer_full_name, 'Edwin Jarvis')
        self.assertEqual(
            inactive_consent.customer_address,
            'b43f1190001008b23b75f4feab52b61af29c5ccdf4b97009964d8d693fb732c4',
        )
        self.assertEqual(
            active_consent.customer_address, '890 Fifth Avenue, Manhattan, New York City, NY, USA'
        )
        self.assertEqual(inactive_consent.customer_cell_phone, '')
        self.assertEqual(active_consent.customer_cell_phone, '****** 111 111')
