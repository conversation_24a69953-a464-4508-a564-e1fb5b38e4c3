from datetime import timedelta
from unittest import TestCase

import pytest

from lib.tools import tznow

from webapps.boost.baker_recipes import boosted_business_recipe
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.family_and_friends.baker_recipes import (
    invitation_recipe,
    member_recipe,
    relation_recipe,
)
from webapps.family_and_friends.enums import InvitationStatus, RelationshipType
from webapps.family_and_friends.helpers.booking import (
    change_family_and_friends_client_type_if_needed,
)
from webapps.family_and_friends.helpers.profiles import get_contact_from_confirmed_member
from webapps.family_and_friends.tests.utils import TestFamilyAndFriendsMixin
from webapps.marketplace.models import BoostClientCard
from webapps.user.baker_recipes import customer_user


@pytest.mark.django_db
class TestGetContactToBCIFromMemberProfile(TestCase):
    def setUp(self) -> None:
        self.member = member_recipe.make(
            user_profile=None, email='<EMAIL>', cell_phone='+**************'
        )
        self.parent = member_recipe.make(email='<EMAIL>', cell_phone='+**************')

    def test_data_from_booksy_user(self):
        user = customer_user.make(email='<EMAIL>', cell_phone='+48 333 444 555')
        member = member_recipe.make(
            email='<EMAIL>',
            cell_phone='+**************',
            user_profile=user.customer_profile,
        )
        relation_recipe.make(member=member, parent=self.parent)
        cell_phone, email = get_contact_from_confirmed_member(member)
        assert cell_phone == ''
        assert email == ''

    def test_data_from_confirmed_booksy_user(self):
        user = customer_user.make(email='<EMAIL>', cell_phone='+48 333 444 555')
        member = member_recipe.make(
            email='<EMAIL>',
            cell_phone='+**************',
            user_profile=user.customer_profile,
        )
        relation_recipe.make(member=member, parent=self.parent)
        invitation_recipe.make(member=member, parent=self.parent, status=InvitationStatus.ACCEPTED)
        cell_phone, email = get_contact_from_confirmed_member(member)
        assert email == user.email
        assert cell_phone == user.cell_phone

    def test_data_from_inactive_member(self):
        relation_recipe.make(
            member=self.member, parent=self.parent, relationship_type=RelationshipType.PET
        )
        cell_phone, email = get_contact_from_confirmed_member(self.member)
        assert cell_phone == ''
        assert email == ''

    def test_data_from_member_with_not_accepted_invite(self):
        relation_recipe.make(member=self.member, parent=self.parent)
        invitation_recipe.make(member=self.member, parent=self.parent)
        cell_phone, email = get_contact_from_confirmed_member(self.member)
        assert cell_phone == ''
        assert email == ''


class TestChangeFamilyAndFriendsClientTypeIfNeeded(TestFamilyAndFriendsMixin):
    def test_for_next_inactive_customer(self):
        parent_bci, member_bci = self.create_inactive_member_bcis()
        appointment = self.create_appointment(parent_bci, member_bci)
        assert (
            appointment.booked_for.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        assert (
            appointment.booked_by.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        change_family_and_friends_client_type_if_needed(appointment)
        assert (
            appointment.booked_for.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        assert (
            appointment.booked_by.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        assert not BoostClientCard.objects.exists()

    def test_for_new_inactive_customer(self):
        business = boosted_business_recipe.make()
        parent_bci, member_bci = self.create_inactive_member_bcis(business=business)
        appointment = self.create_appointment(parent_bci, member_bci)
        appointment.booked_for.client_type = BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
        appointment.booked_for.first_appointment = appointment
        appointment.booked_for.save()
        appointment.booked_by.first_appointment = appointment
        appointment.booked_by.save()
        change_family_and_friends_client_type_if_needed(appointment)
        assert (
            appointment.booked_for.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        assert appointment.booked_by.client_type == BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW

    def test_for_new_inactive_customer_not_first_visit(self):
        parent_bci, member_bci = self.create_inactive_member_bcis()
        first_appointment = self.create_appointment(parent_bci, member_bci)
        appointment = self.create_appointment(parent_bci, member_bci)
        appointment.booked_for.client_type = BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
        appointment.booked_for.first_appointment = first_appointment
        appointment.booked_for.save()
        appointment.booked_by.save()
        change_family_and_friends_client_type_if_needed(appointment)
        assert appointment.booked_for.client_type == BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
        assert (
            appointment.booked_by.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        assert not BoostClientCard.objects.exists()

    def test_for_next_active_customer(self):
        parent_bci, member_bci = self.create_active_members_bcis()
        appointment = self.create_appointment(parent_bci, member_bci)
        assert (
            appointment.booked_for.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        assert (
            appointment.booked_by.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        change_family_and_friends_client_type_if_needed(appointment)
        assert (
            appointment.booked_for.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        assert (
            appointment.booked_by.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        assert not BoostClientCard.objects.exists()

    def test_for_new_active_customer(self):
        business = boosted_business_recipe.make()
        parent_bci, member_bci = self.create_active_members_bcis(business=business)
        appointment = self.create_appointment(parent_bci, member_bci)
        appointment.booked_for.client_type = BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
        appointment.booked_for.first_appointment = appointment
        appointment.booked_for.save()
        appointment.booked_by.first_appointment = appointment
        appointment.booked_by.save()
        change_family_and_friends_client_type_if_needed(appointment)
        assert (
            appointment.booked_for.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        assert appointment.booked_by.client_type == BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW

    def test_for_new_active_customer_not_first_visit(self):
        parent_bci, member_bci = self.create_active_members_bcis()
        first_appointment = self.create_appointment(parent_bci, member_bci)
        appointment = self.create_appointment(parent_bci, member_bci)
        appointment.booked_for.client_type = BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
        appointment.booked_for.first_appointment = first_appointment
        appointment.booked_for.save()
        appointment.booked_by.save()
        change_family_and_friends_client_type_if_needed(appointment)
        assert appointment.booked_for.client_type == BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
        assert (
            appointment.booked_by.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        assert not BoostClientCard.objects.exists()

    def test_for_new_active_customer_to_another_active(self):
        business = boosted_business_recipe.make()
        parent_bci, member_bci = self.create_active_members_bcis(business=business)
        appointment = self.create_appointment(parent_bci, member_bci)
        appointment.booked_for.client_type = BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
        appointment.booked_for.first_appointment = appointment
        appointment.booked_for.save()
        appointment.booked_by.first_appointment = appointment
        appointment.booked_by.save()
        parent_bci, another_member_bci = self.create_active_members_bcis(
            business=parent_bci.business, parent=parent_bci.memberprofile_set.first()
        )
        another_appointment = self.create_appointment(parent_bci, another_member_bci)
        another_member_bci.first_appointment = another_appointment
        another_member_bci.save()
        another_appointment.updated = tznow() - timedelta(days=5)
        another_appointment.save()
        change_family_and_friends_client_type_if_needed(appointment)
        assert (
            appointment.booked_for.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        assert (
            appointment.booked_by.client_type
            == BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS
        )
        another_member_bci.refresh_from_db()
        assert another_member_bci.client_type == BusinessCustomerInfo.CLIENT_TYPE__CUSTOMER_NEW
