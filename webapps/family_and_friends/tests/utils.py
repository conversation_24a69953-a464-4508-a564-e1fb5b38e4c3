import datetime
import random
from datetime import date, timedelta
from typing import NoReturn, Union, Optional

from django.test import TestCase
from model_bakery import baker
from model_bakery.random_gen import gen_email

from lib.baker_utils import get_or_create_booking_source
from lib.tools import datetimeinfinity, tznow
from webapps.booking.models import Appointment, PriceType, ServiceVariant
from webapps.booking.tests.utils import create_appointment
from webapps.booking.baker_recipes import booking_source_recipe
from webapps.business.baker_recipes import business_recipe
from webapps.business.models import ServiceVariantPayment
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.family_and_friends.baker_recipes import (
    invitation_recipe,
    mbci_recipe,
    member_appointment_recipe,
    member_recipe,
    relation_recipe,
)
from webapps.family_and_friends.enums import InvitationStatus, RelationshipType
from webapps.family_and_friends.factory import (
    make_bci_for_booksy_user,
    make_bci_for_not_booksy_user,
    create_relation_of_bcis,
)
from webapps.family_and_friends.helpers.invitation import send_invite_to_user
from webapps.family_and_friends.models import (
    MemberProfile,
    MemberBusinessCustomerInfo,
)
from webapps.user.models import UserProfile


class TestFamilyAndFriendsMixin(TestCase):
    @staticmethod
    def create_child():
        return member_recipe.make(
            first_name="Paweł",
            last_name="Jankowski",
            user_profile=None,
            email=None,
            cell_phone=None,
        )

    @staticmethod
    def create_pet():
        return member_recipe.make(
            first_name="Lejek",
            user_profile=None,
            email=None,
            cell_phone=None,
            additional_data={
                'breed': 'Pudel',
                'pet_type': 'piesek',
                'weight': 10,
                'additional_info': 'incydent z kulturą',
            },
        )

    @staticmethod
    def create_parent():
        member = member_recipe.make(
            first_name="Zofia",
            last_name="Jankowska",
            email=gen_email(),
            cell_phone="+**************",
        )
        TestFamilyAndFriendsMixin._copy_to_user(member)
        return member

    @staticmethod
    def create_booksy():
        member = member_recipe.make(
            first_name="Irena",
            last_name="Kamińska",
            email=gen_email(),
            cell_phone="+48 222 333 444",
        )
        TestFamilyAndFriendsMixin._copy_to_user(member)
        return member

    @staticmethod
    def _copy_to_user(member):
        user = member.user_profile.user
        user.first_name = member.first_name
        user.last_name = member.last_name
        user.email = member.email
        user.cell_phone = member.cell_phone
        user.save()

    @staticmethod
    def create_not_booksy():
        return member_recipe.make(
            first_name="Kazimierz",
            last_name="Jankowski",
            email=gen_email(),
            cell_phone="+48 333 444 555",
            user_profile=None,
        )

    @classmethod
    def create_inactive_member(cls, parent=None):
        relation = relation_recipe.make(
            member=cls.create_child(),
            parent=parent or cls.create_parent(),
            relationship_type=RelationshipType.CHILD,
        )
        return relation.parent, relation.member

    @classmethod
    def create_pet_member(cls, parent=None):
        relation = relation_recipe.make(
            member=cls.create_pet(),
            parent=parent or cls.create_parent(),
            relationship_type=RelationshipType.PET,
        )
        return relation.parent, relation.member

    @classmethod
    def create_confirmed_booksy(cls, parent=None):
        relation = relation_recipe.make(
            member=cls.create_booksy(), parent=parent or cls.create_parent()
        )
        invitation_recipe.make(
            member=relation.member, parent=relation.parent, status=InvitationStatus.ACCEPTED
        )
        return relation.parent, relation.member

    @classmethod
    def create_not_confirmed_booksy(cls, parent=None):
        relation = relation_recipe.make(
            member=cls.create_booksy(), parent=parent or cls.create_parent()
        )
        invitation_recipe.make(
            member=relation.member,
            parent=relation.parent,
        )
        return relation.parent, relation.member

    @classmethod
    def create_confirmed_not_booksy(cls, parent=None):
        relation = relation_recipe.make(
            member=cls.create_not_booksy(),
            parent=parent or cls.create_parent(),
            relationship_type=RelationshipType.SPOUSE,
        )
        invitation_recipe.make(
            member=relation.member, parent=relation.parent, status=InvitationStatus.ACCEPTED
        )
        return relation.parent, relation.member

    @classmethod
    def create_not_confirmed_not_booksy(cls, parent=None):
        relation = relation_recipe.make(
            member=cls.create_not_booksy(),
            parent=parent or cls.create_parent(),
            relationship_type=RelationshipType.SPOUSE,
        )
        invitation_recipe.make(member=relation.member, parent=relation.parent)
        return relation.parent, relation.member

    @classmethod
    def create_bci(cls, member, business=None, parent=None):
        business = business or business_recipe.make()
        defaults = {
            'client_type': BusinessCustomerInfo.CLIENT_TYPE__FAMILY_AND_FRIENDS,
        }
        if member.user_profile:
            member_bci = make_bci_for_booksy_user(
                member, business, booking_source_recipe.make(), defaults
            )
        else:
            parent_user = parent.user_profile.user if parent else None
            member_bci = make_bci_for_not_booksy_user(member, business, defaults, parent_user)
        mbci_recipe.make(member=member, bci=member_bci)
        return member_bci

    @classmethod
    def create_inactive_member_bcis(cls, business=None, parent=None, member=None):
        if not member or not parent:
            parent, member = cls.create_inactive_member(parent)
        member_bci = cls.create_bci(member, business, parent)
        parent_bci = cls.create_bci(parent, business)
        create_relation_of_bcis(parent_bci, member_bci, member, parent_bci.user)
        return parent_bci, member_bci

    @classmethod
    def create_active_members_bcis(cls, business=None, parent=None):
        parent, member = cls.create_confirmed_booksy(parent)
        member_bci = cls.create_bci(member, business, parent)
        parent_bci = cls.create_bci(parent, business)
        create_relation_of_bcis(parent_bci, member_bci, member, parent_bci.user)
        return parent_bci, member_bci

    @classmethod
    def create_not_confirmed_booksy_bci(cls, business=None, parent=None):
        parent, member = cls.create_not_confirmed_booksy(parent)
        member_bci = cls.create_bci(member, business, parent)
        parent_bci = cls.create_bci(parent, business)
        create_relation_of_bcis(parent_bci, member_bci, member, parent_bci.user)
        return parent_bci, member_bci

    @classmethod
    def create_confirmed_not_booksy_bci(cls, business=None, parent=None):
        parent, member = cls.create_confirmed_not_booksy(parent)
        member_bci = cls.create_bci(member, business, parent)
        parent_bci = cls.create_bci(parent, business)
        create_relation_of_bcis(parent_bci, member_bci, member, parent_bci.user)
        return parent_bci, member_bci

    @classmethod
    def create_not_confirmed_not_booksy_bci(cls, business=None, parent=None):
        parent, member = cls.create_not_confirmed_not_booksy(parent)
        member_bci = cls.create_bci(member, business, parent)
        parent_bci = cls.create_bci(parent, business)
        create_relation_of_bcis(parent_bci, member_bci, member, parent_bci.user)
        return parent_bci, member_bci

    @classmethod
    def create_appointment(cls, parent_bci, member_bci) -> Appointment:
        appointment = create_appointment(
            [{}, {}],
            business=parent_bci.business,
            type=Appointment.TYPE.CUSTOMER,
            source=get_or_create_booking_source(chargeable=True),
        )
        appointment.booked_for = member_bci
        appointment.save()
        member_appointment_recipe.make(
            booked_for=member_bci, booked_by=parent_bci, appointment=appointment
        )
        return appointment


def add_child(
    parent: MemberProfile,
    child: MemberProfile,
    relation: RelationshipType = RelationshipType.FRIEND,
) -> None:
    parent.members.add(child, through_defaults={'relationship_type': relation})


def send_invite(parent: MemberProfile, child: MemberProfile, user_profile: UserProfile = None):
    invitation = invitation_recipe.make(parent=parent, member=child)
    send_invite_to_user(invitation, user_profile or child.user_profile)
    return invitation


def invite_new_member(  # pylint: disable=too-many-arguments
    *,
    parent_user: Union[MemberProfile, UserProfile],
    relationship_type: str,
    first_name: str,
    last_name: Optional[str] = None,
    cell_phone: Optional[str] = None,
    email: Optional[str] = None,
    birthday: Optional[date] = None,
    status: str = InvitationStatus.AWAITING,
    valid_till: datetime = datetimeinfinity(),
) -> NoReturn:
    user_data = {
        'first_name': first_name,
        'last_name': last_name,
        'cell_phone': cell_phone,
        'email': email,
        'birthday': birthday,
    }
    if not isinstance(parent_user, MemberProfile):
        parent_user: MemberProfile = MemberProfile.from_user_profile(parent_user)
    child = baker.make(MemberProfile, **{k: v for k, v in user_data.items() if v is not None})
    invitation = invitation_recipe.make(
        member=child, parent=parent_user, status=status, valid_till=valid_till
    )
    parent_user.members.add(child, through_defaults={'relationship_type': relationship_type})
    return invitation


# pylint: disable=R0913, R0917
def create_appointment_for_member(
    parent_bci: BusinessCustomerInfo,
    member_bci: BusinessCustomerInfo,
    parent_profile: MemberProfile = None,
    member_profile: MemberProfile = None,
    past_appointment: bool = False,
    appointment_status: str = None,
) -> Appointment:
    parent_bci.members.add(member_bci)
    if not MemberBusinessCustomerInfo.objects.filter(bci=parent_bci).exists() and parent_profile:
        mbci_recipe.make(member=parent_profile, bci=parent_bci)
    if not MemberBusinessCustomerInfo.objects.filter(bci=member_bci).exists() and member_profile:
        mbci_recipe.make(member=member_profile, bci=member_bci)
    member_profile_data = {
        'customer_name': member_bci.full_name,
        'customer_phone': member_bci.cell_phone,
        'customer_email': member_bci.email,
    }
    member_profile_data = {k: v for k, v in member_profile_data.items() if v}
    if past_appointment:
        booked_from = tznow() - timedelta(days=random.randint(1, 3))
    else:
        booked_from = tznow() + timedelta(hours=random.randint(24, 72))

    service_variant = baker.make(
        ServiceVariant,
        type=PriceType.FIXED,
        payment=baker.make(ServiceVariantPayment),
        price=20,
    )
    appointment = create_appointment(
        [
            {
                'service_variant': service_variant,
                'booked_from': booked_from,
                'booked_till': booked_from + timedelta(hours=4),
            }
        ],
        business=parent_bci.business,
        type=Appointment.TYPE.CUSTOMER,
        status=appointment_status or Appointment.STATUS.ACCEPTED,
        booked_for=member_bci,
        **member_profile_data,
    )
    appointment.save()
    member_appointment_recipe.make(
        booked_for=member_bci, booked_by=parent_bci, appointment=appointment
    )
    return appointment
