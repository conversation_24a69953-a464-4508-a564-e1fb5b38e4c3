from collections import defaultdict
from django.contrib import admin
from django.db.models.aggregates import Avg
import re

from lib.admin_helpers import (
    BaseModelAdmin,
    NoAddDelMixin,
    RemovalCandidateMixin,
)
from webapps import consts
from webapps.admin_extra.tools import export_as_csv_action
from webapps.booking.models import BookingSources

from webapps.feedback.models import Feedback

f_regexp = re.compile(r"Fingerprint: ((\d+).){4}, (\S+)")


def average(numbers):
    return float(sum(numbers)) / max(len(numbers), 1)


def get_source_type(feedbacks):
    return {i['source__name'] for i in feedbacks}


def count_stars(feedbacks):
    ret = {key: 0 for key in range(1, 6)}
    for i in feedbacks:
        ret[i] += 1
    return ret


class FeedbackAdmin(RemovalCandidateMixin, NoAddDelMixin, BaseModelAdmin):
    list_display = ['id', 'user', 'created', 'feedback_text', 'score_value']
    search_fields = ('created', 'score_value')
    list_filter = ('score_value',)

    readonly_fields = [
        'id',
        'created',
        'feedback_text',
        'score_value',
        'source',
        'language',
        'x_version',
        'user_agent',
        'x_fingerprint',
        'ip',
    ]

    fields = ['user'] + readonly_fields

    raw_id_fields = ['user']

    actions_on_bottom = False
    actions_on_top = True
    actions = [
        export_as_csv_action(
            fields=list_display,
            header=True,
            force_fields=True,
        ),
    ]

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('user')


admin.site.register(Feedback, FeedbackAdmin)
