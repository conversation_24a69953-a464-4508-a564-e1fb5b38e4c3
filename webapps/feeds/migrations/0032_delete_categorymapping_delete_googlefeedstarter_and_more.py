# Generated by Django 4.2.18 on 2025-06-16 15:28

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('feeds', '0031_remove_yelpbooking_booking_delete_yelpfeedstatus_and_more'),
    ]

    operations = [
        migrations.SeparateDatabaseAndState(
            database_operations=[],
            state_operations=[
                migrations.DeleteModel(
                    name='CategoryMapping',
                ),
                migrations.DeleteModel(
                    name='GoogleFeedStarter',
                ),
                migrations.DeleteModel(
                    name='GoogleFeedStatus',
                ),
            ],
        ),
    ]
