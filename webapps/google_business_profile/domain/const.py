# pylint: disable=line-too-long
import enum

VERIFICATION_PENDING = "PENDING"
VERIFICATION_FAILED = "FAILED"


class StrEnum(str, enum.Enum):
    def __str__(self):
        """Serialize by value for easy conversion."""
        return self.value


class OpenInfoStatus(StrEnum):
    OPEN_INFO_STATUS_UNSPECIFIED = "OPEN_INFO_STATUS_UNSPECIFIED"
    OPEN = "OPEN"
    CLOSED_PERMANENTLY = "CLOSED_PERMANENTLY"
    CLOSED_TEMPORARILY = "CLOSED_TEMPORARILY"


class InternalCategory(StrEnum):
    HAIR_SALONS = "Hair salons"
    BRAIDS = "Braids"
    HAIR_REMOVAL = "Hair Removal"
    MAKEUP = "Make-up"
    WEDDING_MAKEUP = "Wedding Makeup Artist"
    BARBERS = "Barbers"
    BEAUTY_SALON = "Beauty salon"
    BROWS_LASHES = "Brows & Lashes"
    TATTOO = "Tattoo artists"
    NAIL_SALONS = "Nail salons"
    AESTHETIC_MEDICINE = "Aesthetic Medicine"
    DAY_SPA = "Day SPA"
    SKIN_CARE = "Skin care"
    HEALTH = "Health"
    DENTAL = "Dental"
    HOLISTIC_MEDICINE = "Holistic Medicine"
    PHYSICAL_THERAPY = "Physical Therapy"
    PERSONAL_TRAINERS = "Personal trainers"
    YOGA = "Yoga"
    MASSAGE = "Massage"
    OTHER = "Other"

    @classmethod
    def from_report_name(cls, report_name: str) -> 'InternalCategory':
        for category in cls:
            if category.value == report_name:
                return category
        return cls.OTHER


class GoogleCategory(StrEnum):
    HAIR_SALON = "Hair Salon"
    LASER_HAIR_REMOVAL = "Laser Hair Removal"
    MAKE_UP = "Make Up"
    BARBER_SHOP = "Barber Shop"
    BEAUTY_SALON = "Beauty Salon"  # Default fallback
    TATTOO_SALON = "Tattoo Salon"
    NAIL_SALON = "Nail Salon"
    MED_SPA = "Med_Spa"
    DOCTOR = "Doctor"
    DENTIST = "Dentist"
    ALTERNATIVE_HEALTH = "Alternative Health Practitioner"
    PHYSICAL_THERAPIST = "Physical Therapist"
    PERSONAL_TRAINER = "Personal Trainer"
    YOGA_STUDIO = "Yoga Studio"
    MASSAGE_THERAPIST = "Massage Therapist"
    DAY_SPA = "Day Spa"


class GoogleCategoryName(StrEnum):
    HAIR_SALON = "categories/gcid:hair_salon"
    LASER_HAIR_REMOVAL = "categories/gcid:laser_hair_removal_service"
    MAKE_UP = "categories/gcid:makeup_artist"
    BARBER_SHOP = "categories/gcid:barber_shop"
    BEAUTY_SALON = "categories/gcid:beauty_salon"
    TATTOO_SALON = "categories/gcid:tattoo_shop"
    NAIL_SALON = "categories/gcid:nail_salon"
    MED_SPA = "categories/gcid:medical_spa"
    DOCTOR = "categories/gcid:doctor"
    DENTIST = "categories/gcid:dentist"
    ALTERNATIVE_HEALTH = "categories/gcid:alternative_medicine_practitioner"
    PHYSICAL_THERAPIST = "categories/gcid:physiotherapist"
    PERSONAL_TRAINER = "categories/gcid:personal_trainer"
    YOGA_STUDIO = "categories/gcid:yoga_studio"
    MASSAGE_THERAPIST = "categories/gcid:massage_therapist"
    DAY_SPA = "categories/gcid:day_spa"

    @classmethod
    def has_value(cls, value: str) -> bool:
        return value in [
            getattr(cls, attr)
            for attr in dir(cls)
            if not attr.startswith('__') and not callable(getattr(cls, attr))
        ]


class GoogleVerificationStatus(StrEnum):
    COMPLETED = "Completed"
    PENDING = "Pending"
    FAILED = "Failed"
    UNVERIFIED = "Unverified"


class VerificationMethod(StrEnum):
    ADDRESS = "ADDRESS"
    EMAIL = "EMAIL"
    PHONE_CALL = "PHONE_CALL"
    SMS = "SMS"
    AUTO = "AUTO"
    VETTED_PARTNER = "VETTED_PARTNER"


class AddressCompareResult(StrEnum):
    MATCH = "Match"
    SIMILAR = "Similar"
    DIFFERENT = "Different"


class GoogleMapsAddressComponent(StrEnum):
    # https://developers.google.com/maps/documentation/geocoding/requests-geocoding#address-types
    STREET_NUMBER = "street_number"
    ROUTE = "route"
    LOCALITY = "locality"
    ADMINISTRATIVE_AREA_LEVEL_1 = "administrative_area_level_1"
    ADMINISTRATIVE_AREA_LEVEL_2 = "administrative_area_level_2"
    POSTAL_CODE = "postal_code"
    COUNTRY = "country"
    PREMISE = "premise"
    FLOOR = "floor"
    SUBLOCALITY = "sublocality"
    ROOM = "room"


class GoogleMapsAddressComponentPresentation(StrEnum):
    STREET_NUMBER = "Street number"
    ROUTE = "Route"
    LOCALITY = "Locality"
    ADMINISTRATIVE_AREA_LEVEL_1 = "Administrative area"
    ADMINISTRATIVE_AREA_LEVEL_2 = "Administrative area"
    POSTAL_CODE = "Postal code"
    COUNTRY = "Country"
    PREMISE = "Premise"
    FLOOR = "Floor"
    SUBLOCALITY = "Sublocality"
    ROOM = "Room"


GOOGLE_MAPS_ADDRESS_COMPONENT_PRESENTATION: dict[
    GoogleMapsAddressComponent, GoogleMapsAddressComponentPresentation
] = {
    GoogleMapsAddressComponent.STREET_NUMBER: GoogleMapsAddressComponentPresentation.STREET_NUMBER,
    GoogleMapsAddressComponent.ROUTE: GoogleMapsAddressComponentPresentation.ROUTE,
    GoogleMapsAddressComponent.LOCALITY: GoogleMapsAddressComponentPresentation.LOCALITY,
    GoogleMapsAddressComponent.ADMINISTRATIVE_AREA_LEVEL_1: GoogleMapsAddressComponentPresentation.ADMINISTRATIVE_AREA_LEVEL_1,
    GoogleMapsAddressComponent.ADMINISTRATIVE_AREA_LEVEL_2: GoogleMapsAddressComponentPresentation.ADMINISTRATIVE_AREA_LEVEL_2,
    GoogleMapsAddressComponent.POSTAL_CODE: GoogleMapsAddressComponentPresentation.POSTAL_CODE,
    GoogleMapsAddressComponent.COUNTRY: GoogleMapsAddressComponentPresentation.COUNTRY,
    GoogleMapsAddressComponent.PREMISE: GoogleMapsAddressComponentPresentation.PREMISE,
    GoogleMapsAddressComponent.FLOOR: GoogleMapsAddressComponentPresentation.FLOOR,
    GoogleMapsAddressComponent.SUBLOCALITY: GoogleMapsAddressComponentPresentation.SUBLOCALITY,
    GoogleMapsAddressComponent.ROOM: GoogleMapsAddressComponentPresentation.ROOM,
}


class GoogleMapsAddressGranularityValues(StrEnum):
    PREMISE = "PREMISE"
    SUB_PREMISE = "SUB_PREMISE"
    PREMISE_PROXIMITY = "PREMISE_PROXIMITY"


SEO_URL = ''


class Country(StrEnum):
    US = "us"


POSTAL_CODE_REGEXES = {
    Country.US: r"^[0-9]{5}(-[0-9]{4})?$",  # 12345 or 12345-6789
}
