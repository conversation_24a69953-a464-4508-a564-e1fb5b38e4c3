import abc

from webapps.google_business_profile.domain.dtos import VerificationOptionsDTO
from webapps.google_business_profile.domain.value_types import LanguageCode
from webapps.google_business_profile.shared import LocationId


class GoogleLocationVerificationGateway(abc.ABC):
    @abc.abstractmethod
    def fetch_verification_options(self, language_code: LanguageCode) -> VerificationOptionsDTO: ...

    @abc.abstractmethod
    def verify_location(self, language_code: LanguageCode, location_id: LocationId) -> None: ...

    @abc.abstractmethod
    def complete_location_verification(self) -> None: ...
