from unittest import TestCase
from unittest.mock import patch

from webapps.google_business_profile.domain.services.postal_code import PostalCodeService
from webapps.google_business_profile.domain.value_types import PostalCode


class TestPostalCodeService(TestCase):
    def setUp(self):
        self.service = PostalCodeService()

    def test_create_postal_code_valid_data_capital(self):
        postal_code_obj = self.service.create_postal_code('12345', 'US')
        self.assertIsInstance(postal_code_obj, PostalCode)

    def test_create_postal_code_valid_data_small(self):
        postal_code_obj = self.service.create_postal_code('12345', 'us')
        self.assertIsInstance(postal_code_obj, PostalCode)

    @patch('webapps.google_business_profile.domain.value_types.logger')
    def test_create_postal_code_invalid_country_code(self, mock_service_logger):
        postal_code_obj = self.service.create_postal_code('12345', 'xyz')
        mock_service_logger.error.assert_called_once_with('Country code not supported %s', None)
        self.assertIsInstance(postal_code_obj, PostalCode)
        self.assertEqual(postal_code_obj.country_code, None)
        self.assertEqual(postal_code_obj.value, '12345')
