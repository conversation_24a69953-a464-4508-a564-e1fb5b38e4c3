import unittest
from unittest.mock import patch

from webapps.google_business_profile.domain.const import Country
from webapps.google_business_profile.domain.value_types import PostalCode


class TestPostalCode(unittest.TestCase):
    def test_valid_postal_code(self):
        self.assertIsNotNone(PostalCode(value='12345', country_code=Country.US))

    @patch('webapps.google_business_profile.domain.value_types.logger')
    def test_invalid_postal_code(self, mock_logger):
        PostalCode(value='abc-wrong-123', country_code=Country.US)
        mock_logger.error.assert_called_with("Invalid postal code format %s", 'abc-wrong-123')

    @patch('webapps.google_business_profile.domain.value_types.logger')
    def test_unsupported_country(self, mock_service_logger):
        PostalCode(value='abc-wrong-123', country_code="xyz")  # type: ignore
        mock_service_logger.error.assert_called_once_with("Country code not supported %s", "xyz")

    @patch('webapps.google_business_profile.domain.value_types.logger')
    def test_empty_postal_code(self, mock_logger):
        PostalCode(value='', country_code=Country.US)
        mock_logger.error.assert_any_call("Postal code cannot be empty %s", '')
