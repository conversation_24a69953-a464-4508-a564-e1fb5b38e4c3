import logging
import re
from dataclasses import dataclass

from webapps.google_business_profile.domain.const import POSTAL_CODE_REGEXES, Country
from webapps.google_business_profile.domain.exceptions import (
    EmailValidationError,
)

logger = logging.getLogger('booksy.google_business_profile')


@dataclass(frozen=True)
class RegionCode:
    value: str

    def __post_init__(self):
        # ISO 3166-1 alpha-2
        if not re.match(r"^[A-Z]{2}$", self.value):
            # raise RegionCodeValidationError(self.value)
            logger.error("Invalid region code format: %s", self.value)

    def __str__(self) -> str:
        return self.value


@dataclass(frozen=True)
class LanguageCode:
    value: str

    def __post_init__(self):
        # ISO 639-1
        if not re.match(r"^[a-z]{2}(-[A-Z]{2})?$", self.value):
            # raise LanguageCodeValidationError(self.value)
            logger.error("Invalid language code format: %s", self.value)

    def __str__(self) -> str:
        return self.value


@dataclass(frozen=True)
class CountryCode:
    value: str

    def __post_init__(self):
        # ISO 3166-1 alpha-2
        if not re.match(r"^[A-Za-z]{2}$", self.value):
            # raise RegionCodeValidationError(self.value, "Invalid country code format")
            logger.error("Invalid country code format: %s", self.value)

    def __str__(self) -> str:
        return self.value


@dataclass(frozen=True)
class Email:
    value: str

    def __post_init__(self):
        if not re.match(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", self.value):
            raise EmailValidationError(self.value, "Invalid email address format")

    def __str__(self) -> str:
        return self.value


@dataclass(frozen=True)
class PostalCode:
    value: str
    country_code: Country | None

    def __post_init__(self):
        self._validate()

    def _validate(self):
        postal_code_regex = (
            POSTAL_CODE_REGEXES.get(self.country_code) if self.country_code else None
        )
        if not postal_code_regex:
            logger.error("Country code not supported %s", self.country_code)
        if not self.value:
            # raise PostalCodeValidationError(self.value, "PostalCode cannot be empty")
            logger.error("Postal code cannot be empty %s", self.value)
        if postal_code_regex and not re.match(postal_code_regex, self.value):
            # raise PostalCodeValidationError(self.value, "Invalid postal code format")
            logger.error("Invalid postal code format %s", self.value)

    def __str__(self) -> str:
        return self.value
