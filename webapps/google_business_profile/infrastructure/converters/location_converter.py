import re
from typing import Any

from webapps.google_business_profile.domain.models import GoogleBusinessProfile
from webapps.google_business_profile.domain.const import OpenInfoStatus
from webapps.google_business_profile.domain.value_objects import (
    LocationAddress,
    LocationCategory,
    LocationCategories,
    LocationPhoneNumbers,
    GoogleLocationResource,
    GoogleLocationDetails,
    GoogleLocationLinkage,
    OpenInfo,
)
from webapps.google_business_profile.shared import LocationId, WebsiteUri, PhoneNumber
from webapps.google_business_profile.domain.value_types import (
    RegionCode,
    LanguageCode,
)
from webapps.google_business_profile.domain.services.postal_code import PostalCodeService


class LocationMapper:
    @staticmethod
    def _camel_to_snake(name: str) -> str:
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

    @staticmethod
    def to_domain_entity(google_api_location_data: dict[str, Any]) -> GoogleBusinessProfile:
        location_id = (
            int(google_api_location_data['name'].split('/')[-1])
            if '/' in google_api_location_data['name']
            else int(google_api_location_data['name'])
        )

        resource = GoogleLocationResource(
            id=LocationId(location_id), name=google_api_location_data.get('name', '')
        )
        open_info = None
        if 'openInfo' in google_api_location_data:
            status = google_api_location_data['openInfo'].get(
                'status', OpenInfoStatus.OPEN_INFO_STATUS_UNSPECIFIED
            )
            open_info = OpenInfo(status=status)

        details = GoogleLocationDetails(
            title=google_api_location_data.get('title', ''),
            website_uri=(
                WebsiteUri(google_api_location_data.get('websiteUri', ''))
                if google_api_location_data.get('websiteUri')
                else None
            ),
            open_info=open_info,
        )

        phone_numbers = LocationPhoneNumbers(
            primary_phone=PhoneNumber(
                google_api_location_data.get('phoneNumbers', {}).get('primaryPhone', '')
            ),
            additional_phones=[
                PhoneNumber(phone)
                for phone in google_api_location_data.get('phoneNumbers', {}).get(
                    'additionalPhones', []
                )
            ],
        )

        primary_category = google_api_location_data.get('categories', {}).get('primaryCategory', {})

        categories = LocationCategories(
            primary_category=LocationCategory(
                name=primary_category.get('name', ''),
                display_name=primary_category.get('displayName'),
            )
        )

        address_data = google_api_location_data.get('storefrontAddress', {})
        storefront_address = LocationAddress(
            region_code=RegionCode(address_data.get('regionCode', '')),
            language_code=LanguageCode(address_data.get('languageCode', '')),
            postal_code=PostalCodeService.create_postal_code(
                address_data.get('postalCode', ''), address_data.get('countryCode', '')
            ),
            administrative_area=address_data.get('administrativeArea', ''),
            locality=address_data.get('locality', ''),
            address_lines=address_data.get('addressLines', []),
        )

        linkage = GoogleLocationLinkage(account_id=None, business_id=None, country_code=None)

        return GoogleBusinessProfile(
            resource=resource,
            details=details,
            linkage=linkage,
            phone_numbers=phone_numbers,
            categories=categories,
            storefront_address=storefront_address,
        )
