from typing import Any, <PERSON><PERSON>lias, Protocol

from webapps.google_business_profile.domain.const import VerificationMethod
from webapps.google_business_profile.domain.dtos import (
    EmailVerificationOptionDTO,
    EmailData,
    PhoneCallVerificationOptionDTO,
    SMSVerificationOptionDTO,
    AutoVerificationOptionDTO,
    VettedPartnerVerificationOptionDTO,
    VerificationOptionsDTO,
    BaseVerificationOptionDTO,
)
from webapps.google_business_profile.domain.exceptions import (
    EmailDataValueError,
    PhoneNumberValueError,
    VerificationMethodValueError,
    VerificationDataValueError,
    AnnouncementValueError,
)


VerificationData: TypeAlias = dict[str, Any]


class VerificationConverter(Protocol):
    def __call__(self, data: VerificationData) -> BaseVerificationOptionDTO: ...


class LocationVerificationOptionsMapper:
    def __init__(self):
        self._conversion_dispatch: dict[str, VerificationConverter] = {
            VerificationMethod.EMAIL.value: self._convert_to_email_verification_option,
            VerificationMethod.PHONE_CALL.value: self._convert_to_phone_call_verification_option,
            VerificationMethod.SMS.value: self._convert_to_sms_verification_option,
            VerificationMethod.AUTO.value: self._convert_to_auto_verification_option,
            VerificationMethod.VETTED_PARTNER.value: self._convert_to_vetted_partner_verification_option,  # pylint: disable=line-too-long
        }

    @staticmethod
    def _convert_to_email_verification_option(option: dict[str, Any]) -> EmailVerificationOptionDTO:
        email_data_raw = option.get('emailData')
        if not email_data_raw:
            raise EmailDataValueError('Missing email data for email verification method.')

        email_data_obj = EmailData(
            domain=email_data_raw.get('domain', ''),
            user=email_data_raw.get('user', ''),
            is_user_name_editable=email_data_raw.get('isUserNameEditable', False),
        )
        return EmailVerificationOptionDTO(
            email_data=email_data_obj,
        )

    @staticmethod
    def _convert_to_phone_call_verification_option(
        option: dict[str, Any],
    ) -> PhoneCallVerificationOptionDTO:
        phone_number = option.get('phoneNumber')
        if not phone_number:
            raise PhoneNumberValueError('Missing phone number for phone call verification method.')
        return PhoneCallVerificationOptionDTO(phone_number=phone_number)

    @staticmethod
    def _convert_to_sms_verification_option(option: dict[str, Any]) -> SMSVerificationOptionDTO:
        phone_number = option.get('phoneNumber')
        if not phone_number:
            raise PhoneNumberValueError('Missing phone number for SMS verification method.')
        return SMSVerificationOptionDTO(phone_number=phone_number)

    @staticmethod
    def _convert_to_auto_verification_option() -> AutoVerificationOptionDTO:
        return AutoVerificationOptionDTO()

    @staticmethod
    def _convert_to_vetted_partner_verification_option(
        option: dict[str, Any],
    ) -> VettedPartnerVerificationOptionDTO:
        announcement = option.get('announcement')
        if not announcement:
            raise AnnouncementValueError(
                'Missing announcement data for Vetted Partner verification method.'
            )
        return VettedPartnerVerificationOptionDTO(
            announcement=announcement,
        )

    # pylint: disable=line-too-long
    def to_domain_dto(self, verification_options_data: dict[str, Any]) -> VerificationOptionsDTO:

        conversion_dispatch = self._conversion_dispatch

        converted_options: list[BaseVerificationOptionDTO] = []
        options = verification_options_data.get('options', [])

        for option in options:
            method = option.get('verificationMethod')
            if not method:
                raise VerificationMethodValueError('Missing verification method.')

            converter = conversion_dispatch.get(method)

            if converter:
                try:
                    converted_option = converter(option)
                    converted_options.append(converted_option)
                except VerificationDataValueError as e:
                    print(
                        f"Error converting data for method '{method}': {e}. Skipping this option."
                    )
                except (ValueError, TypeError) as e:
                    print(
                        f"Unexpected error converting option for method '"
                        f"{method}': {e}. Skipping this option."
                    )
            else:
                print(
                    f"Warning: No converter found for unknown method '"
                    f"{method}'. Skipping option: {option}"
                )

        return VerificationOptionsDTO(options=converted_options)
