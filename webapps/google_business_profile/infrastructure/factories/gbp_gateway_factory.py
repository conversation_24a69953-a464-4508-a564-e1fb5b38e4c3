from webapps.google_business_profile.application.dtos.auth_dto import AuthContext
from webapps.google_business_profile.domain.interfaces.gbp_gateway import (
    GoogleBusinessProfileGateway,
)
from webapps.google_business_profile.infrastructure.dtos.gbp_dto import GBPClientData
from webapps.google_business_profile.infrastructure.gateways.gbp_gateway import (
    GoogleBusinessProfileApiGateway,
)
from webapps.google_business_profile.infrastructure.gateways.address_validation_gateway import (
    GoogleAddressValidationApiGateway,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.client import (
    GoogleBusinessProfileClient,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.address_validation_client import (  # pylint: disable=line-too-long
    GoogleAddressValidationClient,
)
from webapps.google_business_profile.shared import BusinessId


class GoogleBusinessProfileGatewayFactory:
    @staticmethod
    def create_gateway(
        auth_context: AuthContext, business_id: BusinessId | None = None
    ) -> GoogleBusinessProfileGateway:
        client_data = GBPClientData(
            oauth_token=auth_context.oauth_token,
            account_id=auth_context.account_id,
            location_id=auth_context.location_id,
            business_id=business_id,
        )
        api_client = GoogleBusinessProfileClient(client_data=client_data)
        return GoogleBusinessProfileApiGateway(api_client=api_client)


class GoogleAddressValidationGatewayFactory:
    @staticmethod
    def create_gateway() -> GoogleAddressValidationApiGateway:
        api_client = GoogleAddressValidationClient()
        return GoogleAddressValidationApiGateway(api_client=api_client)
