from webapps.google_business_profile.application.dtos.auth_dto import AuthContext
from webapps.google_business_profile.domain.interfaces.location_verification_gateway import (
    GoogleLocationVerificationGateway,
)
from webapps.google_business_profile.infrastructure.dtos.location_verification_client_dto import (
    LocationVerificationClientDTO,
)
from webapps.google_business_profile.infrastructure.gateways.location_verification_gateway import (
    GoogleLocationVerificationApiGateway,
)

# pylint: disable=line-too-long
from webapps.google_business_profile.infrastructure.google_business_profile_api.location_verification_client import (
    GoogleLocationVerificationClient,
)


class LocationVerificationGatewayFactory:
    @staticmethod
    def create_gateway(auth_context: AuthContext) -> GoogleLocationVerificationGateway:
        client_data = LocationVerificationClientDTO(
            oauth_token=auth_context.oauth_token,
            location_id=auth_context.location_id,
            verification_id=auth_context.verification_id,
        )
        api_client = GoogleLocationVerificationClient(client_data=client_data)
        return GoogleLocationVerificationApiGateway(api_client=api_client)
