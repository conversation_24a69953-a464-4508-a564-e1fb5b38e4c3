# pylint: disable=line-too-long
from typing import Any

from webapps.google_business_profile.domain.interfaces.google_business_profile_client import (
    GoogleBusinessProfileAbstractClient,
    ApiResponse,
    CategoryQueryParams,
    UpdateLocationParams,
    CreateLocationParams,
)
from webapps.google_business_profile.domain.value_objects import (
    LocationAddress,
    LocationCategories,
)
from webapps.google_business_profile.infrastructure.dtos.gbp_dto import GBPClientData
from webapps.google_business_profile.infrastructure.google_business_profile_api.http.http_client import (
    HttpClient,
)

LOCATION_FIELDS = (
    'name,title,phoneNumbers,websiteUri,regularHours,storefrontAddress,openInfo.status,categories'
)
UPDATE_LOCATION_FIELDS = 'title,storefrontAddress,categories'
GBP_INFO_API_V1 = 'https://mybusinessbusinessinformation.googleapis.com/v1'
GBP_CORE_API_V1 = 'https://mybusiness.googleapis.com/v1'


class GoogleBusinessProfileClient(GoogleBusinessProfileAbstractClient):
    """Client for Google Business Profile API that handles API-specific concerns"""

    def __init__(self, client_data: GBPClientData, http_client: HttpClient = None):
        self._oauth_token = client_data.oauth_token
        self.account_id = client_data.account_id
        self.location_id = client_data.location_id
        self.business_id = client_data.business_id
        self._http_client = http_client or HttpClient()

    def _get_auth_headers(self) -> dict[str, str]:
        return {
            "Authorization": f"Bearer {self._oauth_token}",
            "Content-Type": "application/json",
        }

    @staticmethod
    def _build_storefront_address(storefront_address: LocationAddress) -> dict[str, Any]:
        return {
            'regionCode': str(storefront_address.region_code),
            'languageCode': str(storefront_address.language_code),
            'postalCode': str(storefront_address.postal_code),
            'administrativeArea': storefront_address.administrative_area,
            'locality': storefront_address.locality,
            'addressLines': storefront_address.address_lines,
        }

    @staticmethod
    def _build_categories(categories: LocationCategories) -> dict[str, Any]:
        primary_category = {'name': categories.primary_category.name}

        if categories.primary_category.display_name:
            primary_category['displayName'] = categories.primary_category.display_name

        return {'primaryCategory': primary_category}

    def _build_location_update_request(self, location_data: UpdateLocationParams) -> dict[str, Any]:
        request_data = {
            'title': location_data.title,
            'categories': self._build_categories(location_data.categories),
            'storefrontAddress': (
                self._build_storefront_address(location_data.storefront_address)
                if location_data.storefront_address
                else {}
            ),
        }

        return request_data

    def _build_location_create_request(self, location_data: CreateLocationParams) -> dict[str, Any]:
        request_data = {
            'title': location_data.title,
            'languageCode': str(location_data.language_code),
            'storefrontAddress': self._build_storefront_address(location_data.storefront_address),
            'categories': self._build_categories(location_data.categories),
            'phoneNumbers': {'primaryPhone': location_data.phone_numbers.primary_phone},
        }

        if location_data.phone_numbers.additional_phones:
            request_data['phoneNumbers'][
                'additionalPhones'
            ] = location_data.phone_numbers.additional_phones

        if location_data.website_uri:
            request_data['websiteUri'] = location_data.website_uri

        return request_data

    def get_gbp_accounts(self) -> ApiResponse:
        url = f'{GBP_INFO_API_V1}/accounts'
        response = self._http_client.get(url, headers=self._get_auth_headers())
        return ApiResponse(status_code=response['status_code'], data=response['data'])

    def get_gbp_locations(self) -> ApiResponse:
        url = (
            f'{GBP_INFO_API_V1}/accounts/'
            f'{self.account_id}/locations?readMask={LOCATION_FIELDS},&pageSize=100'
        )
        response = self._http_client.get(url, headers=self._get_auth_headers())
        return ApiResponse(status_code=response['status_code'], data=response['data'])

    def create_gbp_location(self, location_data: CreateLocationParams) -> ApiResponse:
        url = f'{GBP_CORE_API_V1}/accounts/{self.account_id}/locations/'
        request_data = self._build_location_create_request(location_data)
        response = self._http_client.post(url, headers=self._get_auth_headers(), data=request_data)
        return ApiResponse(status_code=response['status_code'], data=response['data'])

    def get_particular_location(self) -> ApiResponse:
        url = f'{GBP_INFO_API_V1}' f'/locations/{self.location_id}?readMask={LOCATION_FIELDS}'
        response = self._http_client.get(url, headers=self._get_auth_headers())
        return ApiResponse(status_code=response['status_code'], data=response['data'])

    def update_gbp_location(self, location_data: UpdateLocationParams) -> ApiResponse:
        validate = 'validateOnly=False'
        if location_data.validate_only:
            validate = 'validateOnly=True'

        url = (
            f'{GBP_INFO_API_V1}'
            f'/locations/{self.location_id}?{validate}&updateMask={UPDATE_LOCATION_FIELDS}'
        )
        request_data = self._build_location_update_request(location_data)
        response = self._http_client.patch(url, headers=self._get_auth_headers(), data=request_data)
        return ApiResponse(status_code=response['status_code'], data=response['data'])

    def get_gbp_location_status(self) -> ApiResponse:
        url = f'{GBP_INFO_API_V1}' f'/locations/{self.location_id}/VoiceOfMerchantState'
        response = self._http_client.get(url, headers=self._get_auth_headers())
        return ApiResponse(status_code=response['status_code'], data=response['data'])

    def get_gbp_location_verifications(self) -> ApiResponse:
        url = f'{GBP_INFO_API_V1}' f'/locations/{self.location_id}/verifications'
        response = self._http_client.get(url, headers=self._get_auth_headers())

        return ApiResponse(status_code=response['status_code'], data=response['data'])

    def get_categories(self, data: CategoryQueryParams) -> ApiResponse:
        url = (
            f'{GBP_CORE_API_V1}/categories?'
            f'regionCode={data.region_code}&'
            f'languageCode={data.language_code}&'
            f'pageSize={data.page_size}&'
            f'view={data.view}&'
            f'filter=displayName={data.filter}'
        )
        response = self._http_client.get(url, headers=self._get_auth_headers())
        return ApiResponse(status_code=response['status_code'], data=response['data'])

    def delete_gbp_location(self) -> ApiResponse:
        url = f'{GBP_INFO_API_V1}' f'/locations/{self.location_id}'
        response = self._http_client.delete(url, headers=self._get_auth_headers())
        return ApiResponse(status_code=response['status_code'], data=response['data'])
