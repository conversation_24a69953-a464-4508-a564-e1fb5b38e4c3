# pylint: disable=protected-access, duplicate-code, line-too-long
import datetime
from collections import OrderedDict
from unittest.mock import patch, PropertyMock

import pytest
from django.urls import reverse
from rest_framework import status

from drf_api.lib.base_drf_test_case import BusinessOwnerAPITestCase
from webapps.business.baker_recipes import business_recipe
from webapps.business.v2.business.application.queries.dto.my_businesses import BusinessRWGDTO
from webapps.google_business_profile.domain.const import OpenInfoStatus, Country
from webapps.google_business_profile.domain.interfaces.google_business_profile_client import (
    ApiResponse,
)
from webapps.google_business_profile.domain.models import GoogleBusinessProfile
from webapps.google_business_profile.domain.value_objects import (
    LocationAddress,
    LocationCategories,
    LocationCategory,
    LocationPhoneNumbers,
    GoogleLocationResource,
    GoogleLocationDetails,
    OpenInfo,
    GoogleLocationLinkage,
)
from webapps.google_business_profile.domain.value_types import (
    RegionCode,
    LanguageCode,
    CountryCode,
    PostalCode,
)
from webapps.google_business_profile.infrastructure.firestore.client import (
    BusinessProfileFirestoreRepository,
    BusinessFirestoreRepository,
)
from webapps.google_business_profile.infrastructure.gateways.business_gateway import (
    BusinessDetailsGateway,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.address_validation_client import (
    GoogleAddressValidationClient,
)
from webapps.google_business_profile.infrastructure.google_business_profile_api.client import (
    GoogleBusinessProfileClient,
)
from webapps.google_business_profile.shared import (
    LocationId,
    AccountId,
    BusinessId,
    PhoneNumber,
)


@pytest.mark.django_db
class TestAccountsView(BusinessOwnerAPITestCase):
    def setUp(self):
        self.business = business_recipe.make(zipcode='12345')
        self.user = self.business.owner
        self.url = reverse('google_business_profiles', kwargs={'business_pk': self.business.id})
        self.data = {'oauth_token': 'xxxxxxxx'}
        super().setUp()

    @patch.object(GoogleBusinessProfileClient, 'get_gbp_accounts')
    def test_get_accounts(self, mock_get_accounts):
        account_data = {
            'accounts': [
                {
                    'name': 'accounts/110165398739483868345',
                    'accountName': 'Dan Kuk',
                    'type': 'PERSONAL',
                    'verificationState': 'UNVERIFIED',
                    'vettedState': 'NOT_VETTED',
                    'role': None,
                    'permissionLevel': None,
                },
                {
                    'name': 'accounts/10758437509412373209',
                    'accountName': 'users',
                    'type': 'LOCATION_GROUP',
                    'verificationState': 'UNVERIFIED',
                    'vettedState': 'NOT_VETTED',
                    'role': 'PRIMARY_OWNER',
                    'permissionLevel': 'OWNER_LEVEL',
                },
            ]
        }
        excepted_account_data = {
            'accounts': [
                OrderedDict([('account_id', '110165398739483868345'), ('account_name', 'Dan Kuk')]),
                OrderedDict([('account_id', '10758437509412373209'), ('account_name', 'users')]),
            ]
        }
        mock_get_accounts.return_value = ApiResponse(200, account_data)
        response = self.client.post(self.url, self.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(response.data, excepted_account_data)

    @patch.object(GoogleBusinessProfileClient, 'get_gbp_accounts')
    def test_get_404_accounts(self, mock_get_accounts):
        account_data = {
            'error': {
                'code': 404,
                'message': 'Requested entity was not found.',
                'status': 'NOT_FOUND',
            },
        }
        excepted_account_data = {
            "errors": [
                {
                    "type": 'invalid',
                    "code": "not_found",
                    "description": "Requested object not found",
                }
            ]
        }
        mock_get_accounts.return_value = ApiResponse(404, account_data)
        response = self.client.post(self.url, self.data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertDictEqual(response.data, excepted_account_data)


@pytest.mark.django_db
class TestLocationsView(BusinessOwnerAPITestCase):
    def setUp(self):
        self.business = business_recipe.make(zipcode='12345')
        self.user = self.business.owner
        self.url = reverse('google_business_locations', kwargs={'business_pk': self.business.id})
        self.data = {'oauth_token': 'xxxxxxxx', 'account_id': '110165398739483868345'}
        super().setUp()

    @patch.object(GoogleBusinessProfileClient, 'get_gbp_locations')
    def test_get_locations(self, mock_get_locations):
        locations_data = {
            "locations": [
                {
                    "name": "locations/8377985737370899348",
                    "title": "Google Sydney",
                    "phoneNumbers": {"primaryPhone": "(02) 9374 4000"},
                    "categories": {
                        "primaryCategory": {
                            "name": "categories/gcid:software_company",
                            "displayName": "Software company",
                        }
                    },
                    "storefrontAddress": {
                        "regionCode": "AU",
                        "languageCode": "en-AU",
                        "postalCode": "2009",
                        "administrativeArea": "NSW",
                        "locality": "Pyrmont",
                        "addressLines": ["Level 5", "48 Pirrama Road"],
                    },
                    "websiteUri": "https://www.google.com.au/",
                    "openInfo": {"status": "OPEN"},
                },
                {
                    "name": "locations/9352331843269194691",
                    "title": "vlad",
                    "phoneNumbers": {"primaryPhone": "8 (495) 739-53-65"},
                    "categories": {
                        "primaryCategory": {
                            "name": "categories/gcid:massage_school",
                            "displayName": "Massage school",
                        }
                    },
                    "storefrontAddress": {
                        "regionCode": "US",
                        "languageCode": "us",
                        "postalCode": "12345",
                        "administrativeArea": "",
                        "locality": "",
                        "addressLines": [],
                    },
                    "openInfo": {"status": "OPEN"},
                },
            ]
        }
        expected_locations_data = {
            'locations': [
                OrderedDict(
                    [
                        ('location_id', '8377985737370899348'),
                        ('title', 'Google Sydney'),
                        ('address', 'Level 5, 48 Pirrama Road, Pyrmont, 2009, NSW'),
                    ]
                ),
                OrderedDict(
                    [
                        ('location_id', '9352331843269194691'),
                        ('title', 'vlad'),
                        ('address', ', , 12345, '),
                    ]
                ),
            ]
        }
        mock_get_locations.return_value = ApiResponse(200, locations_data)
        response = self.client.post(self.url, self.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(response.data, expected_locations_data)

    @patch.object(GoogleBusinessProfileClient, 'get_gbp_locations')
    def test_get_401_locations(self, mock_get_locations):
        locations_data = {
            "error": {
                "code": 401,
                "message": "Request had invalid authentication credentials. "
                "Expected OAuth 2 access token, login cookie or other valid "
                "authentication credential.",
                "status": "UNAUTHENTICATED",
            }
        }
        expected_locations_data = {
            "errors": [
                {
                    "type": 'authentication',
                    "code": "invalid_credentials",
                    "description": "Authentication failed. Please check your credentials.",
                }
            ]
        }
        mock_get_locations.return_value = ApiResponse(401, locations_data)
        response = self.client.post(self.url, self.data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertDictEqual(response.data, expected_locations_data)

    @patch.object(GoogleBusinessProfileClient, 'create_gbp_location')
    def test_create_location(self, mock_create_location_business_profile):
        with patch.object(
            self.business.__class__, 'subdomain', new_callable=PropertyMock
        ) as mock_subdomain:
            mock_subdomain.return_value = "my-salon"
            request_data = {
                "oauth_token": "xxxxxxx",
                "account_id": '110165365839483868769',
            }

            response_location_data = {
                "name": "locations/8366985737370899348",
                "title": "Google Sydney",
                # "location_id": *********,
                "phoneNumbers": {"primaryPhone": "(02) 9374 4000"},
                "categories": {
                    "primaryCategory": {
                        "name": "categories/gcid:software_company",
                        "displayName": "Software company",
                    }
                },
                "storefrontAddress": {
                    "regionCode": "AU",
                    "languageCode": "en-AU",
                    "postalCode": "2009",
                    "administrativeArea": "NSW",
                    "locality": "Pyrmont",
                    "addressLines": ["Level 5", "48 Pirrama Road"],
                },
                "websiteUri": "my-salon.booksy.com/",
                "openInfo": {"status": "OPEN"},
            }

            expected_location_data = {
                "location_id": '8366985737370899348',
            }

            mock_create_location_business_profile.return_value = ApiResponse(
                status_code=200,
                data=response_location_data,
            )

            response = self.client.put(self.url, request_data)

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertDictEqual(response.data, expected_location_data)

            mock_create_location_business_profile.assert_called_once()

    @patch.object(GoogleBusinessProfileClient, 'update_gbp_location')
    def test_update_location(self, update_gbp_location):
        location_data = {
            "oauth_token": "xxxxxxxxx",
            "location_id": '110165345839483868769',
            "languageCode": "en-AU",
            "title": "Google Sydne",
            "storefront_address": {
                "address_lines": ["Level 6", "48 Pirrama Road"],
                "locality": "Pyrmont",
                "postal_code": "2009",
                "administrative_area": "NSW",
                "region_code": "AU",
                "country_code": "AU",
                "language_code": "en-AU",
            },
            "categories": {"primary_category": {"name": "gcid:software_company"}},
        }

        response_location_data = {
            "name": "locations/8366985737354899348",
            "title": "Google Sydney",
            "phoneNumbers": {"primaryPhone": "(02) 9374 4000"},
            "categories": {
                "primaryCategory": {
                    "name": "categories/gcid:software_company",
                    "displayName": "Software company",
                }
            },
            "storefrontAddress": {
                "countryCode": "AU",
                "regionCode": "AU",
                "languageCode": "en-AU",
                "postalCode": "2009",
                "administrativeArea": "NSW",
                "locality": "Pyrmont",
                "addressLines": ["Level 5", "48 Pirrama Road"],
            },
            "websiteUri": "https://www.google.com.au/",
            "openInfo": {"status": "OPEN"},
        }
        expected_response_data = {}
        url = reverse('google_business_location', kwargs={'business_pk': self.business.id})
        update_gbp_location.return_value = ApiResponse(200, response_location_data)
        response = self.client.put(url, location_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(response.data, expected_response_data)

    @patch.object(GoogleBusinessProfileClient, 'get_particular_location')
    @patch.object(BusinessProfileFirestoreRepository, 'get')
    @patch.object(BusinessDetailsGateway, '_fetch_business_rwg_data')
    def test_get_location(self, _fetch_business_rwg_data, get, get_particular_location):
        data = {
            "oauth_token": "xxxxxxxxx",
            "location_id": '110165345839483868769',
        }

        response_google_location_data = {
            "name": "locations/8366985737354439348",
            "title": "My Salon",
            "phoneNumbers": {"primaryPhone": "+**************"},
            "categories": {
                "primaryCategory": {
                    "name": "categories/gcid:beauty_salon",
                    "displayName": "Beauty salon",
                }
            },
            "storefrontAddress": {
                "regionCode": "US",
                "languageCode": "en-US",
                "postalCode": "20016",
                "administrativeArea": "DC",
                "locality": "Washington",
                "addressLines": ['4866 Massachusetts Ave NW'],
            },
            "websiteUri": "",
            "openInfo": {"status": "OPEN"},
        }

        response_booksy_buisness_profile = GoogleBusinessProfile(
            resource=GoogleLocationResource(
                id=LocationId(14831498223480222051), name="locations/14831498223480222051"
            ),
            details=GoogleLocationDetails(
                title='My Salon',
                website_uri=None,
                open_info=OpenInfo(status=OpenInfoStatus('OPEN')),
            ),
            linkage=GoogleLocationLinkage(
                account_id=AccountId(110165368939483868769),
                business_id=BusinessId(404894),
                country_code=CountryCode('US'),
            ),
            phone_numbers=LocationPhoneNumbers(
                primary_phone=PhoneNumber('+**************'), additional_phones=[]
            ),
            categories=LocationCategories(
                primary_category=LocationCategory(
                    name='categories/gcid:beauty_salon',
                    display_name='Beauty salon',
                    category_id='gcid:beauty_salon',
                ),
                additional_categories=[],
            ),
            storefront_address=LocationAddress(
                region_code=RegionCode('US'),
                language_code=LanguageCode('en-US'),
                postal_code=PostalCode('20016', Country.US),
                administrative_area='DC',
                locality='Washington',
                address_lines=['4866 Massachusetts Ave NW'],
            ),
        )

        response_booksy_business_data = BusinessRWGDTO(
            name='My Salon',
            official_name='',
            region_id=44468,
            address='4866 Massachusetts Ave NW',
            address2=None,
            city='Washington',
            city_or_region_city='Washington',
            country_code='us',
            active=True,
            created=datetime.datetime(
                2024, 10, 8, 11, 43, 31, 206076, tzinfo=datetime.timezone.utc
            ),
            description='',
            booking_mode='S',
            disable_google_reserve=False,
            latitude=38.934,
            longitude=-77.066,
            owner_id=356,
            phone='+***********',
            primary_category_id=1,
            primary_category_name='Barbershop',
            primary_category_slug='barber-shop',
            primary_category_report_name='Barbershop',
            public_email='',
            region_state_code='DC',
            renting_venue=False,
            status='T',
            verification='?',
            visible=True,
            website='',
            zipcode='20016',
            active_till=None,
            active_from=datetime.datetime(
                2024, 10, 8, 11, 43, 31, 206076, tzinfo=datetime.timezone.utc
            ),
            visible_from=datetime.datetime(
                2024, 10, 8, 11, 43, 31, 206076, tzinfo=datetime.timezone.utc
            ),
            updated=datetime.datetime(
                2024, 10, 8, 11, 43, 31, 206076, tzinfo=datetime.timezone.utc
            ),
            subdomain='my-salon',
        )

        expected_location = OrderedDict(
            [
                ("title", "My Salon"),
                ("category", "Beauty salon"),
                ("address", "4866 Massachusetts Ave NW, Washington, 20016, DC"),
            ]
        )

        expected_business = OrderedDict(
            [
                ("title", "My Salon"),
                ("category", "Barbershop"),
                ("address", "4866 Massachusetts Ave NW, Washington, 20016, DC"),
            ]
        )

        expected_compare = {
            "title": True,
            "category": True,
            "address": "Match",
        }
        url = reverse('google_business_location', kwargs={'business_pk': self.business.id})
        get_particular_location.return_value = ApiResponse(200, response_google_location_data)
        get.return_value = response_booksy_buisness_profile
        _fetch_business_rwg_data.return_value = response_booksy_business_data
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(response.data['location'], expected_location)
        self.assertDictEqual(response.data['business'], expected_business)
        self.assertDictEqual(response.data['compare'], expected_compare)


@pytest.mark.django_db
class TestCategories(BusinessOwnerAPITestCase):
    def setUp(self):
        self.business = business_recipe.make()
        self.user = self.business.owner
        self.url = reverse(
            'google_business_categories',
            kwargs={'business_pk': self.business.id},
        )
        super().setUp()

    @patch.object(GoogleBusinessProfileClient, 'get_categories')
    def test_get_categories(self, get_categories):
        request_data = {
            'oauth_token': 'xxxxxxxx',
            'region_code': 'PL',
            'language_code': 'en',
            'filter': 'barber',
        }
        response_data = {
            "categories": [
                {"name": "categories/gcid:barber_school", "displayName": "Barber school"},
                {"name": "categories/gcid:barber_shop", "displayName": "Barber shop"},
                {
                    "name": "categories/gcid:barber_supply_store",
                    "displayName": "Barber supply store",
                },
            ]
        }
        expected_data = {
            "categories": [
                {"name": "categories/gcid:barber_school", "display_name": "Barber school"},
                {"name": "categories/gcid:barber_shop", "display_name": "Barber shop"},
                {
                    "name": "categories/gcid:barber_supply_store",
                    "display_name": "Barber supply store",
                },
            ]
        }

        get_categories.return_value = ApiResponse(200, response_data)
        response = self.client.post(self.url, request_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(response.data, expected_data)


@pytest.mark.django_db
class TestSaveBooksyProfile(BusinessOwnerAPITestCase):
    def setUp(self):
        self.business = business_recipe.make()
        self.user = self.business.owner
        self.url = reverse(
            'booksy_profile',
            kwargs={'business_pk': self.business.id},
        )
        super().setUp()

    @patch.object(GoogleBusinessProfileClient, 'get_particular_location')
    @patch.object(BusinessProfileFirestoreRepository, 'save')
    @patch.object(BusinessDetailsGateway, '_fetch_business_rwg_data')
    @patch.object(BusinessFirestoreRepository, 'save')
    def test_save_booksy_profile(
        self,
        b_repository_save,
        _fetch_business_rwg_data,
        bp_repository_save,
        get_particular_location,
    ):
        data = {
            "oauth_token": "xxxxxxxxx",
            "account_id": "110165345839483868345",
            "location_id": "110165345839483868769",
        }

        response_google_location_data = {
            "name": "locations/8366985737354439348",
            "title": "My Salon",
            "phoneNumbers": {"primaryPhone": "+**************"},
            "categories": {
                "primaryCategory": {
                    "name": "categories/gcid:beauty_salon",
                    "displayName": "Beauty salon",
                }
            },
            "storefrontAddress": {
                "regionCode": "US",
                "languageCode": "en-US",
                "postalCode": "20016",
                "administrativeArea": "DC",
                "locality": "Washington",
                "addressLines": ['4866 Massachusetts Ave NW'],
            },
            "websiteUri": "",
            "openInfo": {"status": "OPEN"},
        }

        response_booksy_business_data = BusinessRWGDTO(
            name='My Salon',
            official_name='',
            region_id=44468,
            address='4866 Massachusetts Ave NW',
            address2=None,
            city='Washington',
            city_or_region_city='Washington',
            country_code='US',
            active=True,
            created=datetime.datetime(
                2024, 10, 8, 11, 43, 31, 206076, tzinfo=datetime.timezone.utc
            ),
            description='',
            booking_mode='S',
            disable_google_reserve=False,
            latitude=38.934,
            longitude=-77.066,
            owner_id=356,
            phone='+***********',
            primary_category_id=1,
            primary_category_name='Barbershop',
            primary_category_slug='barber-shop',
            primary_category_report_name='Barbershop',
            public_email='',
            region_state_code='DC',
            renting_venue=False,
            status='T',
            verification='?',
            visible=True,
            website='',
            zipcode='20016',
            active_till=None,
            active_from=datetime.datetime(
                2024, 10, 8, 11, 43, 31, 206076, tzinfo=datetime.timezone.utc
            ),
            visible_from=datetime.datetime(
                2024, 10, 8, 11, 43, 31, 206076, tzinfo=datetime.timezone.utc
            ),
            updated=datetime.datetime(
                2024, 10, 8, 11, 43, 31, 206076, tzinfo=datetime.timezone.utc
            ),
            subdomain='my-salon',
        )

        get_particular_location.return_value = ApiResponse(200, response_google_location_data)
        bp_repository_save.return_value = 1
        _fetch_business_rwg_data.return_value = response_booksy_business_data
        b_repository_save.return_value = 1

        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {})

    @patch.object(BusinessProfileFirestoreRepository, 'get')
    def test_get_location_id(self, get_bp):
        response_booksy_buisness_profile = GoogleBusinessProfile(
            resource=GoogleLocationResource(
                id=LocationId(14831498223480222051), name="locations/14831498223480222051"
            ),
            details=GoogleLocationDetails(
                title='My Salon',
                website_uri=None,
                open_info=OpenInfo(status=OpenInfoStatus('OPEN')),
            ),
            linkage=GoogleLocationLinkage(
                account_id=AccountId(110165368939483868769),
                business_id=BusinessId(404894),
                country_code=CountryCode('US'),
            ),
            phone_numbers=LocationPhoneNumbers(
                primary_phone=PhoneNumber('+**************'), additional_phones=[]
            ),
            categories=LocationCategories(
                primary_category=LocationCategory(
                    name='categories/gcid:beauty_salon',
                    display_name='Beauty salon',
                    category_id='gcid:beauty_salon',
                ),
                additional_categories=[],
            ),
            storefront_address=LocationAddress(
                region_code=RegionCode('US'),
                language_code=LanguageCode('en-US'),
                postal_code=PostalCode('20016', Country.US),
                administrative_area='DC',
                locality='Washington',
                address_lines=['4866 Massachusetts Ave NW'],
            ),
        )
        get_bp.return_value = response_booksy_buisness_profile

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['location_id'], '14831498223480222051')


@pytest.mark.django_db
class TestCheckGoogleMapsAddress(BusinessOwnerAPITestCase):
    def setUp(self):
        self.business = business_recipe.make(zipcode='12345')
        self.user = self.business.owner
        self.url = reverse(
            'check_address',
            kwargs={'business_pk': self.business.id},
        )
        super().setUp()

    @patch.object(GoogleAddressValidationClient, 'check_address')
    def test_check_address_wrong(self, check_address):

        check_address.return_value = ApiResponse(
            200,
            {
                'result': {
                    'address': {
                        'addressComponents': [
                            {
                                'componentName': {'text': '*********'},
                                'componentType': 'street_number',
                                'confirmationLevel': 'UNCONFIRMED_BUT_PLAUSIBLE',
                            },
                            {
                                'componentName': {
                                    'languageCode': 'en',
                                    'text': 'Massachusetts Avenue Northwest',
                                },
                                'componentType': 'route',
                                'confirmationLevel': 'CONFIRMED',
                            },
                            {
                                'componentName': {'languageCode': 'en', 'text': 'Washington'},
                                'componentType': 'locality',
                                'confirmationLevel': 'CONFIRMED',
                            },
                            {
                                'componentName': {'languageCode': 'en', 'text': 'PC'},
                                'componentType': 'administrative_area_level_1',
                                'confirmationLevel': 'UNCONFIRMED_AND_SUSPICIOUS',
                            },
                            {
                                'componentName': {'text': '20016'},
                                'componentType': 'postal_code',
                                'confirmationLevel': 'CONFIRMED',
                            },
                            {
                                'componentName': {'languageCode': 'en', 'text': 'USA'},
                                'componentType': 'country',
                                'confirmationLevel': 'CONFIRMED',
                            },
                        ],
                        'formattedAddress': '********* Massachusetts Avenue Northwest, Washington, PC 20016, USA',
                        'postalAddress': {
                            'addressLines': ['********* Massachusetts Ave NW'],
                            'administrativeArea': 'PC',
                            'languageCode': 'en',
                            'locality': 'Washington',
                            'postalCode': '20016',
                            'regionCode': 'US',
                        },
                        'unconfirmedComponentTypes': [
                            'street_number',
                            'administrative_area_level_1',
                        ],
                    },
                    'verdict': {
                        'addressComplete': True,
                        'geocodeGranularity': 'ROUTE',
                        'hasUnconfirmedComponents': True,
                        'inputGranularity': 'PREMISE',
                        'validationGranularity': 'ROUTE',
                    },
                }
            },
        )

        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(
            response.json(),
            {
                'address': '********* Massachusetts Avenue Northwest, Washington, PC 20016, USA',
                'is_valid': 'False',
                'missing_components': None,
                'unconfirmed_components': ['Street number', 'Administrative area'],
            },
        )

    @patch.object(GoogleAddressValidationClient, 'check_address')
    def test_check_address_correct(self, check_address):
        check_address.return_value = ApiResponse(
            200,
            {
                'result': {
                    'address': {
                        'addressComponents': [
                            {
                                'componentName': {'text': '4866'},
                                'componentType': 'street_number',
                                'confirmationLevel': 'CONFIRMED',
                            },
                            {
                                'componentName': {
                                    'languageCode': 'en',
                                    'text': 'Massachusetts Avenue Northwest',
                                },
                                'componentType': 'route',
                                'confirmationLevel': 'CONFIRMED',
                            },
                            {
                                'componentName': {'languageCode': 'en', 'text': 'Washington'},
                                'componentType': 'locality',
                                'confirmationLevel': 'CONFIRMED',
                            },
                            {
                                'componentName': {'languageCode': 'en', 'text': 'DC'},
                                'componentType': 'administrative_area_level_1',
                                'confirmationLevel': 'CONFIRMED',
                            },
                            {
                                'componentName': {'text': '20016'},
                                'componentType': 'postal_code',
                                'confirmationLevel': 'CONFIRMED',
                            },
                            {
                                'componentName': {'languageCode': 'en', 'text': 'USA'},
                                'componentType': 'country',
                                'confirmationLevel': 'CONFIRMED',
                            },
                            {
                                'componentName': {'text': '2066'},
                                'componentType': 'postal_code_suffix',
                                'confirmationLevel': 'CONFIRMED',
                                'inferred': True,
                            },
                        ],
                        'formattedAddress': '4866 Massachusetts Avenue Northwest, Washington, DC 20016-2066, USA',
                        'postalAddress': {
                            'addressLines': ['4866 Massachusetts Ave NW'],
                            'administrativeArea': 'DC',
                            'languageCode': 'en',
                            'locality': 'Washington',
                            'postalCode': '20016-2066',
                            'regionCode': 'US',
                        },
                    },
                    'verdict': {
                        'addressComplete': True,
                        'geocodeGranularity': 'PREMISE',
                        'hasInferredComponents': True,
                        'inputGranularity': 'PREMISE',
                        'validationGranularity': 'PREMISE',
                    },
                },
            },
        )
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(
            response.json(),
            {
                'address': '4866 Massachusetts Avenue Northwest, Washington, DC 20016-2066, USA',
                'is_valid': 'True',
                'missing_components': None,
                'unconfirmed_components': None,
            },
        )
