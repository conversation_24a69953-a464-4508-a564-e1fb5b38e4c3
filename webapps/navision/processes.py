import logging
from datetime import timedelta
from typing import Optional, Tuple

from django.conf import settings
from django.db import transaction
from django.db.models import Q

from country_config import Country
from lib.db import (
    using_db_for_reads,
    READ_ONLY_DB,
)
from lib.feature_flag.feature.navision import NavisionDefaultTaxGroupFlag
from lib.payment_gateway.enums import (
    WalletOwnerType,
    BalanceTransactionType,
    BalanceTransactionStatus,
)
from lib.tools import tznow
from webapps.billing.models import BillingCycle
from webapps.boost.models import BoostAppointment
from webapps.business.models import Business
from webapps.business.models.business_change import BusinessChange
from webapps.navision.adapters import StripeTerminalOrderingAdapter
from webapps.navision.models import TaxGroup
from webapps.navision.ports import InsufficientDataError
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.pos.enums import PaymentProviderEnum, receipt_status
from webapps.pos.models import PaymentRow
from webapps.purchase.models import InvoiceAddress, SubscriptionBuyer
from webapps.navision.tasks.buyer_merchant_integration import sync_buyer_with_merchant_task
from webapps.structure.enums import RegionType
from webapps.structure.models import Region
from webapps.user.models import User

logger = logging.getLogger("booksy.navision")


class BusinessMigrationError(Exception):
    pass


def _get_businesses_ids(offset_days=60, window_length=59):
    horizon_from = tznow() - timedelta(days=offset_days)
    horizon_to = horizon_from + timedelta(days=window_length)
    yesterday = tznow() - timedelta(days=1)
    horizon_to = min(horizon_to, yesterday)

    pt_filters = {
        'sender_owner_type': WalletOwnerType.CUSTOMER,
        'receiver_owner_type': WalletOwnerType.BUSINESS,
        'transaction_type': BalanceTransactionType.PAYMENT,
        'status': BalanceTransactionStatus.SUCCESS,
        'payment_capture_date_gte': horizon_from,
        'payment_capture_date_lte': horizon_to,
    }
    payment_transactions_businesses = PaymentGatewayPort.get_businesses_with_balance_transactions(
        business_is_sender=False,
        **pt_filters,
    )

    fee_filters = {
        'sender_owner_type': WalletOwnerType.BUSINESS,
        'receiver_owner_type': WalletOwnerType.BOOKSY,
        'transaction_type': BalanceTransactionType.FEE,
        'status': BalanceTransactionStatus.SUCCESS,
        'created_date_gte': horizon_from,
        'created_date_lte': horizon_to,
    }
    fee_transactions_businesses = PaymentGatewayPort.get_businesses_with_balance_transactions(
        business_is_sender=True, **fee_filters
    )

    adyen_transactions_businesses = set(
        PaymentRow.objects.filter(
            provider=PaymentProviderEnum.ADYEN_PROVIDER,
            status__in=[
                receipt_status.DEPOSIT_CHARGE_SUCCESS,
                receipt_status.PAYMENT_SUCCESS,
                receipt_status.PREPAYMENT_SUCCESS,
            ],
            marketpay_splits__isnull=False,
            created__date__gte=horizon_from,
            created__date__lte=horizon_to,
        )
        .values_list('receipt__transaction__pos__business__id', flat=True)
        .distinct()
    )
    boost_appointment_businesses = set(
        BoostAppointment.objects.filter(
            created__date__gte=horizon_from, created__date__lte=horizon_to
        )
        .distinct('appointment__business')
        .values_list('appointment__business__id', flat=True)
    )
    new_billing_subscriptions_businesses = set(
        BillingCycle.objects.exclude(Q(date_start__gte=horizon_to) | Q(date_end__lte=horizon_from))
        .distinct('business')
        .values_list('business_id', flat=True)
    )
    return (
        payment_transactions_businesses
        | fee_transactions_businesses
        | adyen_transactions_businesses
        | boost_appointment_businesses
        | new_billing_subscriptions_businesses
    )


def businesses_without_buyer(offset_days=60, window_length=59, **filters):
    businesses_ids = _get_businesses_ids(offset_days, window_length)
    with using_db_for_reads(READ_ONLY_DB):
        return Business.objects.filter(
            id__in=businesses_ids,
            deleted__isnull=True,
            buyer__isnull=True,
            **filters,
        )


def active_businesses(offset_days=60, window_length=59, **filters):
    businesses_ids = _get_businesses_ids(offset_days, window_length)
    with using_db_for_reads(READ_ONLY_DB):
        return Business.objects.filter(
            id__in=businesses_ids,
            deleted__isnull=True,
            active=True,
            **filters,
        )


def stripe_terminal_owners(**filters):
    """
    Businesses that ever ordered stripe terminal
    """
    return Business.objects.filter(
        id__in=StripeTerminalOrderingAdapter.terminal_buyer_business_ids(),
        **filters,
    )


def get_zipcode(business: Business) -> Tuple[Region, str]:
    if not settings.CHECK_ZIPCODE_IN_REGION_TABLE:
        return None, business.zip

    return (
        Region.objects.filter(type=Region.Type.ZIP, name=business.zip).first(),
        business.zip,
    )


def get_state(business: Business, zipcode: Optional[Region]) -> Optional[Region]:
    state = None

    if zipcode is not None:
        state = zipcode.get_parent_by_type([Region.Type.STATE])

    if business.region is not None and state is None:
        state = business.region.get_parent_by_type([Region.Type.STATE])

    return state


def get_address(business: Business) -> str:
    order = None
    if stripe_terminal_orders := StripeTerminalOrderingAdapter.get_orders(business_id=business.id):
        order = stripe_terminal_orders[0]

    if business.address:
        address = business.address

        if business.address2:
            address = f'{address} {business.address2}'[:100]
    elif business.address2:
        address = business.address2
    elif address1 := getattr(order, 'billing_address_line_1', None):
        address = address1

        if address2 := getattr(order, 'billing_address_line_2', None):
            address = f'{address} {address2}'[:100]
    else:
        address = ' '

    return address


def get_city(business):
    city = ' '
    if business.city:
        city = business.city
    elif business.region:
        city = business.region.get_parent_name_by_type(RegionType.CITY)
    return city or ' '


def get_subscription_buyer_data_from_business(business):
    email = business.owner.email

    if not email:
        order = None
        if stripe_terminal_orders := StripeTerminalOrderingAdapter.get_orders(
            business_id=business.id
        ):
            order = stripe_terminal_orders[0]
        email = order.email if order else ""

    return {
        'entity_name': business.name,
        'invoice_email': email,
    }


def get_gb_tax_groups():
    if settings.API_COUNTRY != Country.GB:
        return
    existing_tax_groups = {group.id: group for group in TaxGroup.objects.all()}

    tax_group_mapping = {
        "JE": existing_tax_groups.get(2),
        "GY": existing_tax_groups.get(3),
    }
    default_tax_group = existing_tax_groups.get(1)

    return tax_group_mapping, default_tax_group


@transaction.atomic
def create_buyer_for_business(business: Business, signer: User, metadata: dict) -> None:
    subscription_buyer_data = get_subscription_buyer_data_from_business(business)

    address = get_address(business)
    zipcode, zipcode_textual = get_zipcode(business)
    state = get_state(business, zipcode)
    city = get_city(business)

    invoice_address = InvoiceAddress.objects.create(
        address_details1=address,
        zipcode=zipcode,
        zipcode_textual=zipcode_textual,
        state=state,
        city=city,
    )
    subscription_buyer_data['invoice_address'] = invoice_address
    subscription_buyer_data['vat_registered'] = False
    subscription_buyer_data['is_verified'] = True
    subscription_buyer_data['verified_at'] = tznow()
    subscription_buyer_data['verified_by'] = signer
    if (
        NavisionDefaultTaxGroupFlag()
        and settings.NAVISION_USE_TAX_GROUPS
        and (country_tax_group := TaxGroup.objects.filter(region__type=RegionType.COUNTRY).first())
    ):
        subscription_buyer_data['tax_group'] = country_tax_group

    (subscription_buyer, created) = SubscriptionBuyer.objects.get_or_create(
        businesses__id=business.id,
        defaults=subscription_buyer_data,
    )

    if created:
        business.subscriptions.filter(buyer__isnull=True).update(buyer=subscription_buyer)

        business_old_vars = {'buyer_id': business.buyer_id}

        business.buyer = subscription_buyer
        business.save(update_fields=['buyer'])

        business_new_vars = {'buyer_id': business.buyer_id}

        BusinessChange.add(
            business,
            business_new_vars,
            business_old_vars,
            operator=signer,
            metadata=metadata,
        )
    else:
        raise BusinessMigrationError(
            f"Business (ID: {business.id}) modified in the middle of adding"
        )

    sync_buyer_with_merchant_task.delay(subscription_buyer.id)

    return subscription_buyer


def try_to_create_buyer_for_business(business: Business, user: 'User'):
    if business.is_test_business() and settings.NAVISION_EXCLUDE_TEST_BUSINESSES:
        logger.info('Skipping test business %s', business.id)
        return

    try:
        create_buyer_for_business(
            business, user, metadata={'Task': 'fill_subscription_buyer_data_task'}
        )
    except BusinessMigrationError:
        logger.exception(
            "Error occurred when creating SubscriptionBuyer for Business (ID: %s)",
            business.id,
        )
    except InsufficientDataError:
        logger.exception(
            "Insufficient data to create Merchant for Business (ID: %s)",
            business.id,
        )
    except Exception as err:  # pylint: disable=broad-except
        logger.exception(
            "Error occurred when creating SubscriptionBuyer for Business (ID: %s): %s",
            business.id,
            err,
        )


def business_invoice_address_diff(
    business: Business,
    subscription_buyer: SubscriptionBuyer,
):
    address = get_address(business)
    zipcode, zipcode_textual = get_zipcode(business)
    city = (
        business.city_or_region_city
    )  # in case of empty city we change nothing in invoice address
    state = get_state(business, zipcode)

    invoice_address = subscription_buyer.invoice_address

    if invoice_address is None:
        diffs = {
            'address_details1': address,
            'zipcode_textual': zipcode_textual,
        }

        if zipcode is not None:
            diffs['zipcode_id'] = zipcode.id
        if state is not None:
            diffs['state_id'] = state.id
        if city:
            diffs['city'] = city

        return diffs

    diffs = {}

    if address != invoice_address.address_details1:
        diffs['address_details1'] = address

    if zipcode is not None and zipcode.id != invoice_address.zipcode.id:
        diffs['zipcode_id'] = zipcode.id
    if zipcode_textual and zipcode_textual != invoice_address.zipcode_textual:
        diffs['zipcode_textual'] = zipcode_textual

    if city and city != invoice_address.city:
        diffs['city'] = city

    if state != invoice_address.state:
        if state is None:
            diffs['state_id'] = None
        else:
            diffs['state_id'] = state.id

    return diffs


def business_subscription_buyer_diff(business: Business, subscription_buyer: SubscriptionBuyer):
    subscription_buyer_data = get_subscription_buyer_data_from_business(business)
    diffs = {}

    for field in (
        'entity_name',
        'invoice_email',
    ):
        value = subscription_buyer_data.get(field)
        if subscription_buyer.entity_name != value:
            diffs[field] = value

    return diffs


@transaction.atomic
def update_subscription_buyer(subscription_buyer: SubscriptionBuyer):
    businesses = list(subscription_buyer.businesses.all())

    if len(businesses) == 0:
        logger.warning(
            "SubscriptionBuyer (ID: %s) does not own any business. Skipping update process.",
            subscription_buyer.id,
        )

    elif len(businesses) == 1:
        business = businesses[0]

        address_diff = business_invoice_address_diff(business, subscription_buyer)
        if 'zipcode_id' in address_diff or 'zipcode_textual' in address_diff:
            buyer_diff = business_subscription_buyer_diff(business, subscription_buyer)

            (invoice_address, created) = InvoiceAddress.objects.update_or_create(
                subscriptionbuyer__id=subscription_buyer.id,
                defaults=address_diff,
            )

            if created:
                buyer_diff['invoice_address_id'] = invoice_address.id

            if buyer_diff:
                SubscriptionBuyer.objects.filter(id=subscription_buyer.id).update(**buyer_diff)

            sync_buyer_with_merchant_task.delay(subscription_buyer.id)
        elif subscription_buyer.merchant is not None:
            subscription_buyer.merchant.updated = tznow()
            # Track when merchant was synced last time
            subscription_buyer.merchant.save(update_fields=['updated'])
    else:
        logger.warning(
            "SubscriptionBuyer (ID: %s) owns multiple businesses."
            "Skipping update process.",  # Should never happen on us
            subscription_buyer.id,
        )
