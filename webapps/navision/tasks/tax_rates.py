import logging

from datetime import timedelta, datetime

from django.conf import settings
from django.db import models, transaction
from django.db.models import Case, When, Value
from django.db.models.signals import post_save
from django.dispatch import receiver

from lib.celery_tools import celery_task
from lib.feature_flag.feature.navision import NavisionTaxRateClientV2Flag
from lib.tools import tznow
from webapps.navision.api_clients.navision_v1 import (
    NavisionTaxRateAPIClient as V1NavisionTaxRateAPIClient,
)
from webapps.navision.api_clients.navision_v2 import NavisionTaxRateAPIClient
from webapps.navision.decorators import navision_enabled
from webapps.navision.models.tax_rate import TaxRate
from webapps.navision.ports import TaxRateAPIData
from webapps.structure.models import Region

logger = logging.getLogger("booksy.navision")


def update_tax_rate_from_api_response(data_dict: dict[str, str]) -> None:
    api_data = TaxRateAPIData.from_api_response(data_dict)

    region = Region.objects.filter(type=Region.Type.ZIP, name=api_data.zip_code).first()
    if not region:
        logger.error('Zipcode %s does not exist in region table', api_data.zip_code)
        return

    for tax_item in api_data.items:
        TaxRate.update_or_create(
            region=region,
            service=tax_item.service_type,
            valid_from=api_data.last_modified,
            tax_rate=tax_item.tax_rate,
        )


@celery_task
@navision_enabled
def fetch_tax_rate_for_zipcode_task(zip_code: str):
    if not settings.NAVISION_TAX_CALCULATED_FROM_ZIPCODE:
        logger.error(
            'SalesTax api is not available outside of `us`. '
            'Aborting fetching of tax rate for zipcode: %s',
            zip_code,
        )

        return

    if TaxRate.current_objects.filter(
        region__type=Region.Type.ZIP,
        region__name=zip_code,
    ).exists():
        logger.info('TaxRate for zipcode - "%s" already exists', zip_code)
        return

    if NavisionTaxRateClientV2Flag():
        client = NavisionTaxRateAPIClient()
    else:
        client = V1NavisionTaxRateAPIClient()

    try:
        data_dict = client.get_sales_tax_for_zip(zip_code)

        if data_dict and data_dict['status'] == 'Success':
            update_tax_rate_from_api_response(data_dict)
        else:
            logger.critical(
                'Navision was unable to calculate tax rate for zip code: %s.',
                zip_code,
            )

    except Exception as err:  # pylint: disable=broad-except
        logger.error(
            'Exception occurred when requesting for tax rate for zip code: %s. Error: %s',
            zip_code,
            err,
        )
        raise


if settings.NAVISION_CHECK_TAX_RATE_API_ON_NEW_MERCHANT:

    @receiver(post_save, sender='purchase.InvoiceAddress')
    def fetch_tax_rate(sender, instance, **kwargs):  # pylint: disable=unused-argument
        fetch_tax_rate_for_zipcode_task.delay(instance.zipcode.name)


@celery_task(time_limit=16 * 60, soft_time_limit=15 * 60)
@navision_enabled
def update_tax_rates_task(modified_datetime: datetime | None = None):
    if not NavisionTaxRateClientV2Flag():
        return

    if not settings.NAVISION_TAX_CALCULATED_FROM_ZIPCODE:
        logger.info('Navision TaxRate API is available only for US. Aborting sync.')
        return

    client = NavisionTaxRateAPIClient()
    if not modified_datetime:
        modified_datetime = tznow() - timedelta(days=2)
    tax_rates = client.get_items_modified_since(modified_datetime)

    for tax_rate in tax_rates:
        try:
            tax_rate_data = TaxRateAPIData.from_api_response(tax_rate)
            region = Region.objects.filter(
                type=Region.Type.ZIP, name=tax_rate_data.zip_code
            ).first()

            if region is None:
                logger.error('Zipcode "%s" is not present in Region table', tax_rate_data.zip_code)
            else:
                for tax_item in tax_rate_data.items:
                    TaxRate.update_or_create(
                        tax_item.service_type,
                        tax_rate_data.last_modified,
                        tax_item.tax_rate,
                        region=region,
                    )
        except Exception as err:  # pylint: disable=broad-except
            logger.exception(
                'Exception occurred when updating/adding tax rate: (%s), error: %s',
                tax_rate,
                err,
            )
            raise


@celery_task
@navision_enabled
def fill_tax_rate_table_from_business_zipcode_task(offset_hours: int | None = 4):
    if not settings.NAVISION_ENABLE_TAX_RATE_INTEGRATION:
        return

    from webapps.business.models import Business
    from webapps.structure.enums import RegionType

    filters = {
        'status__in': [
            Business.Status.TRIAL,
            Business.Status.PAID,
            Business.Status.OVERDUE,
            Business.Status.BLOCKED_OVERDUE,
        ],
        'buyer__isnull': True,
    }

    if offset_hours is not None:
        filters.update({'updated__gte': tznow() - timedelta(hours=offset_hours)})

    biz_zipcodes = (
        Business.objects.filter(**filters)
        .annotate(
            region__name=Case(
                When(region__type=RegionType.ZIP, then='region__name'),
                default=Value(None),
                output_field=models.CharField(),
            )
        )
        .values_list('zipcode', 'region__name')
    )

    unique_biz_zipcodes = set(item for sublist in biz_zipcodes for item in sublist if item)

    zipcodes_from_tax_rates = set(
        TaxRate.current_objects.filter(
            region__name__in=unique_biz_zipcodes,
        ).values_list('region__name', flat=True)
    )

    zipcodes = unique_biz_zipcodes - zipcodes_from_tax_rates

    for zipcode in zipcodes:
        fetch_tax_rate_for_zipcode_task.delay(zipcode)


@celery_task
@navision_enabled
def force_update_tax_rate(zip_code: str):
    if NavisionTaxRateClientV2Flag():
        client = NavisionTaxRateAPIClient()
    else:
        client = V1NavisionTaxRateAPIClient()

    data_dict = client.get_sales_tax_for_zip(zip_code)
    if data_dict and data_dict['status'] == 'Success':
        try:
            api_data = TaxRateAPIData.from_api_response(data_dict)
            if not (
                region := Region.objects.filter(
                    type=Region.Type.ZIP, name=api_data.zip_code
                ).first()
            ):
                logger.error('Zipcode %s does not exist in region table', api_data.zip_code)
                return
            for tax_item in api_data.items:
                with transaction.atomic():
                    TaxRate.current_objects.filter(
                        region=region,
                        service=tax_item.service_type,
                    ).update(valid_to=tznow())
                    TaxRate.objects.create(
                        region=region,
                        service=tax_item.service_type,
                        valid_from=min(api_data.last_modified, tznow()),
                        tax_rate=tax_item.tax_rate,
                    )
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.error(
                'Unable to update tax rate for zipcode: %s. Error: %s', data_dict.get('code'), e
            )
            raise
    else:
        logger.error(
            'Navision was unable to calculate tax rate for zip code: %s.',
            zip_code,
        )


@celery_task(time_limit=16 * 60, soft_time_limit=15 * 60)
@navision_enabled
def update_tax_rate_per_state_task(state_abbrev: str):
    if not settings.NAVISION_TAX_CALCULATED_FROM_ZIPCODE:
        logger.info('Navision TaxRate API is available only for US. Aborting sync.')
        return

    client = NavisionTaxRateAPIClient()
    response = client.get_sales_tax_for_state(state_abbrev)
    if not response:
        logger.warning('No tax rate information available for %s state', state_abbrev)
        return
    for zipcode_tax_rate in response:
        update_tax_rate_from_api_response(zipcode_tax_rate)
