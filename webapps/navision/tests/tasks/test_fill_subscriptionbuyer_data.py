from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.test import TestCase, override_settings
from model_bakery import baker
from parameterized import parameterized

from lib.feature_flag.feature.navision import NavisionDefaultTaxGroupFlag
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from webapps.billing.models import BillingCycle
from webapps.business.baker_recipes import business_recipe
from webapps.business.models import Business
from webapps.business.models.business_change import BusinessChange
from webapps.navision.models import Merchant, TaxGroup
from webapps.purchase.models import (
    InvoiceAddress,
    SubscriptionBuyer,
)
from webapps.navision.tasks.subscription_buyer import fill_subscription_buyer_data_task
from webapps.stripe_terminal.models import Order
from webapps.structure.baker_recipes import usa_recipe
from webapps.structure.enums import RegionType
from webapps.structure.models import Region, RegionGraph, get_parents_of_region
from webapps.user.models import User


@patch(
    'webapps.navision.api_clients.navision_v1.NavisionMerchantAPIClient.create_or_update_merchant',
    MagicMock(side_effect=id),
)
class TestFillSubscriptionBuyerData(TestCase):
    def setUp(self) -> None:
        super().setUp()

        self.john = baker.make(User, first_name='John', last_name='Doe', email='<EMAIL>')

        self.usa = usa_recipe.make()

        self.florida = Region.objects.create(type=RegionType.STATE, name='Florida', abbrev='FL')
        RegionGraph.objects.create(region=self.usa, related_region=self.florida)

        self.largo = Region.objects.create(type=RegionType.CITY, name='Largo')
        RegionGraph.objects.create(region=self.florida, related_region=self.largo)

        self.largo_zip = Region.objects.create(type=RegionType.ZIP, name='33770')
        RegionGraph.objects.create(region=self.largo, related_region=self.largo_zip)

        get_parents_of_region.cache_clear()

    def tearDown(self) -> None:
        super().tearDown()
        get_parents_of_region.cache_clear()

    @staticmethod
    def _add_billing_cycle(business: Business, date_start: datetime):
        baker.make(
            BillingCycle,
            business=business,
            date_start=date_start,
            date_end=date_start + relativedelta(months=1),
        )

    @override_settings(NAVISION_USE_TAX_GROUPS=False)
    @override_eppo_feature_flag({NavisionDefaultTaxGroupFlag.flag_name: True})
    def test_migrate_regular_business(self):
        business = business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='Largo',
            invoice_address=None,
            owner=self.john,
            address='9 Street Richmond Heights',
            active=True,
        )
        baker.make(
            TaxGroup,
            name='usa',
            region=baker.make(Region, name='USA', type=RegionType.COUNTRY),
            accounting_group='domestic',
        )
        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        self.assertEqual(InvoiceAddress.objects.all().count(), 0)
        self.assertEqual(SubscriptionBuyer.objects.all().count(), 0)

        fill_subscription_buyer_data_task()

        self.assertEqual(InvoiceAddress.objects.all().count(), 1)
        self.assertEqual(SubscriptionBuyer.objects.all().count(), 1)

        buyer: SubscriptionBuyer = SubscriptionBuyer.objects.first()

        self.assertEqual(buyer.entity_name, business.name)
        self.assertEqual(buyer.invoice_email, self.john.email)

        self.assertEqual(buyer.invoice_address.address_details1, '9 Street Richmond Heights')
        self.assertEqual(buyer.invoice_address.state.id, self.florida.id)
        self.assertEqual(buyer.invoice_address.zipcode.id, self.largo_zip.id)
        self.assertEqual(buyer.invoice_address.city, self.largo.name)
        self.assertTrue(buyer.is_verified)
        self.assertEqual(buyer.verified_by.email, '<EMAIL>')
        self.assertFalse(buyer.vat_registered)
        self.assertIsNone(buyer.tax_group)

    @override_settings(NAVISION_USE_TAX_GROUPS=True)
    @override_eppo_feature_flag({NavisionDefaultTaxGroupFlag.flag_name: True})
    def test_assign_default_tax_group_based_on_country(self):
        business = business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='Largo',
            invoice_address=None,
            owner=self.john,
            address='9 Street Richmond Heights',
            active=True,
        )
        tax_group_country = baker.make(
            TaxGroup,
            name='uk',
            region=baker.make(Region, name='United Kingdom', type=RegionType.COUNTRY),
            accounting_group='domestic',
        )
        baker.make(
            TaxGroup,
            name='Guernsey',
            region=baker.make(Region, name='Guernsey', type=RegionType.CITY),
            accounting_group='domestic-gg',
        )
        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()

        buyer: SubscriptionBuyer = SubscriptionBuyer.objects.first()
        self.assertIsNotNone(buyer.tax_group)
        self.assertEqual(tax_group_country.id, buyer.tax_group.id)
        self.assertEqual(tax_group_country.accounting_group, buyer.merchant.accounting_group)

    @override_settings(NAVISION_USE_TAX_GROUPS=True)
    @override_eppo_feature_flag({NavisionDefaultTaxGroupFlag.flag_name: True})
    def test_no_country_tax_group(self):
        business = business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='Largo',
            invoice_address=None,
            owner=self.john,
            address='9 Street Richmond Heights',
            active=True,
        )
        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()

        buyer: SubscriptionBuyer = SubscriptionBuyer.objects.first()
        self.assertIsNone(buyer.tax_group)
        self.assertEqual('domestic', buyer.merchant.accounting_group)

    def test_business_without_region_but_with_zipcode_have_region_deduced(self):
        business = business_recipe.make(
            name='GameStop Corp.',
            region=None,
            zipcode=self.largo_zip.name,
            city='Largo',
            owner=self.john,
            address='9 Street Largo Hi',
            address2='12',
            active=True,
        )

        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()

        self.assertEqual(SubscriptionBuyer.objects.all().count(), 1)

        buyer: SubscriptionBuyer = SubscriptionBuyer.objects.first()

        self.assertEqual(buyer.invoice_address.address_details1, '9 Street Largo Hi 12')
        self.assertEqual(buyer.invoice_address.state.id, self.florida.id)
        self.assertEqual(buyer.invoice_address.zipcode.id, self.largo_zip.id)
        self.assertEqual(buyer.invoice_address.city, self.largo.name)
        self.assertFalse(buyer.vat_registered)

    def test_business_without_zipcode_and_region_is_not_migrated(self):
        business = business_recipe.make(
            name='GameStop Corp.',
            city='Largo',
            owner=self.john,
            zipcode=None,
            region=None,
            invoice_address=None,
            address='9 Street Richmond Heights',
            active=True,
        )

        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()

        self.assertEqual(InvoiceAddress.objects.all().count(), 0)
        self.assertEqual(SubscriptionBuyer.objects.all().count(), 0)

    def test_biz_without_zipcode_and_with_non_zipcode_region_is_not_migrated(
        self,
    ):
        business = business_recipe.make(
            name='GameStop Corp.',
            city='Largo',
            owner=self.john,
            zipcode=None,
            region=self.largo,
            invoice_address=None,
            address='9 Street Richmond Heights',
            active=True,
        )

        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()

        self.assertEqual(InvoiceAddress.objects.all().count(), 0)
        self.assertEqual(SubscriptionBuyer.objects.all().count(), 0)

    def test_migrated_subscription_buyer_is_verified(self):
        business = business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='Largo',
            owner=self.john,
            address='Hawaii Street',
            active=True,
        )

        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()

        self.assertEqual(SubscriptionBuyer.objects.all().count(), 1)

        buyer: SubscriptionBuyer = SubscriptionBuyer.objects.first()

        self.assertTrue(buyer.is_verified)
        self.assertFalse(buyer.vat_registered)
        self.assertEqual(buyer.verified_by.email, '<EMAIL>')

    def test_city_filled_if_given(self):
        business = business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='Las Testeras',
            owner=self.john,
            address='Hawaii Street',
            active=True,
        )

        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()
        business.refresh_from_db()

        self.assertIsNotNone(business.buyer)
        self.assertIsNotNone(business.buyer.merchant)
        self.assertEqual(business.buyer.invoice_address.city, 'Las Testeras')
        self.assertTrue(business.buyer.is_verified)
        self.assertEqual(business.buyer.verified_by.email, '<EMAIL>')
        self.assertEqual(business.buyer.merchant.city, 'Las Testeras')
        self.assertFalse(business.buyer.vat_registered)

    def test_city_filled_from_region_if_not_given(self):
        business = business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='',
            owner=self.john,
            address='Hawaii Street',
            active=True,
        )

        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()
        business.refresh_from_db()

        self.assertIsNotNone(business.buyer)
        self.assertIsNotNone(business.buyer.merchant)
        self.assertEqual(business.buyer.invoice_address.city, 'Largo')
        self.assertTrue(business.buyer.is_verified)
        self.assertEqual(business.buyer.verified_by.email, '<EMAIL>')
        self.assertEqual(business.buyer.merchant.city, 'Largo')
        self.assertFalse(business.buyer.vat_registered)

    @parameterized.expand(
        [
            ('Las Testeras', 'Las Testeras'),
            ('', ' '),
        ]
    )
    def test_business_with_no_region_has_city_or_empty_string_assigned(self, city, expected_city):
        business = business_recipe.make(
            name='GameStop Corp.',
            region=None,
            zipcode=self.largo_zip.name,
            city=city,
            owner=self.john,
            address='9 Street Largo Hi',
            address2='12',
            active=True,
        )

        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()
        business.refresh_from_db()

        self.assertIsNotNone(business.buyer)
        self.assertIsNotNone(business.buyer.merchant)
        self.assertEqual(business.buyer.invoice_address.city, expected_city)
        self.assertTrue(business.buyer.is_verified)
        self.assertEqual(business.buyer.verified_by.email, '<EMAIL>')
        self.assertEqual(business.buyer.merchant.city, expected_city)

    def test_buyer_change_is_noted_in_business_changes(self):
        business = business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='Largo',
            owner=self.john,
            address='Hawaii Street',
            active=True,
        )

        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()

        change = BusinessChange.objects.get(business_id=business.id)

        self.assertIn(
            'business.buyer_id',
            change.data,
            "Buyer data change should be noted in BusinessChange",
        )

    def test_businesses_without_subscriptions_are_not_migrated(self):
        business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='Largo',
            owner=self.john,
            address='Hawaii Street',
            active=True,
        )

        fill_subscription_buyer_data_task()

        self.assertEqual(SubscriptionBuyer.objects.all().count(), 0)

    def test_only_latest_payment_is_taken_in_filtering(self):
        business = business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='Largo',
            owner=self.john,
            address='Hawaii Street',
            active=True,
        )

        self._add_billing_cycle(business, tznow() - timedelta(days=90))
        self._add_billing_cycle(business, tznow() - timedelta(days=65))
        self._add_billing_cycle(business, tznow() - timedelta(days=45))
        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()

        self.assertEqual(SubscriptionBuyer.objects.all().count(), 1)

        business.refresh_from_db()

        self.assertIsNotNone(business.buyer)

    def test_new_merchant_is_created_for_all_subscription_buyers(self):
        business: Business = business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='Largo',
            owner=self.john,
            address='Hawaii Street',
            active=True,
        )

        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()

        business.refresh_from_db()
        self.assertIsNotNone(business.buyer)
        self.assertIsNotNone(business.buyer.merchant)

        merchant: Merchant = business.buyer.merchant
        self.assertEqual(merchant.address_details1, business.address)
        self.assertIsNone(merchant.tax_id)
        self.assertEqual(merchant.state, 'FL')
        self.assertEqual(merchant.zip_code, business.region.name)
        self.assertEqual(merchant.city, business.city)
        self.assertEqual(merchant.invoice_emails, [business.owner.email])
        self.assertEqual(merchant.country_code, settings.API_COUNTRY)

    def test_business_without_address_have_empty_string_assigned(self):
        business: Business = business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='Largo',
            owner=self.john,
            address=None,
            active=True,
        )

        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()

        business.refresh_from_db()
        self.assertIsNotNone(business.buyer)
        self.assertIsNotNone(business.buyer.merchant)
        self.assertEqual(business.buyer.invoice_address.address_details1, ' ')

        merchant: Merchant = business.buyer.merchant
        self.assertEqual(merchant.address_details1, ' ')

    @override_settings(CHECK_ZIPCODE_IN_REGION_TABLE=False)
    def test_country_with_zipcode_not_checked_in_region_table_with_zip_region(self):
        business: Business = business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='Largo',
            owner=self.john,
            address=None,
            active=True,
        )

        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()

        business.refresh_from_db()
        self.assertIsNotNone(business.buyer)
        self.assertIsNotNone(business.buyer.merchant)

        buyer = business.buyer
        merchant: Merchant = business.buyer.merchant

        self.assertEqual(buyer.invoice_address.zipcode_str, self.largo_zip.name)
        self.assertEqual(merchant.zip_code, self.largo_zip.name)

    @override_settings(CHECK_ZIPCODE_IN_REGION_TABLE=False)
    def test_country_with_zipcode_not_checked_in_region_table_with_zip_code(self):
        business: Business = business_recipe.make(
            name='GameStop Corp.',
            zipcode='33770',
            city='Largo',
            owner=self.john,
            address=None,
            active=True,
        )

        self._add_billing_cycle(business, tznow() - timedelta(days=30))

        fill_subscription_buyer_data_task()

        business.refresh_from_db()
        self.assertIsNotNone(business.buyer)
        self.assertIsNotNone(business.buyer.merchant)

        buyer = business.buyer
        merchant: Merchant = business.buyer.merchant

        self.assertEqual(buyer.invoice_address.zipcode_str, '33770')
        self.assertEqual(merchant.zip_code, '33770')

    def test_itunes_with_stripe_terminal_have_buyer_created(self):
        business: Business = business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='Largo',
            owner=self.john,
            address=None,
            active=True,
            payment_source=Business.PaymentSource.ITUNES,
            buyer=None,
        )

        baker.make(Order, business_id=business.id, cell_phone='123456789')

        fill_subscription_buyer_data_task()

        business.refresh_from_db()

        self.assertIsNotNone(business.buyer)
        self.assertIsNotNone(business.buyer.merchant)

    def test_itunes_with_stripe_terminal_have_buyer_created__missing_owner_data(self):
        self.john.last_name = ''
        self.john.email = ''
        self.john.save()

        business: Business = business_recipe.make(
            name='GameStop Corp.',
            region=self.largo_zip,
            city='Largo',
            owner=self.john,
            address=None,
            active=True,
            payment_source=Business.PaymentSource.ITUNES,
            buyer=None,
        )

        baker.make(
            Order,
            business_id=business.id,
            cell_phone='123456789',
            billing_first_name='Jan',
            billing_last_name='Kowalsky',
            email='<EMAIL>',
            billing_address_line_1='Sunrise blvd. 3',
            copy_shipping_to_billing=False,  # if True, shipping address is coppied to billing
        )

        fill_subscription_buyer_data_task()

        business.refresh_from_db()

        self.assertIsNotNone(business.buyer)
        self.assertIsNotNone(business.buyer.merchant)
        buyer = business.buyer
        self.assertEqual(buyer.invoice_email, '<EMAIL>')
        self.assertIsNotNone(buyer.invoice_address)
        self.assertEqual(buyer.invoice_address.address_details1, 'Sunrise blvd. 3')
        self.assertTrue(buyer.is_verified)
        self.assertEqual(buyer.verified_by.email, '<EMAIL>')
