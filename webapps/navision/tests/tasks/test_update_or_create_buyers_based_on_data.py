from django.test import TestCase, override_settings
from model_bakery import baker

from country_config import Country
from webapps.navision.models import TaxGroup
from webapps.navision.tasks.subscription_buyer import (
    create_buyers_based_on_data_task,
    update_buyers_based_on_data_task,
)
from webapps.purchase.models import SubscriptionBuyer, InvoiceAddress


class PrepareDataMixin(TestCase):
    @override_settings(API_COUNTRY=Country.GB, CHECK_ZIPCODE_IN_REGION_TABLE=False)
    def setUp(self):
        super().setUp()
        self.mainland = baker.make(TaxGroup, id=1, name='Mainland and Isle of Man')
        self.jersey = baker.make(TaxGroup, id=2, name='Jersey')
        self.guernsey = baker.make(TaxGroup, id=3, name='Guernsey')

        self.buyer_1 = baker.make(
            SubscriptionBuyer,
            entity_name='Random name',
            invoice_address=baker.make(InvoiceAddress),
            tax_group=None,
            tax_id=None,
        )
        self.buyer_2 = baker.make(
            SubscriptionBuyer,
            entity_name='Kozak name',
            invoice_address=baker.make(InvoiceAddress),
            tax_group=None,
            tax_id=None,
        )
        self.buyer_no_adyen_data = baker.make(
            SubscriptionBuyer,
            entity_name='Super name',
            invoice_address=baker.make(InvoiceAddress),
            tax_group=None,
            tax_id=None,
        )
        self.business_buyer_to_update_1 = baker.make('business.Business', buyer=self.buyer_1)
        self.business_buyer_to_update_2 = baker.make('business.Business', buyer=self.buyer_2)
        _business_same_buyer_to_update_2 = baker.make('business.Business', buyer=self.buyer_2)
        _business_no_adyen_data = baker.make('business.Business', buyer=self.buyer_no_adyen_data)
        self.business_no_buyer_1 = baker.make('business.Business', buyer=None)
        self.business_no_buyer_2 = baker.make('business.Business', buyer=None)

    def data(self) -> dict:
        return {
            f"{self.business_no_buyer_1.id}": {
                "address_details": "Dermont avenue 12",
                "zipcode": "BT36 4",
                "city": "Newtownabbey",
                "type": "INDIVIDUAL",
                "entity_name": "Kellyanne Okane",
                "email": "<EMAIL>",
            },
            f"{self.business_buyer_to_update_1.id}": {
                "address_details": "Browning Street Apartment 138",
                "zipcode": "JE16 8GZ",
                "city": "Birmingham",
                "type": "INDIVIDUAL",
                "entity_name": "Wiphawa Pharueang",
                "email": "<EMAIL>",
            },
            f"{self.business_buyer_to_update_2.id}": {
                "address_details": "Bredisholm Terrace 42",
                "zipcode": "GY69 7HS",
                "city": "Glasgow",
                "type": "INDIVIDUAL",
                "entity_name": "Rhiannon Drain",
                "email": "<EMAIL>",
            },
            f"{self.business_no_buyer_2.id}": {
                "address_details": "Hearsey Gardens 85",
                "zipcode": "GY17 0ES",
                "city": "Camberley",
                "type": "INDIVIDUAL",
                "entity_name": "Darren Wicks",
                "email": "<EMAIL>",
            },
        }


@override_settings(API_COUNTRY=Country.GB, CHECK_ZIPCODE_IN_REGION_TABLE=False)
class TestCreateBuyersBasedOnData(PrepareDataMixin):
    def test_create_buyers(self):
        with self.assertNumQueries(64):
            create_buyers_based_on_data_task(self.data())

        self.assertEqual(5, SubscriptionBuyer.objects.count())
        self.assertEqual(5, InvoiceAddress.objects.count())

        # two must be created
        self.business_no_buyer_1.refresh_from_db()
        self.assertEqual('Kellyanne Okane', self.business_no_buyer_1.buyer.entity_name)
        self.assertEqual('Newtownabbey', self.business_no_buyer_1.buyer.invoice_address.city)
        self.assertEqual(self.mainland, self.business_no_buyer_1.buyer.tax_group)

        self.business_no_buyer_2.refresh_from_db()
        self.assertEqual('Darren Wicks', self.business_no_buyer_2.buyer.entity_name)
        self.assertEqual('Camberley', self.business_no_buyer_2.buyer.invoice_address.city)
        self.assertEqual(self.guernsey, self.business_no_buyer_2.buyer.tax_group)

        # three unchanged
        self.buyer_1.refresh_from_db()
        self.assertEqual('Random name', self.buyer_1.entity_name)
        self.assertIsNone(self.buyer_1.tax_group)

        self.buyer_2.refresh_from_db()
        self.assertEqual('Kozak name', self.buyer_2.entity_name)

        self.buyer_no_adyen_data.refresh_from_db()
        self.assertEqual('Super name', self.buyer_no_adyen_data.entity_name)

        for buyer in SubscriptionBuyer.objects.all():
            self.assertFalse(buyer.vat_registered)


@override_settings(API_COUNTRY=Country.GB, CHECK_ZIPCODE_IN_REGION_TABLE=False)
class TestUpdateBuyerBasedOnData(PrepareDataMixin):
    def test_update_buyers(self):
        with self.assertNumQueries(40):
            update_buyers_based_on_data_task(self.data())

        self.assertEqual(3, SubscriptionBuyer.objects.count())
        self.assertEqual(3, InvoiceAddress.objects.count())

        # two must be updated
        self.buyer_1.refresh_from_db()
        self.assertEqual('Wiphawa Pharueang', self.buyer_1.entity_name)
        self.assertEqual('Birmingham', self.buyer_1.invoice_address.city)
        self.assertEqual(self.jersey, self.buyer_1.tax_group)

        self.buyer_2.refresh_from_db()
        self.assertEqual('Rhiannon Drain', self.buyer_2.entity_name)
        self.assertEqual('Glasgow', self.buyer_2.invoice_address.city)
        self.assertEqual(self.guernsey, self.buyer_2.tax_group)

        # three unchanged
        self.business_no_buyer_1.refresh_from_db()
        self.business_no_buyer_2.refresh_from_db()
        self.buyer_no_adyen_data.refresh_from_db()
        self.assertIsNone(self.business_no_buyer_1.buyer)
        self.assertIsNone(self.business_no_buyer_2.buyer)
        self.assertEqual('Super name', self.buyer_no_adyen_data.entity_name)
