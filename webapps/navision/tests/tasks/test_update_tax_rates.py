from datetime import timedelta, datetime, timezone
from decimal import Decimal
from unittest.mock import patch, MagicMock, call

from django.test import TestCase
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized

from lib.feature_flag.feature.navision import NavisionTaxRateClientV2Flag
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from webapps.business.baker_recipes import business_recipe
from webapps.business.models import Business
from webapps.navision.baker_recipes import navision_integration_enabled_recipe
from webapps.navision.models import TaxRate
from webapps.navision.tasks.tax_rates import (
    update_tax_rates_task,
    fetch_tax_rate_for_zipcode_task,
    fill_tax_rate_table_from_business_zipcode_task,
    force_update_tax_rate,
    update_tax_rate_per_state_task,
)
from webapps.purchase.models import SubscriptionBuyer
from webapps.structure.baker_recipes import bake_region_graphs
from webapps.structure.enums import RegionType
from webapps.structure.models import Region, get_parents_of_region

FROZEN_DATE_TIME = datetime(2023, 9, 1, tzinfo=timezone.utc)

_SALES_TAX_DATA = [
    {
        'code': '90002',
        'id': '85d9e9f0-b5cd-4fd7-81c1-d38c0f28d81d',
        'displayName': 'Los Angeles',
        'taxType': 'Sales Tax',
        'lastModifiedDateTime': tznow().isoformat(),  # v1
        'zip2TaxLastModifiedDateTime': tznow().isoformat(),  # v2
        'status': 'Success',
        'tax_items': [
            {
                'serviceType': 'SAAS',
                'taxRate': 9.5,
            },
            {
                'serviceType': 'BOOST',
                'taxRate': 9.5,
            },
        ],
    },
    {
        'code': '95376',
        'id': 'b3753e29-6776-4e40-bbd0-fd9ce8cad7ab',
        'displayName': 'San Joaquin',
        'taxType': 'Sales Tax',
        'lastModifiedDateTime': (tznow() - timedelta(days=365)).isoformat(),  # v1
        'zip2TaxLastModifiedDateTime': (tznow() - timedelta(days=365)).isoformat(),  # v2
        'status': 'Success',
        'tax_items': [
            {
                'serviceType': 'SAAS',
                'taxRate': 8.25,
            },
            {
                'serviceType': 'BOOST',
                'taxRate': 8.25,
            },
        ],
    },
    {
        'code': '98101',
        'id': 'b55854a4-9ecb-414c-ae3d-8d862c6f94d9',
        'displayName': 'King',
        'taxType': 'Sales Tax',
        'lastModifiedDateTime': tznow().isoformat(),  # v1
        'zip2TaxLastModifiedDateTime': tznow().isoformat(),  # v2
        'status': 'Failed',
        'tax_items': [
            {
                'serviceType': 'SAAS',
                'taxRate': 10.25,
            },
            {
                'serviceType': 'BOOST',
                'taxRate': 10.25,
            },
        ],
    },
    {
        'code': '99503',
        'id': '85d9e9f0-b5cd-4fd7-81c1-d38c0f28d81d',
        'displayName': 'Anchorage',
        'taxType': 'Sales Tax',
        'lastModifiedDateTime': '2021-05-05T15:12:48.267086+00:00',  # v1
        'zip2TaxLastModifiedDateTime': '2021-06-06T15:12:48.267086+00:00',  # v2
        'status': 'Success',
        'tax_items': [
            {
                'serviceType': 'SAAS',
                'taxRate': 0.0,
            },
            {
                "serviceType": 'BOOST',
                'taxRate': 0.0,
            },
        ],
    },
]


def get_sales_tax_rates_mock(_):
    return _SALES_TAX_DATA


def get_sales_tax_for_zip_mock(zipcode: str):
    data = [element for element in _SALES_TAX_DATA if element['code'] == zipcode]

    if len(data) == 1:
        return data[0]

    return {
        'code': zipcode,
        'id': '321326a4-63c0-4b0a-8a6b-6956545097d6',
        'displayName': '',
        'taxType': 'Sales Tax',
        'lastModifiedDateTime': '2021-05-05T15:12:48.79Z',  # v1
        'zip2TaxLastModifiedDateTime': '2021-06-06T15:12:48.79Z',  # v2
        'status': f'Insufficient input to specify a tax jurisdiction {zipcode}',
    }


@patch(
    'webapps.navision.api_clients.navision_v2.NavisionTaxRateAPIClient.get_items_modified_since',
    new=MagicMock(side_effect=get_sales_tax_rates_mock),
)
@override_eppo_feature_flag({NavisionTaxRateClientV2Flag.flag_name: True})
class TestUpdateTaxRates(TestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        navision_integration_enabled_recipe.make()

    def setUp(self) -> None:
        super().setUp()
        self.zip_90002 = baker.make(Region, type=RegionType.ZIP, name='90002')
        self.zip_95376 = baker.make(Region, type=RegionType.ZIP, name='95376')
        self.zip_98101 = baker.make(Region, type=RegionType.ZIP, name='98101')
        self.zip_99503 = baker.make(Region, type=RegionType.ZIP, name='99503')

    def tearDown(self) -> None:
        get_parents_of_region.cache_clear()
        super().tearDown()

    def test_non_existent_tax_rates_are_created(self):
        TaxRate.objects.create(
            service=TaxRate.Service.SAAS,
            region=self.zip_90002,
            tax_rate=Decimal('0.0700'),
            valid_from=tznow(),
        )
        TaxRate.objects.create(
            service=TaxRate.Service.BOOST,
            region=self.zip_90002,
            tax_rate=Decimal('0.0700'),
            valid_from=tznow(),
        )

        TaxRate.objects.create(
            service=TaxRate.Service.SAAS,
            region=self.zip_99503,
            tax_rate=Decimal('0.0700'),
            valid_from=tznow(),
        )

        TaxRate.objects.create(
            service=TaxRate.Service.BOOST,
            region=self.zip_99503,
            tax_rate=Decimal('0.0700'),
            valid_from=tznow(),
        )

        update_tax_rates_task()

        self.assertEqual(TaxRate.objects.count(), 8)

        tax_rate_98101 = TaxRate.current_objects.get(
            region=self.zip_98101, service=TaxRate.Service.BOOST
        )

        self.assertEqual(tax_rate_98101.tax_rate, Decimal('0.1025'))

        tax_rate_95376 = TaxRate.current_objects.get(
            region=self.zip_95376,
            service=TaxRate.Service.SAAS,
        )

        self.assertEqual(tax_rate_95376.tax_rate, Decimal('0.0825'))

    @parameterized.expand([None, datetime(2025, 5, 5, tzinfo=timezone.utc)])
    def test_outdated_tax_rates_have_valid_to_set_to_new_tax_rate_valid_from(
        self, modified_datetime
    ):
        old_tax_rate_90002 = TaxRate.objects.create(
            service=TaxRate.Service.SAAS,
            region=self.zip_90002,
            tax_rate=Decimal('0.0700'),
            valid_from=tznow() - timedelta(days=3),
        )

        old_tax_rate_95376 = TaxRate.objects.create(
            service=TaxRate.Service.SAAS,
            region=self.zip_95376,
            tax_rate=Decimal('0.0700'),
            valid_from=tznow() - timedelta(days=3),
        )

        update_tax_rates_task(modified_datetime)

        new_tax_rate_90002 = TaxRate.current_objects.get(
            region=self.zip_90002, service=TaxRate.Service.SAAS
        )
        old_tax_rate_90002.refresh_from_db()

        self.assertNotEqual(old_tax_rate_90002.id, new_tax_rate_90002.id)
        self.assertEqual(old_tax_rate_90002.valid_to, new_tax_rate_90002.valid_from)

        new_tax_rate_95376 = TaxRate.current_objects.get(
            region=self.zip_95376, service=TaxRate.Service.SAAS
        )
        old_tax_rate_95376.refresh_from_db()

        self.assertEqual(old_tax_rate_95376.id, new_tax_rate_95376.id)
        self.assertIsNone(new_tax_rate_95376.valid_to)


@patch(
    'webapps.navision.api_clients.navision_v1.NavisionTaxRateAPIClient.get_sales_tax_for_zip',
    new=MagicMock(side_effect=get_sales_tax_for_zip_mock),
)
class TestFetchTaxRateForZipCodeFlagOff(TestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        navision_integration_enabled_recipe.make()

    def setUp(self) -> None:
        super().setUp()
        self.zip_90002 = baker.make(Region, type=RegionType.ZIP, name='90002')
        self.zip_34125 = baker.make(Region, type=RegionType.ZIP, name='34125')
        self.zip_95376 = baker.make(Region, type=RegionType.ZIP, name='95376')

    def tearDown(self) -> None:
        get_parents_of_region.cache_clear()
        super().tearDown()

    def test_fetch_for_existing_zipcode_inserts_into_database(self):
        fetch_tax_rate_for_zipcode_task('90002')

        self.assertEqual(TaxRate.objects.count(), 2)
        self.assertEqual(TaxRate.current_objects.count(), 2)

        saas_tax_rate = TaxRate.find_by_region(self.zip_90002, TaxRate.Service.SAAS)
        self.assertEqual(saas_tax_rate.tax_rate, Decimal('0.0950'))

        boost_tax_rate = TaxRate.find_by_region(self.zip_90002, TaxRate.Service.BOOST)
        self.assertEqual(boost_tax_rate.tax_rate, Decimal('0.0950'))

    def test_fetch_for_zipcode_outside_navision_table_doesnt_insert_anything(
        self,
    ):
        fetch_tax_rate_for_zipcode_task('34125')

        self.assertFalse(TaxRate.objects.exists())

    def test_fetch_for_zipcode_outside_our_region_table_doesnt_insert_anything(
        self,
    ):
        fetch_tax_rate_for_zipcode_task('42311')

        self.assertFalse(TaxRate.objects.exists())

    def test_fetch_for_zipcode_ignores_tax_rates_with_old_last_modified_date(
        self,
    ):
        TaxRate.objects.create(
            region=self.zip_95376,
            service=TaxRate.Service.SAAS,
            tax_rate=Decimal('0.0950'),
            valid_from=tznow(),
        )

        TaxRate.objects.create(
            region=self.zip_95376,
            service=TaxRate.Service.BOOST,
            tax_rate=Decimal('0.1050'),
            valid_from=tznow(),
        )

        fetch_tax_rate_for_zipcode_task('95376')

        self.assertEqual(TaxRate.objects.count(), 2)
        self.assertEqual(TaxRate.current_objects.count(), 2)

    def test_valid_from_field(self):
        baker.make(Region, type=RegionType.ZIP, name='99503')
        fetch_tax_rate_for_zipcode_task('99503')
        self.assertEqual(TaxRate.objects.count(), 2)
        self.assertEqual(2, TaxRate.objects.filter(valid_from__date=datetime(2021, 5, 5)).count())


@patch(
    'webapps.navision.api_clients.navision_v2.NavisionTaxRateAPIClient.get_sales_tax_for_zip',
    new=MagicMock(side_effect=get_sales_tax_for_zip_mock),
)
@override_eppo_feature_flag({NavisionTaxRateClientV2Flag.flag_name: True})
class TestFetchTaxRateForZipCode(TestFetchTaxRateForZipCodeFlagOff):
    def test_valid_from_field(self):
        baker.make(Region, type=RegionType.ZIP, name='99503')
        fetch_tax_rate_for_zipcode_task('99503')
        self.assertEqual(TaxRate.objects.count(), 2)
        self.assertEqual(2, TaxRate.objects.filter(valid_from__date=datetime(2021, 6, 6)).count())


@patch('webapps.navision.tasks.tax_rates.fetch_tax_rate_for_zipcode_task.delay')
class TestUpdateTaxRateModelFromBusinessZipcode(TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        navision_integration_enabled_recipe.make()
        cls.zip_90002 = baker.make(Region, type=RegionType.ZIP, name='90002')
        cls.zip_34125 = baker.make(Region, type=RegionType.ZIP, name='34125')
        cls.zip_95376 = baker.make(Region, type=RegionType.ZIP, name='95376')

    def test_city_as_region_name(self, zipcode_task_mock):
        region_clute = baker.make(Region, type=RegionType.CITY, name='Clute')

        business_recipe.make(
            zipcode='90002',
            region=region_clute,
            buyer=None,
            status=Business.Status.TRIAL,
        )

        fill_tax_rate_table_from_business_zipcode_task()

        self.assertEqual(1, zipcode_task_mock.call_count)
        zipcode_task_mock.assert_has_calls([call('90002')])  # ok, no Clute region in mock

    @parameterized.expand([(4, 0, []), (None, 1, [call('95376')])])
    def test_query_get_business_based_on_offset_and_updated_field(
        self, zipcode_task_mock, offset_hours, expected_cnt_fnc_calls, expected_args_fnc_calls
    ):
        with freeze_time(tznow() - timedelta(hours=7)):
            business_recipe.make(
                zipcode=None,
                region=self.zip_95376,  # zipcode ok
                buyer=None,
                status=Business.Status.TRIAL,
            )
        with freeze_time(tznow() - timedelta(hours=2)):
            business_recipe.make(
                zipcode='90002',
                region=self.zip_90002,
                buyer=baker.make(SubscriptionBuyer),  # buyer exists
                status=Business.Status.TRIAL,
            )
            business_recipe.make(
                zipcode='90002',
                region=self.zip_90002,
                buyer=None,
                status=Business.Status.VENUE,  # improper status
            )

        fill_tax_rate_table_from_business_zipcode_task(offset_hours)
        self.assertEqual(expected_cnt_fnc_calls, zipcode_task_mock.call_count)
        self.assertEqual(expected_args_fnc_calls, zipcode_task_mock.call_args_list)

    def test_create_new_tax_rates_based_on_zipcode_and_region_name(
        self,
        zipcode_task_mock,
    ):
        business_recipe.make(
            zipcode='12345',  # no region
            region=self.zip_95376,
            buyer=None,
            status=Business.Status.TRIAL,
        )
        business_recipe.make(
            zipcode='90002',  # zipcode == region.name
            region=self.zip_90002,
            status=Business.Status.TRIAL,
            buyer=None,
        )

        fill_tax_rate_table_from_business_zipcode_task()

        self.assertEqual(3, zipcode_task_mock.call_count)
        zipcode_task_mock.assert_has_calls(
            [call('90002'), call('95376'), call('12345')], any_order=True
        )

    @parameterized.expand([(None, 0), (tznow() - timedelta(days=1), 1)])
    def test_zipcodes_with_existing_tax_rates(
        self, zipcode_task_mock, valid_to, expected_cnt_calls
    ):
        baker.make(
            TaxRate,
            service=TaxRate.Service.SAAS,
            region=self.zip_90002,
            tax_rate=Decimal('0.0700'),
            valid_from=tznow(),
            valid_to=valid_to,
        )
        baker.make(
            TaxRate,
            service=TaxRate.Service.BOOST,
            region=self.zip_90002,
            tax_rate=Decimal('0.0700'),
            valid_from=tznow(),
            valid_to=valid_to,
        )

        business_recipe.make(
            zipcode='90002',  # zipcode == region.name
            region=self.zip_90002,
            status=Business.Status.TRIAL,
            buyer=None,
        )

        fill_tax_rate_table_from_business_zipcode_task()
        self.assertEqual(expected_cnt_calls, zipcode_task_mock.call_count)


@patch(
    'webapps.navision.api_clients.navision_v1.NavisionTaxRateAPIClient.get_sales_tax_for_zip',
    new=MagicMock(side_effect=get_sales_tax_for_zip_mock),
)
@freeze_time(FROZEN_DATE_TIME)
class TestForceUpdateTaxRate(TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        navision_integration_enabled_recipe.make()
        cls.zip_90002 = baker.make(Region, type=RegionType.ZIP, name='90002')
        cls.zip_34125 = baker.make(Region, type=RegionType.ZIP, name='34125')

    def test_task_creates_new_tax_rates(self):
        self.assertEqual(TaxRate.objects.count(), 0)
        force_update_tax_rate('90002')
        self.assertEqual(TaxRate.objects.count(), 2)  # SAAS + BOOST

    def test_task_invalidates_existing_rates(self):
        self.assertEqual(TaxRate.objects.count(), 0)
        saas_tr = baker.make(
            TaxRate,
            service=TaxRate.Service.SAAS,
            region=self.zip_90002,
            tax_rate=Decimal('0.0700'),
            valid_from=tznow(),
            valid_to=None,
        )
        boost_tr = baker.make(
            TaxRate,
            service=TaxRate.Service.BOOST,
            region=self.zip_90002,
            tax_rate=Decimal('0.0700'),
            valid_from=tznow(),
            valid_to=None,
        )
        force_update_tax_rate('90002')
        saas_tr.refresh_from_db()
        boost_tr.refresh_from_db()
        self.assertEqual(saas_tr.valid_to, FROZEN_DATE_TIME)
        self.assertEqual(boost_tr.valid_to, FROZEN_DATE_TIME)
        self.assertEqual(
            TaxRate.objects.filter(region=self.zip_90002, valid_to__isnull=True).count(), 2
        )  # SAAS + BOOST
        self.assertEqual(
            TaxRate.current_objects.filter(
                region=self.zip_90002, tax_rate=Decimal('0.0950')
            ).count(),
            2,
        )

    def test_region_does_not_exists(self):
        self.assertEqual(TaxRate.objects.count(), 0)
        force_update_tax_rate('77777')
        self.assertEqual(TaxRate.objects.count(), 0)

    def test_zipcode_does_not_exists_in_api(self):
        self.assertEqual(TaxRate.objects.count(), 0)
        force_update_tax_rate('34125')
        self.assertEqual(TaxRate.objects.count(), 0)


class TestUpdateTaxRatePerState(TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        navision_integration_enabled_recipe.make()

    def tearDown(self) -> None:
        super().tearDown()
        get_parents_of_region.cache_clear()

    @patch(
        'webapps.navision.api_clients.navision_v2.NavisionTaxRateAPIClient.get_sales_tax_for_state',
        new=MagicMock(side_effect=get_sales_tax_rates_mock),
    )
    def test_update_tax_rates(self):
        oklahoma = baker.make(Region, type=RegionType.STATE, name='Oklahoma', abbrev='OK')
        grant = baker.make(Region, type=RegionType.COUNTY, name='Grant County')
        wakita = baker.make(Region, type=RegionType.CITY, name='Wakita')
        zip_90002 = baker.make(Region, type=RegionType.ZIP, name='90002')
        pond_creek = baker.make(Region, type=RegionType.CITY, name='Pond Creek')
        zip_95376 = baker.make(Region, type=RegionType.ZIP, name='95376')
        bake_region_graphs(oklahoma, grant, wakita, zip_90002)
        bake_region_graphs(grant, pond_creek, zip_95376)

        update_tax_rate_per_state_task(oklahoma.abbrev)

        self.assertEqual(4, TaxRate.objects.count())
        self.assertEqual(2, TaxRate.objects.filter(region=zip_90002).count())
        self.assertEqual(2, TaxRate.objects.filter(region=zip_95376).count())

    @patch('webapps.navision.tasks.tax_rates.update_tax_rate_from_api_response')
    @patch(
        'webapps.navision.api_clients.navision_v2.'
        'NavisionTaxRateAPIClient.get_sales_tax_for_state',
        return_value=[],
    )
    def test_usage(self, mocked_client, _):
        baker.make(Region, type=RegionType.STATE, name='Oklahoma', abbrev='OK')
        baker.make(Region, type=RegionType.STATE, name='Alaska', abbrev='AK')
        baker.make(Region, type=RegionType.STATE, name='Colorado', abbrev='CO')
        baker.make(Region, type=RegionType.STATE, name='Florida', abbrev='FL')
        baker.make(Region, type=RegionType.CITY, name='New York')

        states = Region.objects.filter(type=RegionType.STATE)
        for state in states:
            update_tax_rate_per_state_task.delay(state.abbrev)
        self.assertEqual(4, mocked_client.call_count)
        mocked_client.assert_has_calls(
            [call('OK'), call('AK'), call('CO'), call('FL')], any_order=True
        )
