# pylint: disable=protected-access, use-dict-literal
import random
from dataclasses import asdict
from datetime import date, datetime
from decimal import Decimal
from unittest import mock
from uuid import uuid4

import pytest
from dateutil.relativedelta import relativedelta
from django.test import TestCase, override_settings
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized, param
from pytz import UTC

from country_config import Country
from lib.feature_flag.feature.navision import UseNavisionMerchantAPIClientV2Flag
from lib.tests.utils import override_eppo_feature_flag

from lib.tools import tznow
from service.billing.tests import gen_func
from webapps.business.models import Business
from webapps.navision.enums import InvoicePaymentSource, InvoiceService
from webapps.navision.models import Merchant
from webapps.navision.models.invoice import Invoice, InvoiceItem
from webapps.navision.ports import (
    InsufficientDataError,
    InvoiceData,
    InvoiceItemAPIData,
    InvoiceNavisionAPIBase,
    InvoiceNavisionAPIData,
    InvoiceUpdatedNavisionAPIData,
    MerchantData,
    MerchantNavisionAPIData,
)
from webapps.purchase.models import SubscriptionBuyer
from webapps.structure.models import Region

baker.generators.add('lib.interval.fields.IntervalField', gen_func)


class TestMerchantData(TestCase):
    def test_invoice_emails(self):
        data = MerchantData(
            country_code='pl',
            emails='<EMAIL>',
            tax_id='123',
            entity_name='Company',
        )
        self.assertEqual(data.invoice_emails, ['<EMAIL>'])

        data = MerchantData(
            country_code='pl',
            emails=['<EMAIL>', '<EMAIL>'],
            tax_id='123',
            entity_name='Company',
        )
        self.assertEqual(
            data.invoice_emails,
            ['<EMAIL>', '<EMAIL>'],
        )

    def test_tax_merchant_with_incomplete_data(self):
        for field in ['emails', 'country_code']:
            data = {
                'emails': ['<EMAIL>'],
                'tax_id': '9512381607',
                'country_code': 'pl',
            }
            data[field] = None

            with pytest.raises(InsufficientDataError) as exc_info:
                MerchantData(**data)

            assert f'\'{field}\' is required for a tax Merchant.' in str(exc_info.value)

    def test_create_non_tax_merchant_with_incomplete_data(self):
        for field in [
            'emails',
            'address_details1',
            'city',
            'zip_code',
            'country_code',
        ]:
            data = {
                'emails': ['<EMAIL>'],
                'address_details1': 'Adres',
                'city': 'Warszawa',
                'zip_code': '01-100',
                'country_code': 'pl',
            }
            data[field] = None

            with pytest.raises(InsufficientDataError) as exc_info:
                MerchantData(**data)

            assert f'\'{field}\' is required for a non-tax Merchant.' in str(exc_info.value)

    def test_merchant_entity_name_cannot_be_empty_string(self):
        data = {
            'emails': ['<EMAIL>'],
            'tax_id': '9512381607',
            'country_code': 'pl',
            'entity_name': '  ',
        }

        with pytest.raises(InsufficientDataError) as exc_info:
            MerchantData(**data)

        assert 'Entity name is required.' in str(exc_info.value)

    def test_entity_name_when_company(self):
        data = MerchantData(
            country_code='pl',
            tax_id='123',
            emails='<EMAIL>',
            entity_name='Company Ltd.',
        )

        self.assertEqual(data.entity_name, 'Company Ltd.')

    def test_entity_name_when_private_person(self):
        data = MerchantData(
            country_code='pl',
            emails='<EMAIL>',
            entity_name='Private',
            address_details1='Address',
            city='Warszawa',
            zip_code='11-111',
        )

        self.assertEqual(data.entity_name, 'Private')

    def test_structure(self):
        data = asdict(
            MerchantData(
                country_code='pl', tax_id='123', emails='<EMAIL>', entity_name='Company'
            )
        )

        self.assertEqual(len(data), 10)

        for field in [
            'tax_id',
            'address_details1',
            'city',
            'zip_code',
            'state',
            'country_code',
            'invoice_emails',
            'entity_name',
            'payment_due_days',
            'accounting_group',
        ]:
            self.assertIn(field, data)


class TestMerchantNavisionAPIData(TestCase):
    def test_get_billing_address(self):
        data = {
            'merchant_id': 'BC-PL-100003',
            'entity_name': 'Nazwa biznesu',
            'invoice_emails': ['<EMAIL>'],
            'accounting_group': 'domestic',
            'tax_id': None,
            'price_with_vat': True,
            'address_details1': 'Adres',
            'city': 'Warszawa',
            'zip_code': '01-100',
            'state': 'mazowieckie',
            'country_code': 'PL',
        }

        billing_address = MerchantNavisionAPIData(**data).get_billing_address()
        self.assertDictEqual(
            billing_address,
            {
                'address_details1': 'Adres',
                'city': 'Warszawa',
                'zip_code': '01-100',
                'state': 'mazowieckie',
                'country_code': 'PL',
            },
        )

    def test_country_code_and_accounting_group_as_uppercase(self):
        data = {
            'merchant_id': 'BC-PL-100003',
            'entity_name': 'Nazwa biznesu',
            'invoice_emails': ['<EMAIL>'],
            'accounting_group': 'domestic',
            'tax_id': None,
            'price_with_vat': True,
            'address_details1': 'Adres',
            'city': 'Warszawa',
            'zip_code': '01-100',
            'state': 'mazowieckie',
            'country_code': 'pl',
        }

        payload = MerchantNavisionAPIData(**data).payload
        self.assertDictEqual(
            payload,
            {
                'merchant_id': 'BC-PL-100003',
                'business_name': 'Nazwa biznesu',
                'email': '<EMAIL>',
                'accounting_group': 'DOMESTIC',
                'tax_id': None,
                'price_with_vat': True,
                'payment_terms_code': '7',
                'billingAddress': {
                    'address_details1': 'Adres',
                    'city': 'Warszawa',
                    'zip_code': '01-100',
                    'state': 'mazowieckie',
                    'country_code': 'PL',
                },
            },
        )

    def test_get_billing_address_when_is_incomplete(self):
        for field in [
            'address_details1',
            'zip_code',
            'country_code',
            'city',
        ]:
            data = {
                'merchant_id': 'BC-PL-100003',
                'entity_name': 'Nazwa biznesu',
                'invoice_emails': ['<EMAIL>'],
                'accounting_group': 'domestic',
                'tax_id': None,
                'price_with_vat': True,
                'address_details1': 'Adres',
                'city': 'Warszawa',
                'zip_code': '01-100',
                'state': 'mazowieckie',
                'country_code': 'pl',
            }

            data[field] = None

            self.assertFalse(MerchantNavisionAPIData(**data).get_billing_address())

    def test_get_billing_address_cuts_city(self):
        data = {
            'merchant_id': 'BC-PL-100003',
            'entity_name': 'Nazwa biznesu',
            'invoice_emails': ['<EMAIL>'],
            'accounting_group': 'domestic',
            'tax_id': None,
            'price_with_vat': True,
            'address_details1': 'Adres',
            'city': '💈C' * 40,
            'zip_code': '01-100',
            'state': 'mazowieckie',
            'country_code': 'PL',
        }

        billing_address = MerchantNavisionAPIData(**data).get_billing_address()
        self.assertDictEqual(
            billing_address,
            {
                'address_details1': 'Adres',
                'city': 'C' * 30,
                'zip_code': '01-100',
                'state': 'mazowieckie',
                'country_code': 'PL',
            },
        )

    def test_payload_with_billing_address(self):
        data = {
            'merchant_id': 'BC-PL-100003',
            'entity_name': 'Nazwa biznesu',
            'invoice_emails': ['<EMAIL>', '<EMAIL>'],
            'accounting_group': 'domestic',
            'tax_id': None,
            'price_with_vat': True,
            'address_details1': 'Adres',
            'city': 'Warszawa',
            'zip_code': '01-100',
            'state': 'mazowieckie',
            'country_code': 'pl',
        }

        payload = MerchantNavisionAPIData(**data).payload

        self.assertEqual(payload['email'], '<EMAIL>; <EMAIL>')
        self.assertEqual(len(payload), 8)
        self.assertIn('billingAddress', payload)

    def test_payload_without_billing_address(self):
        data = {
            'merchant_id': 'BC-PL-100003',
            'entity_name': 'Nazwa biznesu',
            'invoice_emails': ['<EMAIL>', '<EMAIL>'],
            'accounting_group': 'domestic',
            'tax_id': None,
            'price_with_vat': True,
            'address_details1': 'Adres',
            'city': None,
            'zip_code': '01-100,',
            'state': 'mazowieckie',
            'country_code': 'pl',
        }

        payload = MerchantNavisionAPIData(**data).payload

        self.assertEqual(len(payload), 7)
        self.assertNotIn('billingAddress', payload)

    def test_business_ids_are_added_to_payload_if_supported(self):
        data = {
            'merchant_id': 'BC-PL-100003',
            'entity_name': 'Nazwa biznesu',
            'invoice_emails': ['<EMAIL>', '<EMAIL>'],
            'accounting_group': 'domestic',
            'tax_id': None,
            'price_with_vat': True,
            'address_details1': 'Adres',
            'city': 'Warszawa',
            'zip_code': '01-100',
            'state': 'mazowieckie',
            'country_code': 'pl',
            'owned_businesses': [1234, 5678, 9],
            'payment_due_days': 7,
        }

        with override_settings(NAVISION_MERCHANT_BUSINESS_ID_SUPPORTED=False):
            payload = MerchantNavisionAPIData(**data).payload

            self.assertDictEqual(
                payload,
                {
                    'accounting_group': 'DOMESTIC',
                    'business_name': 'Nazwa biznesu',
                    'email': '<EMAIL>; <EMAIL>',
                    'merchant_id': 'BC-PL-100003',
                    'price_with_vat': True,
                    'tax_id': None,
                    'payment_terms_code': '7',
                    'billingAddress': {
                        'address_details1': 'Adres',
                        'city': 'Warszawa',
                        'country_code': 'PL',
                        'state': 'mazowieckie',
                        'zip_code': '01-100',
                    },
                },
            )

        with override_settings(NAVISION_MERCHANT_BUSINESS_ID_SUPPORTED=True):
            payload = MerchantNavisionAPIData(**data).payload

            self.assertDictEqual(
                payload,
                {
                    'accounting_group': 'DOMESTIC',
                    'business_name': 'Nazwa biznesu',
                    'email': '<EMAIL>; <EMAIL>',
                    'merchant_id': 'BC-PL-100003',
                    'business_id': '1234 5678 9',
                    'price_with_vat': True,
                    'tax_id': None,
                    'payment_terms_code': '7',
                    'billingAddress': {
                        'address_details1': 'Adres',
                        'city': 'Warszawa',
                        'country_code': 'PL',
                        'state': 'mazowieckie',
                        'zip_code': '01-100',
                    },
                },
            )

    def test_business_ids_are_omitted_if_too_many(self):
        businesses = [random.randrange(1, 50, 1) for i in range(254)]
        data = {
            'merchant_id': 'BC-PL-100003',
            'entity_name': 'Nazwa biznesu',
            'invoice_emails': ['<EMAIL>', '<EMAIL>'],
            'accounting_group': 'domestic',
            'tax_id': None,
            'price_with_vat': True,
            'address_details1': 'Adres',
            'city': 'Warszawa',
            'zip_code': '01-100',
            'state': 'mazowieckie',
            'country_code': 'pl',
            'owned_businesses': businesses,
            'payment_due_days': 7,
        }

        with override_settings(NAVISION_MERCHANT_BUSINESS_ID_SUPPORTED=True):
            payload = MerchantNavisionAPIData(**data).payload

            self.assertDictEqual(
                payload,
                {
                    'accounting_group': 'DOMESTIC',
                    'business_name': 'Nazwa biznesu',
                    'email': '<EMAIL>; <EMAIL>',
                    'merchant_id': 'BC-PL-100003',
                    'business_id': '',
                    'price_with_vat': True,
                    'tax_id': None,
                    'payment_terms_code': '7',
                    'billingAddress': {
                        'address_details1': 'Adres',
                        'city': 'Warszawa',
                        'country_code': 'PL',
                        'state': 'mazowieckie',
                        'zip_code': '01-100',
                    },
                },
            )

    @parameterized.expand([(False,), (True,)])
    @override_settings(NAVISION_FLAT_MERCHANT_BILLING_ADDRESS=False)
    def test_api_country_code_support(self, country_code_supported):
        merchant = baker.make(
            Merchant,
            entity_name='Nazwa biznesu',
            invoice_emails=['<EMAIL>', '<EMAIL>'],
            accounting_group='domestic',
            tax_id=None,
            price_with_vat=True,
            address_details1='Adres',
            city='warszawka',
            zip_code='01-100',
            state='mazowieckie',
            country_code='pl',
        )

        with override_settings(NAVISION_API_COUNTRY_CODE_SUPPORTED=country_code_supported):
            payload = MerchantNavisionAPIData.from_merchant(merchant).payload
        self.assertEqual(country_code_supported, 'country_code' in payload['billingAddress'])


class TestInvoiceData(TestCase):
    def test_invoice_emails(self):
        data = InvoiceData(
            merchant_id=0,
            payment_due_date=date(2021, 1, 2),
            sales_date=date(2021, 1, 2),
            source='offline',
            currency='USD',
            document_type='VAT',
            invoice_date=date(2021, 1, 2),
            invoice_items=[],
        )
        self.assertIsNone(data.invoice_emails)

        data = InvoiceData(
            merchant_id=0,
            payment_due_date=date(2021, 1, 2),
            sales_date=date(2021, 1, 2),
            source='offline',
            currency='USD',
            document_type='VAT',
            invoice_date=date(2021, 1, 2),
            emails='<EMAIL>',
            invoice_items=[],
        )
        self.assertEqual(data.invoice_emails, ['<EMAIL>'])

        data = InvoiceData(
            merchant_id=0,
            payment_due_date=date(2021, 1, 2),
            sales_date=date(2021, 1, 2),
            source='offline',
            currency='USD',
            document_type='VAT',
            invoice_date=date(2021, 1, 2),
            emails=['<EMAIL>', '<EMAIL>'],
            invoice_items=[],
        )
        self.assertEqual(
            data.invoice_emails,
            ['<EMAIL>', '<EMAIL>'],
        )


class TestInvoiceAPIData(TestCase):
    def test_cast_to_float(self):
        self.assertEqual(InvoiceNavisionAPIBase._cast_to_float(''), '')
        self.assertIsNone(InvoiceNavisionAPIBase._cast_to_float(None))
        self.assertEqual(InvoiceNavisionAPIBase._cast_to_float(1), 1)
        self.assertEqual(InvoiceNavisionAPIBase._cast_to_float('1'), 1.0)
        self.assertEqual(InvoiceNavisionAPIBase._cast_to_float('1.1'), 1.1)
        self.assertEqual(InvoiceNavisionAPIBase._cast_to_float(Decimal(1)), 1.0)
        self.assertEqual(InvoiceNavisionAPIBase._cast_to_float(Decimal('1.1')), 1.1)
        self.assertEqual(InvoiceNavisionAPIBase._cast_to_float(Decimal('1.12345')), 1.12345)

    def test_dt_iso(self):
        self.assertEqual(InvoiceNavisionAPIBase._dt_iso(''), '')
        self.assertIsNone(InvoiceNavisionAPIBase._dt_iso(None))
        self.assertEqual(InvoiceNavisionAPIBase._dt_iso(date(2020, 1, 1)), '2020-01-01')
        self.assertEqual(
            InvoiceNavisionAPIBase._dt_iso(
                tznow().replace(
                    year=2020,
                    month=1,
                    day=1,
                    hour=12,
                    minute=12,
                    second=12,
                    microsecond=12,
                )
            ),
            '2020-01-01T12:12:12.000012+00:00',
        )


class TestInvoiceNavisionAPIData(TestCase):
    @override_settings(
        API_COUNTRY=Country.US,
        NAVISION_SALES_DATE_SUPPORTED=False,
        NAVISION_BANK_CODE_SUPPORTED=False,
    )
    def test_payload__us_missing_invoice_details(self):
        now = tznow()

        input_data = dict(
            merchant_id='BC-US-100003',
            booksy_id='BC-US-2020-01-**********',
            payment_due_date=date(2020, 1, 1),
            business_name='Business 1',
            business_id=1,
            sales_date=date(2020, 1, 2),
            source=InvoicePaymentSource.OFFLINE,
            service=InvoiceService.SAAS,
            currency='USD',
            document_type='VAT',
            invoice_date=date(2020, 1, 3),
            invoice_note='Uwagi ...',
            aggregation_uuid=None,
            aggregation_total=None,
            emails=['<EMAIL>', '<EMAIL>'],
            invoice_items=[
                InvoiceItemAPIData(
                    booksy_item_id='US-123',
                    product='Example SaaS service line',
                    service=InvoiceService.SAAS,
                    charge_completed=True,
                    base_gross_value=Decimal(10.34),
                    billing_cycle_end=now,
                    billing_cycle_start=now,
                    charging_dt=now,
                    transaction_id='*********',
                    transfer_dt=now,
                ),
            ],
        )
        data = InvoiceNavisionAPIData(**input_data)

        payload = data.payload
        invoice_item = payload['invoice_items'][0]
        del payload['invoice_items']

        self.assertDictEqual(
            payload,
            {
                'sales_tax_date': None,
                'merchant_id': 'BC-US-100003',
                'booksy_id': 'BC-US-2020-01-**********',
                'business_id': '1',
                'business_name': 'Business 1',
                'payment_due_date': date(2020, 1, 1).isoformat(),
                'source': InvoicePaymentSource.OFFLINE,
                'currency': 'USD',
                'service_type': InvoiceService.SAAS,
                'document_type': 'VAT',
                'invoice_date': date(2020, 1, 3).isoformat(),
                'email': '<EMAIL>; <EMAIL>',
            },
        )

        self.assertDictEqual(
            invoice_item,
            {
                'tax_area_code': None,
                'booksy_item_id': 'US-123',
                'base_gross_value': 10.34,
                'quantity': 1,
                'billing_cycle_end': now.isoformat(),
                'billing_cycle_start': now.isoformat(),
                'charging_dt': now.isoformat(),
                'product': 'Example SaaS service line',
                'service': InvoiceService.SAAS,
                'charge_completed': True,
                'transaction_id': '*********',
                'transfer_dt': now.isoformat(),
                'country_code': 'US',
            },
        )

        input_data['emails'] = None

        data = InvoiceNavisionAPIData(**input_data)
        self.assertIsNone(data.payload['email'])

    @override_settings(NAVISION_AGGREGATE_INVOICES=True)
    @pytest.mark.django_db
    def test_aggregations_are_added_to_payload(self):
        aggregation_uuid = uuid4()

        invoice1 = baker.make(
            Invoice,
            aggregation_uuid=aggregation_uuid,
        )

        invoice2 = baker.make(
            Invoice,
            aggregation_uuid=aggregation_uuid,
        )

        invoice1_payload = InvoiceNavisionAPIData.from_invoice(invoice1).payload
        invoice2_payload = InvoiceNavisionAPIData.from_invoice(invoice2).payload

        self.assertEqual(invoice1_payload['aggregation_id'], str(aggregation_uuid))
        self.assertEqual(invoice2_payload['aggregation_id'], str(aggregation_uuid))

        self.assertEqual(invoice2_payload['aggregation_total'], 2)
        self.assertEqual(invoice2_payload['aggregation_total'], 2)

    @override_settings(
        API_COUNTRY=Country.PL,
        NAVISION_BANK_CODE_SUPPORTED=True,
        NAVISION_SALES_DATE_SUPPORTED=True,
    )
    def test_payload__pl(self):
        now = tznow()

        input_data = dict(
            merchant_id='BC-PL-100003',
            booksy_id='BC-PL-2020-01-**********',
            payment_due_date=date(2020, 1, 1),
            sales_date=date(2020, 1, 2),
            source='braintree',
            service='Boost',
            currency='PLN',
            document_type='VAT',
            invoice_date=date(2020, 1, 3),
            bank_code='ALIOR SP',
            invoice_note='Uwagi ...',
            emails=['<EMAIL>', '<EMAIL>'],
            aggregation_uuid=None,
            aggregation_total=None,
            invoice_items=[
                InvoiceItemAPIData(
                    booksy_item_id='PL-123',
                    product='Boost Revenue',
                    quantity=10,
                    service='Boost',
                    charge_completed=False,
                    base_gross_value=Decimal(10.34),
                    billing_cycle_end=now,
                    billing_cycle_start=now,
                    charging_dt=now,
                    transaction_id='*********',
                    transfer_dt=now,
                    country_code='pl',
                ),
            ],
        )
        data = InvoiceNavisionAPIData(**input_data)

        payload = data.payload
        invoice_item = payload['invoice_items'][0]
        del payload['invoice_items']

        self.assertDictEqual(
            payload,
            {
                'sales_tax_date': None,
                'merchant_id': 'BC-PL-100003',
                'booksy_id': 'BC-PL-2020-01-**********',
                'payment_due_date': date(2020, 1, 1).isoformat(),
                'sales_date': date(2020, 1, 2).isoformat(),
                'invoice_date': date(2020, 1, 3).isoformat(),
                'bank_code': 'ALIOR SP',
                'business_id': None,
                'business_name': None,
                'source': 'braintree',
                'currency': 'PLN',
                'service_type': 'Boost',
                'document_type': 'VAT',
                'email': '<EMAIL>; <EMAIL>',
            },
        )

        self.assertDictEqual(
            invoice_item,
            {
                'tax_area_code': None,
                'booksy_item_id': 'PL-123',
                'base_gross_value': 10.34,
                'quantity': 10,
                'billing_cycle_end': now.isoformat(),
                'billing_cycle_start': now.isoformat(),
                'charging_dt': None,
                'product': 'Boost Revenue',
                'service': 'Boost',
                'charge_completed': False,
                'transaction_id': '*********',
                'transfer_dt': now.isoformat(),
                'country_code': 'pl',
            },
        )

        input_data['emails'] = None

        data = InvoiceNavisionAPIData(**input_data)
        self.assertIsNone(data.payload['email'])

    @parameterized.expand(
        [
            param(
                country=Country.IE,
                tax_id=None,
                expected_source='HoFIE_SaaS',
                expected_item_service_saas='SaaS_EU',
                expected_item_service_discount='Discount_EU',
            ),
            param(
                country=Country.IE,
                tax_id='IE1234578',
                expected_source='HoFIE_SaaS',
                expected_item_service_saas='SaaS',
                expected_item_service_discount='Discount',
            ),
            param(
                country=Country.ES,
                tax_id=None,
                expected_source='HoF_SaaS',
                expected_item_service_saas='SaaS',
                expected_item_service_discount='Discount',
            ),
            param(
                country=Country.ES,
                tax_id='21243565',
                expected_source='HoF_SaaS',
                expected_item_service_saas='SaaS',
                expected_item_service_discount='Discount',
            ),
        ]
    )
    def test_invoice_item_service(
        self,
        country,
        tax_id,
        expected_source,
        expected_item_service_saas,
        expected_item_service_discount,
    ):
        invoice = baker.make(
            Invoice,
            merchant=baker.make(Merchant, tax_id=tax_id),
            source=InvoicePaymentSource.HOF,
            invoice_date=date(2025, 1, 1),
            service=Invoice.Service.SAAS,
            currency='EUR',
        )
        baker.make(
            InvoiceItem,
            invoice=invoice,
            service=Invoice.Service.SAAS,
            tax_additional_data={},
            discount_gross_value=Decimal('0'),
            base_gross_value=Decimal('100'),
        )
        baker.make(
            InvoiceItem,
            invoice=invoice,
            service=Invoice.Service.DISCOUNT,
            tax_additional_data={},
            discount_gross_value=Decimal('0'),
            base_gross_value=Decimal('10'),
        )
        with override_settings(API_COUNTRY=country):
            invoice_data = InvoiceNavisionAPIData.from_invoice(invoice)
            payload = invoice_data.payload

        self.assertEqual(expected_source, invoice_data.source)
        self.assertEqual(expected_item_service_discount, invoice_data.invoice_items[0].service)
        self.assertEqual(expected_item_service_saas, invoice_data.invoice_items[1].service)
        self.assertEqual(expected_source, payload['source'])
        self.assertEqual(expected_item_service_discount, payload['invoice_items'][0]['service'])
        self.assertEqual(expected_item_service_saas, payload['invoice_items'][1]['service'])
        self.assertEqual(-10.00, payload['invoice_items'][0]['base_gross_value'])
        self.assertEqual(100.00, payload['invoice_items'][1]['base_gross_value'])

    def test_business_id_field_in_payload(self):
        now = tznow()

        input_data = dict(
            merchant_id='BC-PL-100003',
            booksy_id='BC-PL-2020-01-**********',
            payment_due_date=date(2020, 1, 1),
            sales_date=date(2020, 1, 2),
            source='braintree',
            service='Boost',
            currency='PLN',
            document_type='VAT',
            invoice_date=date(2020, 1, 3),
            bank_code='ALIOR SP',
            invoice_note='Uwagi ...',
            emails=['<EMAIL>', '<EMAIL>'],
            aggregation_uuid=None,
            aggregation_total=None,
            invoice_items=[
                InvoiceItemAPIData(
                    booksy_item_id='PL-123',
                    product='Boost Revenue',
                    quantity=10,
                    service='Boost',
                    charge_completed=False,
                    base_gross_value=Decimal(10.34),
                    billing_cycle_end=now,
                    billing_cycle_start=now,
                    charging_dt=now,
                    discount_gross_value=Decimal(2),
                    transaction_id='*********',
                    transfer_dt=now,
                    country_code='pl',
                ),
            ],
        )
        data = InvoiceNavisionAPIData(**input_data)
        payload = data.payload
        self.assertIn('business_id', payload)

    def test_offline_discount_field(self):
        invoice = baker.make(
            Invoice,
            source=InvoicePaymentSource.OFFLINE,
            invoice_date=date(2022, 1, 1),
            payment_due_date=date(2022, 1, 1),
            service=Invoice.Service.SAAS,
            currency='PLN',
        )
        baker.make(
            InvoiceItem,
            invoice=invoice,
            service=Invoice.Service.SAAS,
            tax_additional_data={},
            discount_gross_value=Decimal('23'),
            base_gross_value=Decimal('100'),
        )

        payload = InvoiceNavisionAPIData.from_invoice(invoice).payload
        invoice_items = payload['invoice_items']
        self.assertEqual(1, len(invoice_items))
        self.assertEqual(invoice_items[0]['base_gross_value'], Decimal('77'))

    @parameterized.expand(
        [
            (InvoicePaymentSource.STRIPE, Invoice.Service.SAAS, Invoice.Service.SAAS),
            (InvoicePaymentSource.STRIPE, Invoice.Service.SAAS, Invoice.Service.STAFFERS),
            (InvoicePaymentSource.STRIPE, Invoice.Service.STAFFERS, Invoice.Service.STAFFERS),
            (InvoicePaymentSource.STRIPE, Invoice.Service.SMS, Invoice.Service.SAAS),
            (InvoicePaymentSource.STRIPE, Invoice.Service.SMS, Invoice.Service.STAFFERS),
        ]
    )
    def test_source_invoice__various_services_in_one_invoice(
        self, invoice_source, invoice_service_1, invoice_service_2
    ):
        invoice = baker.make(
            Invoice,
            source=invoice_source,
            invoice_date=date(2022, 1, 1),
            payment_due_date=date(2022, 1, 1),
            service=Invoice.Service.SAAS,
            currency='EUR',
        )
        baker.make(
            InvoiceItem,
            invoice=invoice,
            payment_source=invoice_source,
            service=invoice_service_1,
            tax_additional_data={},
            _quantity=2,
        )
        baker.make(
            InvoiceItem,
            invoice=invoice,
            payment_source=invoice_source,
            service=invoice_service_2,
            tax_additional_data={},
            _quantity=2,
        )

        payload = InvoiceNavisionAPIData.from_invoice(invoice).payload

        self.assertDictEqual(
            payload,
            {
                'sales_tax_date': None,
                'booksy_id': invoice.booksy_id,
                'business_id': None,
                'business_name': None,
                'currency': 'EUR',
                'document_type': 'VAT',
                'email': None,
                'invoice_date': '2022-01-01',
                'invoice_items': mock.ANY,
                'merchant_id': invoice.merchant.merchant_id,
                'payment_due_date': '2022-01-01',
                'service_type': InvoiceService.SAAS,
                'source': 'Str_SaaS',
            },
        )

    def test_boost_service(self):
        invoice = baker.make(
            Invoice,
            source=InvoicePaymentSource.STRIPE,
            invoice_date=date(2022, 1, 1),
            payment_due_date=date(2022, 1, 1),
            service=Invoice.Service.BOOST,
            currency='EUR',
        )
        baker.make(
            InvoiceItem,
            invoice=invoice,
            payment_source=InvoicePaymentSource.STRIPE,
            service=Invoice.Service.BOOST,
            tax_additional_data={},
            _quantity=2,
        )
        payload = InvoiceNavisionAPIData.from_invoice(invoice).payload
        self.assertEqual(1, Invoice.objects.count())
        self.assertEqual(2, len(payload['invoice_items']))
        self.assertEqual('Str_Boost', payload['source'])
        self.assertEqual(Invoice.Service.BOOST, payload['service_type'])
        self.assertEqual(
            {Invoice.Service.BOOST}, set(item['service'] for item in payload['invoice_items'])
        )

    def test_sms_service(self):
        invoice = baker.make(
            Invoice,
            source=InvoicePaymentSource.STRIPE,
            invoice_date=date(2022, 1, 1),
            payment_due_date=date(2022, 1, 1),
            service=Invoice.Service.SAAS,
            currency='EUR',
        )
        baker.make(
            InvoiceItem,
            invoice=invoice,
            payment_source=InvoicePaymentSource.STRIPE,
            service=Invoice.Service.SMS,
            tax_additional_data={},
            _quantity=2,
        )
        payload = InvoiceNavisionAPIData.from_invoice(invoice).payload
        self.assertEqual(1, Invoice.objects.count())
        self.assertEqual(2, len(payload['invoice_items']))
        self.assertEqual('Str_SMS', payload['source'])
        self.assertEqual(Invoice.Service.SMS, payload['service_type'])
        self.assertEqual(Invoice.Service.SAAS, Invoice.objects.first().service)

    def test_staffer_service(self):
        invoice = baker.make(
            Invoice,
            source=InvoicePaymentSource.STRIPE,
            invoice_date=date(2022, 1, 1),
            payment_due_date=date(2022, 1, 1),
            service=Invoice.Service.SAAS,
            currency='EUR',
        )
        baker.make(
            InvoiceItem,
            invoice=invoice,
            service=Invoice.Service.STAFFERS,
            payment_source=InvoicePaymentSource.STRIPE,
            tax_additional_data={},
            _quantity=2,
        )

        payload = InvoiceNavisionAPIData.from_invoice(invoice).payload
        self.assertEqual(1, Invoice.objects.count())
        self.assertEqual(2, len(payload['invoice_items']))
        self.assertEqual('Str_SaaS', payload['source'])
        self.assertEqual(Invoice.Service.SAAS, payload['service_type'])
        self.assertEqual(Invoice.Service.SAAS, Invoice.objects.first().service)

    @parameterized.expand(
        [
            (InvoicePaymentSource.STRIPE, Invoice.Service.SAAS, Invoice.Service.SMS, 'Str_SaaS'),
            (InvoicePaymentSource.STRIPE, Invoice.Service.SMS, Invoice.Service.SMS, 'Str_SMS'),
            (InvoicePaymentSource.HOF, Invoice.Service.SAAS, Invoice.Service.SMS, 'HoF_SaaS'),
            (InvoicePaymentSource.HOF, Invoice.Service.SMS, Invoice.Service.SMS, 'HoF_SMS'),
        ]
    )
    def test_saas_and_sms_services(self, source, service_1, service_2, expected_source):
        invoice = baker.make(
            Invoice,
            source=source,
            invoice_date=date(2022, 1, 1),
            payment_due_date=date(2022, 1, 1),
            service=Invoice.Service.SAAS,
            currency='EUR',
        )
        baker.make(
            InvoiceItem,
            invoice=invoice,
            service=service_1,
            payment_source=source,
            tax_additional_data={},
        )
        baker.make(
            InvoiceItem,
            invoice=invoice,
            service=service_2,
            payment_source=source,
            tax_additional_data={},
        )
        payload = InvoiceNavisionAPIData.from_invoice(invoice).payload
        self.assertEqual(1, Invoice.objects.count())
        self.assertEqual(2, len(payload['invoice_items']))
        self.assertEqual(expected_source, payload['source'])
        self.assertEqual(service_1, payload['service_type'])

    def test_discount_as_separate_invoice_item(self):
        invoice = baker.make(
            Invoice,
            source=InvoicePaymentSource.STRIPE,
            service=InvoiceService.SAAS,
            items=[
                baker.make(
                    InvoiceItem,
                    service=InvoiceService.SAAS,
                    payment_source=InvoicePaymentSource.STRIPE,
                    tax_additional_data=None,
                    base_gross_value=10,
                    discount_gross_value=Decimal(0.00),
                ),
                baker.make(
                    InvoiceItem,
                    service=InvoiceService.DISCOUNT,
                    payment_source=InvoicePaymentSource.STRIPE,
                    tax_additional_data=None,
                    base_gross_value=5,
                    discount_gross_value=None,
                ),
            ],
        )

        payload = InvoiceNavisionAPIData.from_invoice(invoice).payload
        self.assertIn(
            {
                'tax_area_code': None,
                'booksy_item_id': mock.ANY,
                'country_code': mock.ANY,
                'base_gross_value': -5.0,
                'billing_cycle_end': mock.ANY,
                'billing_cycle_start': mock.ANY,
                'charging_dt': mock.ANY,
                'product': mock.ANY,
                'quantity': mock.ANY,
                'service': InvoiceService.DISCOUNT,
                'charge_completed': mock.ANY,
                'transaction_id': mock.ANY,
                'transfer_dt': mock.ANY,
            },
            payload['invoice_items'],
        )
        self.assertIn(
            {
                'tax_area_code': None,
                'booksy_item_id': mock.ANY,
                'country_code': mock.ANY,
                'base_gross_value': 10.0,
                'billing_cycle_end': mock.ANY,
                'billing_cycle_start': mock.ANY,
                'charging_dt': mock.ANY,
                'product': mock.ANY,
                'quantity': mock.ANY,
                'service': InvoiceService.SAAS,
                'charge_completed': mock.ANY,
                'transaction_id': mock.ANY,
                'transfer_dt': mock.ANY,
            },
            payload['invoice_items'],
        )

    @parameterized.expand(
        [
            (True, '2019-04-15', '44666', '90210', '06100'),
            (False, None, None, None, None),
        ]
    )
    @freeze_time(datetime(2020, 3, 16, 8, 42, tzinfo=UTC))
    def test_tax_rate_fields(
        self,
        tax_rate_integration_enabled,
        expected_sales_tax_date,
        expected_zipcode_1,
        expected_zipcode_2,
        expected_zipcode_3,
    ):
        now = tznow()
        active_from_1 = datetime(2018, 6, 23, 7, 33, tzinfo=UTC)
        active_from_2 = datetime(2019, 4, 15, 8, 21, tzinfo=UTC)

        baker.make(
            Region,
            name='06200',
            type=Region.Type.ZIP,
        )
        merchant = baker.make(
            Merchant,
            entity_name='Test',
            address_details1='addr1',
            city='Test',
            zip_code=expected_zipcode_3,
            country_code=Country.US,
        )
        business = baker.make(Business)
        baker.make(
            SubscriptionBuyer,
            businesses=[business],
            merchant=merchant,
            entity_name='John Doe',
            invoice_email='<EMAIL>',
        )
        charging_date_1 = active_from_1 - relativedelta(days=5)
        charging_date_2 = active_from_1 + relativedelta(days=5)
        charging_date_3 = active_from_2
        invoice = baker.make(
            Invoice,
            merchant=merchant,
            source=InvoicePaymentSource.STRIPE,
            invoice_date=date(2022, 1, 1),
            payment_due_date=date(2022, 1, 1),
            service=Invoice.Service.SAAS,
            currency='EUR',
        )
        baker.make(
            InvoiceItem,
            invoice=invoice,
            id=996,
            charging_dt=charging_date_1,
            service='Boost',
            tax_additional_data=dict(
                tax_rate_id=1,
                area='zip',
                tax_area_name=expected_zipcode_1,
            ),
        )
        baker.make(
            InvoiceItem,
            invoice=invoice,
            id=997,
            charging_dt=charging_date_2,
            service='Boost',
            tax_additional_data=dict(
                tax_rate_id=2,
                area='zip',
                tax_area_name=expected_zipcode_2,
            ),
        )
        baker.make(
            InvoiceItem,
            invoice=invoice,
            id=998,
            charging_dt=charging_date_3,
            service='Boost',
            tax_additional_data=dict(
                tax_rate_id=3,
                area='zip',
                tax_area_name=None,
            ),
        )
        with override_settings(NAVISION_ENABLE_TAX_RATE_INTEGRATION=tax_rate_integration_enabled):
            payload = InvoiceNavisionAPIData.from_invoice(invoice).payload
        self.assertEqual(expected_sales_tax_date, payload['sales_tax_date'])
        self.assertEqual(3, len(payload['invoice_items']))
        for zipcode in [expected_zipcode_1, expected_zipcode_2, expected_zipcode_3]:
            self.assertIn(
                {
                    'tax_area_code': zipcode,
                    'booksy_item_id': mock.ANY,
                    'base_gross_value': mock.ANY,
                    'quantity': 1,
                    'billing_cycle_end': now.isoformat(),
                    'billing_cycle_start': now.isoformat(),
                    'charging_dt': None,
                    'product': mock.ANY,
                    'service': 'Boost',
                    'transaction_id': None,
                    'transfer_dt': None,
                    'charge_completed': None,
                    'country_code': 'US',
                },
                payload['invoice_items'],
            )

    @override_settings(
        API_COUNTRY=Country.PL,
        NAVISION_BANK_CODE_SUPPORTED=True,
        NAVISION_SALES_DATE_SUPPORTED=True,
    )
    @override_eppo_feature_flag({UseNavisionMerchantAPIClientV2Flag.flag_name: True})
    def test_sales_date_and_bank_code_deleted(self):
        invoice = baker.make(
            Invoice,
            source=InvoicePaymentSource.OFFLINE,
            invoice_date=date(2022, 1, 1),
            sales_date=date(2022, 1, 1),
            payment_due_date=date(2022, 1, 1),
            service=Invoice.Service.SAAS,
            bank_code='bank 123',
            currency='PLN',
        )
        payload = InvoiceNavisionAPIData.from_invoice(invoice).payload

        self.assertNotIn('sales_date', payload)
        self.assertNotIn('bank_code', payload)

    @override_settings(
        API_COUNTRY=Country.PL,
        NAVISION_BANK_CODE_SUPPORTED=True,
        NAVISION_SALES_DATE_SUPPORTED=True,
    )
    def test_sales_date_and_bank_code_not_deleted(self):
        invoice = baker.make(
            Invoice,
            source=InvoicePaymentSource.OFFLINE,
            invoice_date=date(2022, 1, 1),
            sales_date=date(2022, 1, 1),
            payment_due_date=date(2022, 1, 1),
            service=Invoice.Service.SAAS,
            bank_code='bank 123',
            currency='PLN',
        )
        payload = InvoiceNavisionAPIData.from_invoice(invoice).payload

        self.assertIn('sales_date', payload)
        self.assertIn('bank_code', payload)


@freeze_time(datetime(2020, 3, 16, 8, 42, tzinfo=UTC))
class TestInvoiceUpdatedNavisionAPIData(TestCase):
    def test_payload(self):
        charging_date = tznow()
        invoice = baker.make(Invoice, id=44)
        for idx, status in enumerate([InvoiceItem.Status.INIT, InvoiceItem.Status.ERROR]):
            baker.make(
                InvoiceItem,
                id=idx + 1,
                invoice=invoice,
                product=f'boost online_{idx}',
                service='Boost',
                charging_dt=charging_date,
                transaction_id=f'*********33_{idx}',
                update_status=status,
                charge_completed=True,
            )

        invoice_data = InvoiceUpdatedNavisionAPIData.from_invoice(invoice)
        result = invoice_data.payload
        self.assertEqual({'booksy_id', 'charge_line'}, result.keys())
        self.assertEqual('DEV-US-2020-03-**********', result['booksy_id'])
        self.assertEqual(2, len(result['charge_line']))
        self.assertIn(
            {
                'booksy_item_id': 'US-1',
                'charge_completed': True,
                'transaction_id': '*********33_0',
                'charging_dt': '2020-03-16T08:42:00+00:00',
            },
            result['charge_line'],
        )
        self.assertIn(
            {
                'booksy_item_id': 'US-2',
                'charge_completed': True,
                'transaction_id': '*********33_1',
                'charging_dt': '2020-03-16T08:42:00+00:00',
            },
            result['charge_line'],
        )

    def test_payload_charging_dt_is_none(self):
        invoice = baker.make(Invoice, id=44)
        baker.make(
            InvoiceItem,
            id=1,
            invoice=invoice,
            product='boost online',
            service='Boost',
            charging_dt=tznow(),
            transaction_id='*********33',
            update_status=InvoiceItem.Status.INIT,
            charge_completed=False,
        )

        invoice_data = InvoiceUpdatedNavisionAPIData.from_invoice(invoice)
        result = invoice_data.payload
        self.assertEqual(
            {
                'booksy_id': 'DEV-US-2020-03-**********',
                'charge_line': [
                    {
                        'booksy_item_id': 'US-1',
                        'charge_completed': False,
                        'transaction_id': '*********33',
                        'charging_dt': None,
                    },
                ],
            },
            result,
        )
