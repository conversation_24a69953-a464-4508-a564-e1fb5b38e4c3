# Generated by Django 4.2.18 on 2025-05-29 21:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('notification', '0084_alter_notificationschedule_id'),
    ]

    operations = [
        migrations.AlterField(
            model_name='notificationhistory',
            name='task_type',
            field=models.CharField(
                choices=[
                    ('AA', 'account_added'),
                    ('BL', 'blast'),
                    ('BD', 'b_listing_claimed'),
                    ('BC', 'booking_changed'),
                    ('BF', 'booking_finished'),
                    ('BO', 'booking_other'),
                    ('BX', 'boost_change_status'),
                    ('BW', 'boost_change_status_with_date'),
                    ('BE', 'boost_status_set_date'),
                    ('BS', 'boost_switch'),
                    ('BI', 'business_account_invite'),
                    ('BT', 'business_activity'),
                    ('BA', 'business_added'),
                    ('PS', 'business_change_payment_source'),
                    ('EB', 'external_business_recommendation'),
                    ('GE', 'gdpr_export'),
                    ('IC', 'import_customers_report'),
                    ('IT', 'invitation'),
                    ('IR', 'invitation_reminder'),
                    ('CC', 'mass_commission_change_task'),
                    ('CL', 'mass_commission_close_task'),
                    ('PR', 'password_reset'),
                    ('PP', 'pattern_reminder'),
                    ('PO', 'pos'),
                    ('RA', 'review_added'),
                    ('RR', 'review_reply_added'),
                    ('SB', 'sms_blast'),
                    ('SE', 'sms_external_business'),
                    ('SG', 'sms_gate'),
                    ('SR', 'sms_registration_code'),
                    ('ST', 'sms_tool'),
                    ('SC', 'statistics'),
                    ('TT', 'template_test'),
                    ('TR', 'transaction'),
                    ('UR', 'update_repeating_report'),
                    ('CF', 'no_show_proposition'),
                    ('NC', 'no_show_confirmation'),
                    ('NS', 'no_show_information'),
                    ('LM', 'last_minute_incentive'),
                    ('HH', 'happy_hours_incentive'),
                    ('WL', 'waitlist'),
                    ('DF', 'df'),
                    ('IW', 'import_warehouse_commodities_report'),
                    ('WC', 'import_wholesaler_commodities_report'),
                    ('BR', 'sms_booking_reactivation'),
                    ('VC', 'voucher'),
                    ('MC', 'sms_marketing_consent'),
                    ('CX', 'sms_customer_invitation'),
                    ('BP', 'booksy_pay'),
                    ('PH', 'premium_hours'),
                ],
                db_index=True,
                max_length=2,
                null=True,
            ),
        ),
    ]
