import datetime
import decimal
import json
from datetime import timedelta
from unittest.mock import Mock

from dateutil.relativedelta import relativedelta
import pytest
from django.conf import settings
from django.test import override_settings
from freezegun import freeze_time
from mock import patch
from model_bakery import baker
from segment.analytics import Client

from country_config import Country
from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.experiment.booking import SuggestiveReviewsExperiment
from lib.feature_flag.feature.notification import (
    IncludeBookingIdInAddReviewPushTargetFlag,
    SendTippingAppetiteExperimentPushFlag,
)
from lib.test_utils import (
    create_subbooking,
    increase_appointment_next_id,
)
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import (
    id_to_external_api,
    tznow,
)

from service.tests import (
    BaseAsyncHTTPTest,
    dict_assert,
)
from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
    SubbookingServiceVariantMode as SVMode,
)
from webapps.booking.models import (
    Appointment,
    BookingChange,
    BookingSources,
    SubBooking,
    BookingResource,
)
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import bci_recipe
from webapps.business.models import (
    Business,
    Resource,
    Service,
    ServiceCategory,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.models.category import BusinessCategory
from webapps.consts import IPHONE_SOLO
from webapps.kill_switch.models import KillSwitch
from webapps.notification.consts import NO_INFORMATION
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.notification.models import (
    NotificationHistory,
    NotificationSchedule,
    Reciever,
    UserNotification,
)
from webapps.notification.push import notification_receivers_list
from webapps.notification.scenarios.scenarios_booking import (
    BookingFinishedScenario,
)
from webapps.reviews.models import Review
from webapps.segment.consts import UserRoleEnum
from webapps.segment.models import ZipCodesToUrbanAreasMapper
from webapps.user.models import UserProfile


def _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer):
    tz = booking.appointment.business.get_timezone()
    body = {
        'customer': {
            'mode': ACMode.CUSTOMER_CARD,
            'id': bci.id,
        },
        'subbookings': [
            {
                'booked_from': booked_from_moved.astimezone(tz).strftime(settings.DATETIME_FORMAT),
                'booked_till': booked_till_moved.astimezone(tz).strftime(settings.DATETIME_FORMAT),
                'appliance_id': None,
                'staffer_id': staffer.id,
                'service_variant': (
                    {
                        'mode': SVMode.NO_VARIANT,
                        'service_name': 'a',
                    }
                    if not booking.service_variant
                    else {
                        'mode': SVMode.VARIANT,
                        'id': booking.service_variant_id,
                    }
                ),
            }
        ],
        '_version': booking.appointment._version,  # pylint: disable=protected-access
        'dry_run': False,
        'overbooking': True,  # ignore working hours
        '_notify_about_reschedule': False,
    }
    return body


def format_minute_precision(datetime_obj, timezone):
    return datetime.datetime(
        year=datetime_obj.year,
        month=datetime_obj.month,
        day=datetime_obj.day,
        hour=datetime_obj.hour,
        minute=datetime_obj.minute,
        tzinfo=timezone,
    )


TEST_DATE_TIME = '2015-05-05 12:00'


@pytest.mark.django_db
class BookingFinishedScenarioTestCase(BaseAsyncHTTPTest):

    def setUp(self):
        increase_appointment_next_id()
        NotificationHistoryDocument.tasks_clear()
        return super().setUp()

    def tearDown(self):
        super().tearDown()
        NotificationHistoryDocument.tasks_clear()

    def _create_stuff(self, booked_from, booked_till, now=None):
        now = now or tznow()
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )

        bci = BusinessCustomerInfo.objects.get_or_create(
            business=self.business,
            user=self.user,
        )[0]

        appointment = create_appointment(
            [
                dict(
                    booked_from=booked_from,
                    booked_till=booked_till,
                    service_name='a',
                    service_variant=baker.make(
                        ServiceVariant,
                        duration='0100',
                        service=baker.make(
                            Service,
                            business=self.business,
                            name='service_name_aaa',
                            resources=[staffer],
                            service_category=baker.make(
                                ServiceCategory,
                                business=self.business,
                                name='service_category_name_bbb',
                            ),
                        ),
                    ),
                ),
            ],
            business=self.business,
            type=Appointment.TYPE.CUSTOMER,
            booked_for=bci,
            source=self.customer_booking_src,
            updated_by=self.user,
            status=Appointment.STATUS.ACCEPTED,
        )
        subbooking = appointment.subbookings[0]
        BookingChange.add(
            subbooking,
            changed_by=BookingChange.BY_CUSTOMER,
            changed_user=self.user,
        )
        booking_change = BookingChange.objects.get()
        booking_change.created = now
        booking_change.save()

        return bci, subbooking, staffer

    def _create_customer_reciever(self):
        booking_source, _ = BookingSources.objects.get_or_create(
            name='Android',
            app_type=BookingSources.CUSTOMER_APP,
            api_key='xx312',
        )
        user_profile, _ = UserProfile.objects.update_or_create(
            user=self.user,
            profile_type=UserProfile.Type.CUSTOMER,
            defaults={
                'source': booking_source,
            },
        )
        user_notification = baker.make(
            UserNotification,
            profile=user_profile,
            type=UserNotification.PUSH_NOTIFICATION,
        )

        return baker.make(
            Reciever,
            identifier='xxx',
            business=self.business,
            device='android',
            customer_notifications=user_notification,
        )

    def _get_url(self, booking):
        return f'/business_api/me/businesses/{self.business.id}/appointments/single/{booking.id}/'

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_futur2past2past(self, schedule_mock):
        booked_from = tznow() + timedelta(hours=2)
        booked_till = tznow() + timedelta(hours=3)

        booked_from_moved = tznow() - timedelta(hours=3)
        booked_till_moved = tznow() - timedelta(hours=2)

        bci, booking, staffer = self._create_stuff(booked_from, booked_till)

        # move booking
        url = self._get_url(booking)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        assert booking.appointment.status == Appointment.STATUS.ACCEPTED
        assert NotificationHistoryDocument.task_count() == 0

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 1

        booking.appointment.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.FINISHED

        assert NotificationHistoryDocument.task_count() == 0
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_changed:customer_booking_reschedule_request:'
            f'appointment_id={booking.appointment_id}',
        ).first()

        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.FINISHED

        # and move again
        booked_from_moved = tznow() - timedelta(hours=4)
        booked_till_moved = tznow() - timedelta(hours=3)
        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        booking.refresh_from_db()
        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 1
        assert booking.appointment.status == Appointment.STATUS.FINISHED

        assert NotificationHistoryDocument.task_count() == 0

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_futur2old_past(self, schedule_mock):
        booked_from = tznow() + timedelta(hours=2)
        booked_till = tznow() + timedelta(hours=3)

        booked_from_moved = tznow() - timedelta(hours=3)
        booked_till_moved = tznow() - timedelta(hours=2)

        bci, booking, staffer = self._create_stuff(booked_from, booked_till)

        # move booking
        url = self._get_url(booking)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 1

        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.FINISHED

        # and move again
        booked_from_moved = tznow() - timedelta(days=3, hours=2)
        booked_till_moved = tznow() - timedelta(days=3, hours=1)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 1
        assert booking.appointment.status == Appointment.STATUS.FINISHED

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_futur2over_month_ago(self, schedule_mock):
        booked_from = tznow() + timedelta(hours=2)
        booked_till = tznow() + timedelta(hours=3)

        booked_from_moved = tznow() - timedelta(days=40, hours=3)
        booked_till_moved = tznow() - timedelta(days=40, hours=2)

        bci, booking, staffer = self._create_stuff(booked_from, booked_till)

        # move booking
        url = self._get_url(booking)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 0

        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.FINISHED

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_past2past(self, schedule_mock):
        booked_from = tznow() - timedelta(hours=4)
        booked_till = tznow() - timedelta(hours=3)

        booked_from_moved = tznow() - timedelta(days=3, hours=2)
        booked_till_moved = tznow() - timedelta(days=3, hours=1)

        bci, booking, staffer = self._create_stuff(booked_from, booked_till)

        assert NotificationHistoryDocument.task_count() == 0

        # move booking
        url = self._get_url(booking)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 0

        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.FINISHED
        assert NotificationHistoryDocument.task_count() == 0

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_just_now2past(self, schedule_mock):
        booked_from = tznow() - timedelta(hours=1, minutes=5)
        booked_till = tznow() - timedelta(minutes=5)

        booked_from_moved = tznow() - timedelta(hours=4)
        booked_till_moved = tznow() - timedelta(hours=3)

        bci, booking, staffer = self._create_stuff(booked_from, booked_till)
        assert NotificationHistoryDocument.task_count() == 0

        # move booking
        url = self._get_url(booking)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 0

        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.FINISHED
        assert NotificationHistoryDocument.task_count() == 0

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_past2futur2past(self, schedule_mock):
        tz = self.business.get_timezone()
        booked_from = tznow() - timedelta(hours=3)
        booked_till = tznow() - timedelta(hours=2)

        bci, booking, staffer = self._create_stuff(booked_from, booked_till)
        assert NotificationHistoryDocument.task_count() == 0

        # move booking
        url = self._get_url(booking)

        booked_from_moved = tznow() + timedelta(days=3, hours=1)
        booked_till_moved = tznow() + timedelta(days=3, hours=2)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 0
        assert NotificationHistoryDocument.task_count() == 1

        booking.refresh_from_db()

        assert format_minute_precision(booking.booked_from, tz) == format_minute_precision(
            booked_from_moved, tz
        )
        assert format_minute_precision(booking.booked_till, tz).astimezone(
            tz
        ) == format_minute_precision(booked_till_moved, tz)

        assert booking.appointment.status == Appointment.STATUS.PROPOSED
        booking_change_entries = BookingChange.objects.filter(
            subbooking=booking,
            status=Appointment.STATUS.PROPOSED,
        )
        assert len(booking_change_entries) == 1
        assert booking_change_entries[0].created == booking.updated

        # and move again
        booked_from_moved = tznow() - timedelta(hours=4)
        booked_till_moved = tznow() - timedelta(hours=3)
        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert NotificationHistoryDocument.task_count() == 1
        booking.refresh_from_db()

        assert resp.code == 200, resp.body
        assert format_minute_precision(booking.booked_from, tz) == format_minute_precision(
            booked_from_moved, tz
        )
        assert format_minute_precision(booking.booked_till, tz).astimezone(
            tz
        ) == format_minute_precision(booked_till_moved, tz)
        assert booking.appointment.status == Appointment.STATUS.FINISHED
        assert schedule_mock.call_count == 1
        assert schedule_mock.call_args[0][0][0][0] == (
            f'booking_finished:next_day:appointment_id={booking.appointment_id}'
        )

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_past2futur2past_killswitch_auto_notify_about_reschedule(
        self, schedule_mock
    ):
        baker.make(
            KillSwitch,
            name=KillSwitch.System.AUTO_NOTIFY_ABOUT_RESCHEDULE,
            is_killed=True,
        )

        tz = self.business.get_timezone()
        booked_from = tznow() - timedelta(hours=3)
        booked_till = tznow() - timedelta(hours=2)

        bci, booking, staffer = self._create_stuff(booked_from, booked_till)
        assert NotificationHistoryDocument.task_count() == 0

        # move booking
        url = self._get_url(booking)

        booked_from_moved = tznow() + timedelta(days=3, hours=1)
        booked_till_moved = tznow() + timedelta(days=3, hours=2)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 0
        assert NotificationHistoryDocument.task_count() == 1

        booking.refresh_from_db()

        assert format_minute_precision(booking.booked_from, tz) == format_minute_precision(
            booked_from_moved, tz
        )
        assert format_minute_precision(booking.booked_till, tz).astimezone(
            tz
        ) == format_minute_precision(booked_till_moved, tz)

        # because _notify_about_reschedule is False then no notification and no user decision
        assert booking.appointment.status == Appointment.STATUS.ACCEPTED

    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_past2futur2past_schedule_time(self, schedule_mock):
        with freeze_time('2015-05-05 12:00'):
            booked_from = tznow() - timedelta(hours=3)
            booked_till = booked_from + timedelta(hours=1)

            bci, booking, staffer = self._create_stuff(booked_from, booked_till)
            assert NotificationHistoryDocument.task_count() == 0

            url = self._get_url(booking)

            booked_from_moved = booked_from + timedelta(days=3)
            booked_till_moved = booked_till + timedelta(days=3)

            body = _get_body(
                bci,
                booked_from_moved,
                booked_till_moved,
                booking,
                staffer,
            )

            resp = self.fetch(url, method='PUT', body=body)
            now = tznow()

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 0
        assert NotificationHistoryDocument.task_count() == 1
        notification_schedule = NotificationSchedule.objects.filter(
            task_id=f'booking_changed:customer_booking_reschedule_request:'
            f'appointment_id={booking.appointment_id}',
        ).first()
        schedule_0 = notification_schedule.scheduled
        assert schedule_0 >= now

        booking.refresh_from_db()

        assert booking.appointment.status == Appointment.STATUS.PROPOSED
        booking_change_entries = BookingChange.objects.filter(
            subbooking=booking,
            status=Appointment.STATUS.PROPOSED,
        )
        assert len(booking_change_entries) == 1

        with freeze_time('2015-05-05 12:01'):
            booked_from_moved = booked_from
            booked_till_moved = booked_till
            body = _get_body(
                bci,
                booked_from_moved,
                booked_till_moved,
                booking,
                staffer,
            )

            resp = self.fetch(url, method='PUT', body=body)

        assert NotificationHistoryDocument.task_count() == 1
        booking.refresh_from_db()

        assert resp.code == 200, resp.body
        assert booking.appointment.status == Appointment.STATUS.FINISHED
        assert schedule_mock.call_count == 1
        assert schedule_mock.call_args[0][0][0][0] == (
            f'booking_finished:next_day:appointment_id={booking.appointment_id}'
        )
        notification_schedule.refresh_from_db()
        schedule_1 = notification_schedule.scheduled
        assert schedule_1 > schedule_0
        assert (
            NotificationSchedule.objects.filter(
                task_id=f'booking_changed:customer_booking_reschedule_request:'
                f'appointment_id={booking.appointment_id}',
            ).count()
            == 1
        )

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_do_not_move_notify_true(self, schedule_mock):
        booked_from = tznow() + timedelta(hours=3)
        booked_till = tznow() + timedelta(hours=4)

        bci, booking, staffer = self._create_stuff(booked_from, booked_till)
        assert NotificationHistoryDocument.task_count() == 0

        # move booking
        url = self._get_url(booking)

        body = _get_body(
            bci,
            booked_from,
            booked_till,
            booking,
            staffer,
        )

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 0
        assert NotificationHistoryDocument.task_count() == 1

        booking.refresh_from_db()

        assert booking.appointment.status == Appointment.STATUS.ACCEPTED
        first_booking_change_entries = (
            BookingChange.objects.filter(
                subbooking=booking,
                status=Appointment.STATUS.ACCEPTED,
            )
            .exclude(
                metadata='{}',
            )
            .first()
        )
        assert (
            BookingChange.objects.filter(
                subbooking=booking,
                status=Appointment.STATUS.ACCEPTED,
            ).count()
            == 2
        )
        assert first_booking_change_entries.created == booking.updated
        metadata_0 = json.loads(first_booking_change_entries.metadata)
        assert not metadata_0['notify_about_reschedule']

        second_booking_change_entries = BookingChange.objects.filter(
            subbooking=booking,
            status=Appointment.STATUS.ACCEPTED,
            metadata='{}',
        ).first()
        metadata_1 = json.loads(second_booking_change_entries.metadata)
        assert 'notify_about_reschedule' not in metadata_1

        body['_notify_about_reschedule'] = True
        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 0
        assert NotificationHistoryDocument.task_count() == 1
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_changed:customer_booking_reschedule_request:'
            f'appointment_id={booking.appointment_id}',
        ).first()

        booking.refresh_from_db()

        assert booking.appointment.status == Appointment.STATUS.PROPOSED
        booking_change_entry = (
            BookingChange.objects.filter(
                subbooking=booking,
                status=Appointment.STATUS.PROPOSED,
            )
            .exclude(
                metadata='{}',
            )
            .first()
        )
        assert booking_change_entry.created == booking.updated
        metadata = json.loads(booking_change_entry.metadata)
        assert metadata['notify_about_reschedule']

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_get_earliest_booked_from_none(
        self,
        schedule_mock,
    ):
        booked_from = tznow() + timedelta(hours=3)
        booked_till = tznow() + timedelta(hours=4)

        bci, booking, staffer = self._create_stuff(booked_from, booked_till)
        assert NotificationHistoryDocument.task_count() == 0

        # move booking
        url = self._get_url(booking)

        body = _get_body(
            bci,
            booked_from,
            booked_till,
            booking,
            staffer,
        )
        body['subbookings'][0]['booked_from'] = None
        body['subbookings'][0]['booked_till'] = None

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 400
        assert schedule_mock.call_count == 0

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_futur2past2futur2past(self, schedule_mock):
        booked_from = tznow() + timedelta(hours=2)
        booked_till = tznow() + timedelta(hours=3)

        bci, booking, staffer = self._create_stuff(booked_from, booked_till)

        url = self._get_url(booking)

        # move booking 2 past
        booked_from_moved = tznow() - timedelta(hours=3)
        booked_till_moved = tznow() - timedelta(hours=2)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        print('schedule_mock:')
        for scenario_args in schedule_mock.call_args_list:
            print(scenario_args)
        assert schedule_mock.call_count == 1

        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.FINISHED

        # move booking 2 futur
        booked_from_moved = tznow() + timedelta(hours=2)
        booked_till_moved = tznow() + timedelta(hours=3)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        print('schedule_mock:')
        for scenario_args in schedule_mock.call_args_list:
            print(scenario_args)
        assert schedule_mock.call_count == 1

        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.ACCEPTED

        # and move 2 past again
        booked_from_moved = tznow() - timedelta(hours=4)
        booked_till_moved = tznow() - timedelta(hours=3)
        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        booking.refresh_from_db()
        assert resp.code == 200, resp.body
        assert booking.appointment.status == Appointment.STATUS.FINISHED
        print('schedule_mock:')
        for scenario_args in schedule_mock.call_args_list:
            print(scenario_args)
        assert schedule_mock.call_count == 2
        assert schedule_mock.call_args[0][0][0][0] == (
            f'booking_finished:next_day:appointment_id={booking.appointment_id}'
        )

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_finished2past(self, schedule_mock):
        booked_from = tznow() - timedelta(hours=1)
        booked_till = tznow()

        bci, booking, staffer = self._create_stuff(booked_from, booked_till)
        assert schedule_mock.call_count == 0

        url = self._get_url(booking)

        # move finished booking
        booked_from_moved = tznow() - timedelta(hours=2)
        booked_till_moved = tznow() - timedelta(hours=1)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 0

        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.FINISHED
        assert NotificationHistoryDocument.task_count() == 0

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_accepted2past_prompted_reviews_case0(self, schedule_mock):
        self._prompted_reviews_base_setup(4)

        self._prompted_reviews_common_test_part(schedule_mock)

        notification_schedule = NotificationSchedule.objects.filter(
            task_id__contains=(
                f'PromptedReviewsFiveCustomerAppointmentsFinishedNotification,{self.business.id}'
            ),
        ).first()
        assert notification_schedule
        NotificationHistoryDocument.tasks_refresh()
        document = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                business_id=self.business.id,
            )
            .query(
                'term',
                status=NO_INFORMATION,
            )
            .query(
                'term',
                task_type='NotificationDocument',
            )
            .execute()[0]
        )
        assert document.metadata.other_entity['original_document_id']
        assert document.metadata.other_entity['type'] == (
            'PromptedReviewsFiveCustomerAppointmentsFinishedNotification'
        )
        assert document.type == UserNotification.POPUP_NOTIFICATION

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_accepted2past_prompted_reviews_case1(self, schedule_mock):
        self._prompted_reviews_base_setup(3)

        self._prompted_reviews_common_test_part(schedule_mock)
        assert NotificationHistoryDocument.task_count() == 0

        assert not NotificationSchedule.objects.filter(
            task_id__contains=f'FiveCustomerAppointmentsFinished,{self.business.id}',
        ).exists()

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_accepted2past_prompted_reviews_case2(self, schedule_mock):
        self._prompted_reviews_base_setup(5)

        self._prompted_reviews_common_test_part(schedule_mock)
        assert NotificationHistoryDocument.task_count() == 0

        assert not NotificationSchedule.objects.filter(
            task_id__contains=f'FiveCustomerAppointmentsFinished,{self.business.id}',
        ).exists()

    def _prompted_reviews_common_test_part(self, schedule_mock):
        booked_from = tznow() + timedelta(hours=1)
        booked_till = tznow() + timedelta(hours=2)

        bci, booking, staffer = self._create_stuff(booked_from, booked_till)
        assert schedule_mock.call_count == 0

        url = self._get_url(booking)

        booked_from_moved = tznow() - timedelta(hours=2)
        booked_till_moved = tznow() - timedelta(hours=1)

        body = _get_body(
            bci,
            booked_from_moved,
            booked_till_moved,
            booking,
            staffer,
        )

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body

        booking.refresh_from_db()
        booking.appointment.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.FINISHED

    def _prompted_reviews_base_setup(self, number_of_bookings):
        self.business.registration_source = get_or_create_booking_source(name=IPHONE_SOLO)
        self.business.save()
        for _ in range(0, number_of_bookings):
            create_subbooking(
                business=self.business,
                booking_type=Appointment.TYPE.CUSTOMER,
                booking_kws={
                    'status': Appointment.STATUS.FINISHED,
                },
            )

    @patch.object(Client, 'identify')
    @pytest.mark.freeze_time(TEST_DATE_TIME)
    def test_customer_booking_finished(
        self,
        analytics_identify_mock,
    ):
        self.business.primary_category = baker.make(
            BusinessCategory,
            type=BusinessCategory.CATEGORY,
            internal_name="PANFRYZJER",
        )
        self.business.save()
        ZipCodesToUrbanAreasMapper.objects.create(
            zip_code=self.business.region.name,
            urban_area='Test urban area',
            urban_subarea='Test suburban area',
            focus=True,
            focus_area='1',
        )
        now = tznow()
        booked_from = now - timedelta(hours=1)
        booked_till = now

        bci, booking, staffer = self._create_stuff(booked_from, booked_till, now)

        url = self._get_url(booking)

        body = _get_body(bci, booked_from, booked_till, booking, staffer)
        self.fetch(url, method='PUT', body=body)

        dict_assert(
            analytics_identify_mock.call_args_list[2][1],
            {
                'traits': {
                    'last_CB_finished_urban_area': 'Test urban area',
                    'last_booking_finished_category': 'PANFRYZJER',
                },
            },
        )

    @patch.object(Client, 'identify')
    @pytest.mark.freeze_time(TEST_DATE_TIME)
    def test_customer_booking_move_finished_last_urban_area_category(
        self,
        analytics_identify_mock,
    ):
        self.business.primary_category = baker.make(
            BusinessCategory, type=BusinessCategory.CATEGORY, internal_name="SuperFryzjer"
        )
        self.business.save()
        ZipCodesToUrbanAreasMapper.objects.create(
            zip_code=self.business.region.name,
            urban_area='Test urban area',
            urban_subarea='Test suburban area',
            focus=True,
            focus_area='1',
        )
        now = tznow()
        booked_from = now - timedelta(hours=1)
        booked_till = now

        bci, booking, staffer = self._create_stuff(booked_from, booked_till, now)

        url = self._get_url(booking)

        # move finished booking to futur
        booked_from_moved = tznow() + timedelta(hours=1)
        booked_till_moved = tznow() + timedelta(hours=2)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        self.fetch(url, method='PUT', body=body)

        # move finished booking to past
        booked_from_moved = tznow() - timedelta(hours=2)
        booked_till_moved = tznow() - timedelta(hours=1)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        self.fetch(url, method='PUT', body=body)

        dict_assert(
            analytics_identify_mock.call_args_list[2][1],
            {
                'traits': {
                    'last_CB_finished_urban_area': 'Test urban area',
                    'last_booking_finished_category': 'SuperFryzjer',
                },
            },
        )

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch(
        'webapps.notification.scenarios.scenarios_booking.spawn_booking_finished_analytics_events'
    )
    def test_customer_booking_analytics_even_if_review_not_needed(
        self,
        spawn_mock,
    ):
        bci = bci_recipe.make(business=self.business, user=self.user)
        appointment = create_appointment(
            {}, booked_for=bci, business=self.business, status=Appointment.STATUS.FINISHED
        )
        baker.make(Review, business=self.business, user=self.user)
        BookingFinishedScenario.plan({'appointment': appointment})
        assert spawn_mock.call_count == 1

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @override_eppo_feature_flag({SendTippingAppetiteExperimentPushFlag.flag_name: True})
    @patch(
        'webapps.notification.scenarios.scenarios_booking.BookingFinishedScenario.is_review_request_needed',  # pylint: disable=line-too-long
        Mock(return_value=False),
    )
    @patch(
        'webapps.notification.scenarios.scenarios_booking.BookingFinishedScenario._tipping_appetite_experiment'  # pylint: disable=line-too-long
    )
    def test_customer_booking_tipping(
        self,
        tipping_appetite_experiment_mock,
    ):
        bci = bci_recipe.make(business=self.business, user=self.user)
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            name='Staff X',
            position='Senior Barber',
        )

        service_variant = baker.prepare(
            'business.ServiceVariant', duration=relativedelta(minutes=30)
        )
        service_variant.staffer_ids = staffer.id

        subbookings, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                total_value=decimal.Decimal('100'),
                booked_for=bci,
            ),
        )
        BookingResource.objects.create(subbooking=subbookings, resource=staffer)

        BookingFinishedScenario.plan({'appointment': subbookings.appointment})
        assert tipping_appetite_experiment_mock.call_count == 1

    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @override_eppo_feature_flag(
        {
            SuggestiveReviewsExperiment.flag_name: ExperimentVariants.VARIANT_A,
        }
    )
    @patch.object(BookingFinishedScenario, 'is_review_request_needed', Mock(return_value=True))
    def test_plan__suggestive_reviews_experiment(self):
        bci = bci_recipe.make(business=self.business, user=self.user)
        appointment = create_appointment(
            {},
            booked_for=bci,
            business=self.business,
            type=Appointment.TYPE.CUSTOMER,
            status=Appointment.STATUS.FINISHED,
        )
        BookingFinishedScenario.plan({'appointment': appointment})
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_finished:immediate:appointment_id={appointment.id}',
        ).exists()

    @override_eppo_feature_flag({IncludeBookingIdInAddReviewPushTargetFlag.flag_name: True})
    @patch('webapps.notification.tasks.push.send_push_notification')
    def test_send_push__ff_enabled(self, send_push_notification_mock):
        bci = bci_recipe.make(business=self.business, user=self.user)
        self._create_customer_reciever()
        appointment = create_appointment(
            {},
            booked_for=bci,
            business=self.business,
            type=Appointment.TYPE.CUSTOMER,
            status=Appointment.STATUS.FINISHED,
        )
        assert appointment.first_booking_id is not None

        BookingFinishedScenario.send_push(appointment)
        send_push_notification_mock.assert_called_once()
        dict_assert(
            send_push_notification_mock.call_args.kwargs,
            {
                'alert': (
                    f'Please share your opinion about your visit at {self.business.name} '
                    'with other Booksy users!'
                ),
                'target': (
                    'business.add_review',
                    self.business.id,
                    appointment.first_booking_id,
                ),
                'history_data': {
                    'booking_id': appointment.first_booking_id,
                    'business_id': self.business.id,
                    'customer_id': bci.user_id,
                    'customer_card_id': bci.id,
                    'sender': NotificationHistory.SENDER_BUSINESS,
                },
            },
        )
        expected_receivers = notification_receivers_list(bci.user_id)
        assert send_push_notification_mock.call_args.kwargs['receivers'] == expected_receivers

    @override_eppo_feature_flag({IncludeBookingIdInAddReviewPushTargetFlag.flag_name: False})
    @patch('webapps.notification.tasks.push.send_push_notification')
    def test_send_push__ff_disabled(self, send_push_notification_mock):
        bci = bci_recipe.make(business=self.business, user=self.user)
        self._create_customer_reciever()
        appointment = create_appointment(
            {},
            booked_for=bci,
            business=self.business,
            type=Appointment.TYPE.CUSTOMER,
            status=Appointment.STATUS.FINISHED,
        )
        assert appointment.first_booking_id is not None

        BookingFinishedScenario.send_push(appointment)
        send_push_notification_mock.assert_called_once()
        dict_assert(
            send_push_notification_mock.call_args.kwargs,
            {
                'alert': (
                    f'Please share your opinion about your visit at {self.business.name} '
                    'with other Booksy users!'
                ),
                'target': ('business.add_review', self.business.id),
                'history_data': {
                    'booking_id': appointment.first_booking_id,
                    'business_id': self.business.id,
                    'customer_id': bci.user_id,
                    'customer_card_id': bci.id,
                    'sender': NotificationHistory.SENDER_BUSINESS,
                },
            },
        )
        expected_receivers = notification_receivers_list(bci.user_id)
        assert send_push_notification_mock.call_args.kwargs['receivers'] == expected_receivers

    @override_eppo_feature_flag(
        {
            SuggestiveReviewsExperiment.flag_name: ExperimentVariants.VARIANT_A,
        }
    )
    @patch('webapps.notification.tasks.push.send_push_notification')
    def test_send_push__suggestive_reviews_experiment(self, send_push_notification_mock):
        bci = bci_recipe.make(business=self.business, user=self.user)
        appointment = create_appointment(
            {},
            booked_for=bci,
            business=self.business,
            type=Appointment.TYPE.CUSTOMER,
            status=Appointment.STATUS.FINISHED,
        )

        BookingFinishedScenario.send_push(appointment)
        send_push_notification_mock.assert_called_once()
        assert send_push_notification_mock.call_args.kwargs['target'] == (
            'business.add_review',
            self.business.id,
            appointment.first_booking_id,
            'in_experiment',
        )

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_finished2futur2past(
        self,
        schedule_mock,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        self.business.primary_category = baker.make(
            BusinessCategory,
            type=BusinessCategory.CATEGORY,
        )
        self.business.save()
        ZipCodesToUrbanAreasMapper.objects.create(
            zip_code=self.business.region.name,
            urban_area='Test urban area',
            urban_subarea='Test suburban area',
            focus=True,
            focus_area='1',
        )
        now = tznow()
        booked_from = now - timedelta(hours=1)
        booked_till = now

        bci, booking, staffer = self._create_stuff(booked_from, booked_till, now)
        assert schedule_mock.call_count == 0

        url = self._get_url(booking)

        # move finished booking to futur
        booked_from_moved = tznow() + timedelta(hours=1)
        booked_till_moved = tznow() + timedelta(hours=2)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 0

        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.PROPOSED

        # move finished booking to past
        booked_from_moved = tznow() - timedelta(hours=2)
        booked_till_moved = tznow() - timedelta(hours=1)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.FINISHED
        assert schedule_mock.call_count == 1

        # twice time more beacause its finished and has been moved to past...
        # i will try avoid double event call later  # zolkiewski
        assert analytics_track_mock.call_count == 8
        assert analytics_identify_mock.call_count == 8
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                # 'user_id': None,
                'event': 'CB_Finished_For_Business',
                'properties': {
                    'country': settings.API_COUNTRY,
                    'email': booking.appointment.business.owner.email,
                    'customer_id': id_to_external_api(booking.appointment.booked_for.user.id),
                    'booked_from': '2015-05-05T10:00:00+00:00',
                    'category_id': [
                        id_to_external_api(booking.service_variant.service.service_category.id),
                    ],
                    'category_name': [booking.service_variant.service.service_category.name],
                    'service_id': [
                        id_to_external_api(
                            booking.service_variant.service.id,
                        )
                    ],
                    'service_name': ['service_name_aaa'],
                    'service_price': [],
                    'booking_score': 0.00,
                    'energy_booking': booking.appointment.get_chargeable(),
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': self.business.primary_category.internal_name,
                    'staff_id': [id_to_external_api(booking.staffer.id)],
                    'appointment_id': id_to_external_api(booking.appointment_id),
                    'urban_area': 'Test urban area',
                    'urban_subarea': 'Test suburban area',
                    'focus_area': '1',
                },
            },
        )

        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'traits': {
                    'user_role': UserRoleEnum.OWNER,
                    'business_CB_finished_count': 1,
                    'business_CB_finished_12m_count': 1,
                },
            },
        )
        dict_assert(
            analytics_track_mock.call_args_list[2][1],
            {
                'event': 'CB_Finished_For_Customer',
                'properties': {
                    'country': settings.API_COUNTRY,
                    'email': booking.appointment.booked_for.user.email,
                    'business_postal_code': self.business.zip,
                    # booked_from,
                    'category_id': [
                        id_to_external_api(booking.service_variant.service.service_category.id),
                    ],
                    'category_name': [booking.service_variant.service.service_category.name],
                    'service_id': [
                        id_to_external_api(
                            booking.service_variant.service.id,
                        )
                    ],
                    'service_name': ['service_name_aaa'],
                    'service_price': [],
                    'booking_score': 0,
                    'energy_booking': booking.appointment.get_chargeable(),
                    # source
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': (self.business.primary_category.internal_name),
                    'staff_id': [id_to_external_api(booking.staffer.id)],
                    'urban_area': 'Test urban area',
                    'urban_subarea': 'Test suburban area',
                    'focus_area': '1',
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[2][1],
            {
                'user_id': id_to_external_api(booking.appointment.booked_for.user_id),
                'traits': {
                    'email': booking.appointment.booked_for.user.email,
                    'CB_finished_count': 1,
                    'CB_finished_12m_count': 1,
                    'CB_created_merchants_count': 1,
                    'CB_finished_merchants_count': 1,
                    'CB_created_categories_count': 1,
                    'CB_finished_categories_count': 1,
                },
            },
        )
        dict_assert(
            analytics_track_mock.call_args_list[3][1],
            {
                'event': '1st_CB_Finished_For_Customer',
                'properties': {
                    'country': settings.API_COUNTRY,
                    'email': booking.appointment.booked_for.user.email,
                    'business_postal_code': self.business.zip,
                    # booked_from,
                    'category_id': [
                        id_to_external_api(booking.service_variant.service.service_category.id),
                    ],
                    'category_name': [booking.service_variant.service.service_category.name],
                    'service_id': [
                        id_to_external_api(
                            booking.service_variant.service.id,
                        )
                    ],
                    'service_name': ['service_name_aaa'],
                    'service_price': [],
                    'booking_score': 0,
                    # source
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': (self.business.primary_category.internal_name),
                    'staff_id': [id_to_external_api(booking.staffer.id)],
                    'appointment_id': id_to_external_api(booking.appointment_id),
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[3][1],
            {
                'traits': {
                    'email': booking.appointment.booked_for.user.email,
                    '1st_CB_finished': True,
                    'user_role': 'Owner',
                },
            },
        )

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_finished2futur2past_analytics_killswitch(
        self,
        schedule_mock,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.CB_FINISHED_FOR_BUSINESS,
            is_killed=True,
        )
        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.CB_FINISHED_FOR_CUSTOMER,
            is_killed=True,
        )

        self.business.primary_category = baker.make(
            BusinessCategory,
            type=BusinessCategory.CATEGORY,
        )
        self.business.save()
        ZipCodesToUrbanAreasMapper.objects.create(
            zip_code=self.business.region.name,
            urban_area='Test urban area',
            urban_subarea='Test suburban area',
            focus=True,
            focus_area='1',
        )
        now = tznow()
        booked_from = now - timedelta(hours=1)
        booked_till = now

        bci, booking, staffer = self._create_stuff(booked_from, booked_till, now)
        assert schedule_mock.call_count == 0

        url = self._get_url(booking)

        # move finished booking to futur
        booked_from_moved = tznow() + timedelta(hours=1)
        booked_till_moved = tznow() + timedelta(hours=2)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 0

        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.PROPOSED

        # move finished booking to past
        booked_from_moved = tznow() - timedelta(hours=2)
        booked_till_moved = tznow() - timedelta(hours=1)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.FINISHED
        assert schedule_mock.call_count == 1

        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    @override_settings(API_COUNTRY=Country.PL)
    def test_customer_booking_move_finished2futur2past_cb_counters(
        self,
        schedule_mock,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        self.business.primary_category = baker.make(
            BusinessCategory,
            type=BusinessCategory.CATEGORY,
        )
        self.business.save()
        self.booking_source = baker.make(BookingSources)
        other_business = baker.make(
            Business,
            primary_category=baker.make(BusinessCategory),
        )
        other_business_bci = baker.make(
            BusinessCustomerInfo,
            user=self.user,
            business=other_business,
        )
        old_booking = baker.prepare(
            SubBooking,
            appointment=baker.make(
                Appointment,
                business=other_business,
                booked_for=other_business_bci,
                type=Appointment.TYPE.CUSTOMER,
                status=Appointment.STATUS.ACCEPTED,
                source=self.booking_source,
                updated_by=self.user,
            ),
        )
        old_booking.save(override=True)
        ZipCodesToUrbanAreasMapper.objects.create(
            zip_code=self.business.region.name,
            urban_area='Test urban area',
            urban_subarea='Test suburban area',
            focus=True,
            focus_area='1',
        )
        now = tznow()
        booked_from = now - timedelta(hours=1)
        booked_till = now

        bci, booking, staffer = self._create_stuff(booked_from, booked_till, now)
        assert schedule_mock.call_count == 0

        url = self._get_url(booking)

        # move finished booking to futur
        booked_from_moved = tznow() + timedelta(hours=1)
        booked_till_moved = tznow() + timedelta(hours=2)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        assert NotificationHistoryDocument.task_count() == 0

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 0

        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.PROPOSED
        assert NotificationHistoryDocument.task_count() == 1
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_changed:customer_booking_reschedule_request:'
            f'appointment_id={booking.appointment_id}',
        ).first()

        # move finished booking to past
        booked_from_moved = tznow() - timedelta(hours=2)
        booked_till_moved = tznow() - timedelta(hours=1)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.FINISHED
        assert schedule_mock.call_count == 1
        assert NotificationHistoryDocument.task_count() == 1

        # twice time more beacause its finished and has been moved to past...
        # i will try avoid double event call later  # zolkiewski
        assert analytics_track_mock.call_count == 8
        assert analytics_identify_mock.call_count == 8

        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'user_id': id_to_external_api(self.user.id),
                'event': 'CB_Finished_For_Business',
                'properties': {
                    'appointment_id': id_to_external_api(booking.appointment_id),
                    'country': settings.API_COUNTRY,
                    'email': booking.appointment.business.owner.email,
                    'customer_id': id_to_external_api(booking.appointment.booked_for.user.id),
                    'category_id': [
                        id_to_external_api(booking.service_variant.service.service_category.id),
                    ],
                    'category_name': [booking.service_variant.service.service_category.name],
                    'service_id': [
                        id_to_external_api(
                            booking.service_variant.service.id,
                        )
                    ],
                    'service_name': ['service_name_aaa'],
                    'service_price': [],
                    'booking_score': 0,
                    'energy_booking': booking.appointment.get_chargeable(),
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': (self.business.primary_category.internal_name),
                    'staff_id': [id_to_external_api(booking.staffer.id)],
                    'urban_area': 'Test urban area',
                    'urban_subarea': 'Test suburban area',
                    'focus_area': '1',
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'traits': {
                    'user_role': UserRoleEnum.OWNER,
                    'business_CB_finished_count': 1,
                    'business_CB_finished_12m_count': 1,
                },
            },
        )
        dict_assert(
            analytics_track_mock.call_args_list[1][1],
            {
                'event': '1st_CB_Finished_For_Business',
                'properties': {
                    'appointment_id': id_to_external_api(booking.appointment_id),
                    'country': settings.API_COUNTRY,
                    'email': booking.appointment.booked_for.user.email,
                    'customer_id': id_to_external_api(booking.appointment.booked_for.user.id),
                    'category_id': [
                        id_to_external_api(booking.service_variant.service.service_category.id),
                    ],
                    'category_name': [booking.service_variant.service.service_category.name],
                    'service_id': [
                        id_to_external_api(
                            booking.service_variant.service.id,
                        )
                    ],
                    'service_name': ['service_name_aaa'],
                    'service_price': [],
                    'booking_score': 0,
                    'energy_booking': booking.appointment.get_chargeable(),
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': (self.business.primary_category.internal_name),
                    'staff_id': [id_to_external_api(booking.staffer.id)],
                },
            },
        )
        dict_assert(
            analytics_track_mock.call_args_list[2][1],
            {
                'event': 'CB_Finished_For_Customer',
                'properties': {
                    'country': settings.API_COUNTRY,
                    'email': booking.appointment.booked_for.user.email,
                    'business_postal_code': booking.appointment.business.zip,
                    'category_id': [
                        id_to_external_api(booking.service_variant.service.service_category.id),
                    ],
                    'category_name': [booking.service_variant.service.service_category.name],
                    'service_id': [
                        id_to_external_api(
                            booking.service_variant.service.id,
                        )
                    ],
                    'service_name': ['service_name_aaa'],
                    'service_price': [],
                    'booking_score': 0,
                    'energy_booking': booking.appointment.get_chargeable(),
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': (self.business.primary_category.internal_name),
                    'staff_id': [id_to_external_api(booking.staffer.id)],
                    'appointment_id': id_to_external_api(booking.appointment_id),
                    'urban_area': 'Test urban area',
                    'urban_subarea': 'Test suburban area',
                    'focus_area': '1',
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[1][1],
            {
                'traits': {
                    'user_role': 'Owner',
                    'email': self.business.owner.email,
                    'business_1st_CB_finished': True,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[2][1],
            {
                'user_id': id_to_external_api(booking.appointment.booked_for.user_id),
                'traits': {
                    'email': booking.appointment.booked_for.user.email,
                    'CB_finished_count': 1,
                    'CB_finished_12m_count': 1,
                    'CB_created_merchants_count': 2,
                    'CB_finished_merchants_count': 1,
                    'CB_created_categories_count': 2,
                    'CB_finished_categories_count': 1,
                },
            },
        )

    @pytest.mark.random_failure  # https://booksy.atlassian.net/browse/PY-199
    @patch('lib.celery_tools.is_replica_synced')
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @pytest.mark.freeze_time(TEST_DATE_TIME)
    @patch.object(BookingFinishedScenario, 'add_to_schedule')
    def test_customer_booking_move_finished2futur2past_with_retry(
        self,
        schedule_mock,
        analytics_track_mock,
        analytics_identify_mock,
        is_replica_synced_mock,
    ):
        ZipCodesToUrbanAreasMapper.objects.create(
            zip_code=self.business.region.name,
            urban_area='Test urban area',
            urban_subarea='Test suburban area',
            focus=True,
            focus_area='1',
        )
        now = tznow()
        booked_from = now - timedelta(hours=1)
        booked_till = now

        bci, booking, staffer = self._create_stuff(booked_from, booked_till, now)
        assert schedule_mock.call_count == 0

        url = self._get_url(booking)

        # move finished booking to futur
        booked_from_moved = tznow() + timedelta(hours=1)
        booked_till_moved = tznow() + timedelta(hours=2)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        assert schedule_mock.call_count == 0
        assert NotificationHistoryDocument.task_count() == 1
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_changed:customer_booking_reschedule_request:'
            f'appointment_id={booking.appointment_id}',
        ).first()

        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.PROPOSED

        # move finished booking to past
        booked_from_moved = tznow() - timedelta(hours=2)
        booked_till_moved = tznow() - timedelta(hours=1)

        body = _get_body(bci, booked_from_moved, booked_till_moved, booking, staffer)

        is_replica_synced_mock.side_effect = [False, True]

        resp = self.fetch(url, method='PUT', body=body)

        assert resp.code == 200, resp.body
        booking.refresh_from_db()
        assert booking.appointment.status == Appointment.STATUS.FINISHED
        assert schedule_mock.call_count == 1
        assert NotificationHistoryDocument.task_count() == 1

        # because of is_replica_synced mock side effect
        event_names = set(x[1]['event'] for x in analytics_track_mock.call_args_list)
        assert analytics_track_mock.call_count == 2
        assert analytics_identify_mock.call_count == 2
        assert event_names == {
            'CB_Finished_For_Customer',
            '1st_CB_Finished_For_Customer',
        }
