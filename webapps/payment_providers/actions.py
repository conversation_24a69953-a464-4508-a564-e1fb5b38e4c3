from dataclasses import asdict

from dacite import from_dict
from django.conf import settings
from django.db.models.signals import post_save
from django.dispatch import receiver

from country_config import Country
from lib.business_consents.events import (
    account_holder_siren_consent_accepted_event,
    account_holder_nip_consent_accepted_event,
    account_holder_pesel_consent_accepted_event,
    user_input_kyc_consent_accepted_event,
)

from lib.events import async_event_receiver
from lib.payment_providers.entities import (
    AccountHolderExternalSourceAdditionalData,
    AccountHolderUserInputKYCAdditionalData,
    StripeAccountHolderUserInputKYCAdditionalData,
    UserInputKYCAccountHolderData,
    USUserInputKYCAccountHolderData,
    CreateStripeAccountHolderAdditionalData,
    ExternalSourceAccountHolderData,
    FRExternalSourceAccountHolderData,
    StripeAccountHolderExternalSourceAdditionalData,
    AccountHolderSirenEntity,
    AccountHolderNIPEntity,
    PLExternalSourceAccountHolderData,
    AccountHolderPESELEntity,
    USUserInputKYCEntity,
)
from lib.payment_providers.events import payment_providers_stripe_account_holder_updated_event
from lib.payments.enums import PaymentProviderCode
from lib.pos.utils import pos_refactor_stage2_enabled
from webapps.market_pay.models import AccountHolder as AdyenAccountV1
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.adapters.notifications import NotificationsAdapter
from webapps.payment_providers.exceptions.stripe import StripeActionNotImplemented
from webapps.payment_providers.models import (
    AccountHolder,
    AdyenAccountHolder,
    Customer,
    StripeAccountHolder,
    StripeCustomer,
    StripeLocation,
)
from webapps.payment_providers.services.account_holder import CommonAccountHolderServices
from webapps.payment_providers.services.stripe.account_holder import StripeAccountHolderServices
from webapps.stripe_integration.models import (
    StripeAccount as StripeAccountV1,
    StripeCustomer as StripeCustomerV1,
    StripeLocation as StripeLocationV1,
)
from webapps.payment_providers.models.stripe import StripeAccountHolderSettings


@receiver(post_save, sender=AdyenAccountV1)
def synchronize_adyen_account_holder(instance, **kwargs):
    if not pos_refactor_stage2_enabled(pos=instance.pos):
        return

    from webapps.payment_gateway.models import Wallet

    wallet = Wallet.objects.filter(
        business_id=instance.pos.business_id,
    ).first()
    if not wallet:
        return

    account_holder = AccountHolder.objects.filter(
        id=wallet.account_holder_id,
    ).first()
    if not account_holder:
        return

    data = {
        'external_id': instance.account_holder_code,
        'account_code': instance.account_code,
        'payouts_enabled': instance.payout_allowed,
        'kyc_verified_at_least_once': instance.ever_passed_kyc,
        'deleted': instance.deleted,
    }

    if not AdyenAccountHolder.objects.filter(
        account_holder_id=account_holder.id,
        external_id=instance.account_holder_code,
    ).exists():
        AdyenAccountHolder.objects.create(
            account_holder=account_holder,
            **data,
        )

    AdyenAccountHolder.objects.filter(
        account_holder_id=account_holder.id,
        external_id=instance.account_holder_code,
    ).update(
        updated=instance.updated,
        created=instance.created,
        **data,
    )


@receiver(post_save, sender=StripeAccountV1)
def synchronize_stripe_account_holder(instance: StripeAccountV1, **kwargs):
    pos = instance.pos
    from webapps.payment_gateway.models import Wallet

    try:
        wallet = Wallet.objects.get(
            business_id=pos.business_id,
        )
    except Wallet.DoesNotExist:
        return

    data = {
        'external_id': instance.external_id,
        'payouts_enabled': instance.payouts_enabled,
        'charges_enabled': instance.charges_enabled,
        'blocked': instance.blocked,
        'charge_fee_for_tips': instance.charge_for_tips,
        'account_type': instance.account_type,
        'status': instance.status,
        'kyc_verified_at_least_once': instance.kyc_verified_at_least_once,
        'tos_acceptance_date': instance.tos_acceptance_date,
        'onboarding_info': instance.onboarding_info,
        'fast_payout_max_limit': instance.fast_payout_max_limit,
        'fast_payout_min_limit': instance.fast_payout_min_limit,
        'fast_payout_merchant_max_limit': instance.fast_payout_merchant_max_limit,
        'default_payout_method_for_fast_payout': instance.default_payout_method_for_fast_payout,
        'account_link_first_time_created': instance.account_link_first_time_created,
    }

    if instance.deleted:
        StripeAccountHolder.objects.filter(
            external_id=instance.external_id,
        ).delete()
        return

    _, is_created = StripeAccountHolder.objects.update_or_create(
        account_holder_id=wallet.account_holder_id,
        defaults=data,
    )

    StripeAccountHolderSettings.objects.get_or_create(
        account_holder_id=wallet.account_holder_id,
    )

    update_data = (
        {
            'updated': instance.updated,
            'created': instance.created,
        }
        if is_created
        else {'updated': instance.updated}
    )

    StripeAccountHolder.objects.filter(
        external_id=instance.external_id,
    ).update(**update_data)

    stripe_account_holder = StripeAccountHolder.objects.get(external_id=instance.external_id)
    StripeAccountHolderServices.save_account_holder_to_history(stripe_account_holder)
    payment_providers_stripe_account_holder_updated_event.send(
        asdict(stripe_account_holder.account_holder.entity),
    )


@receiver(post_save, sender=StripeCustomerV1)
def synchronize_stripe_customer(instance, **kwargs):
    from webapps.payment_gateway.models import Wallet

    if StripeCustomer.objects.filter(external_id=instance.external_id).exists():
        return

    try:
        wallet = Wallet.objects.get(
            user_id=instance.user_id,
        )
    except Wallet.DoesNotExist:
        return

    customer = Customer.objects.get(
        id=wallet.customer_id,
    )
    StripeCustomer.objects.create(
        customer=customer,
        external_id=instance.external_id,
    )


@receiver(post_save, sender=StripeLocationV1)
def synchronize_stripe_location(instance, **kwargs):
    data = {
        'reader_configuration_external_id': instance.reader_configuration_id,
        'name': instance.name,
    }

    account_id = (
        StripeAccountHolder.objects.filter(external_id=instance.account.external_id)
        .values_list('id', flat=True)
        .get()
    )
    _, is_created = StripeLocation.objects.update_or_create(
        account_id=account_id,
        external_id=instance.external_id,
        defaults=data,
    )

    update_data = (
        {
            'updated': instance.updated,
            'created': instance.created,
        }
        if is_created
        else {'updated': instance.updated}
    )

    StripeLocation.objects.filter(
        external_id=instance.external_id,
    ).update(**update_data)


@async_event_receiver(account_holder_siren_consent_accepted_event)
def migrate_account_holder_to_stripe_by_siren(
    account_holder_siren_entity: dict,
    **kwargs,
):
    if settings.API_COUNTRY == Country.FR:
        account_holder_siren_entity = from_dict(
            data_class=AccountHolderSirenEntity,
            data=account_holder_siren_entity,
        )
        additional_data = AccountHolderExternalSourceAdditionalData(
            stripe=StripeAccountHolderExternalSourceAdditionalData(
                provider_data=CreateStripeAccountHolderAdditionalData(
                    business_id=account_holder_siren_entity.business_id,
                ),
                external_source_data=ExternalSourceAccountHolderData(
                    fr=FRExternalSourceAccountHolderData(
                        SIREN=account_holder_siren_entity.siren,
                    ),
                ),
            ),
        )
    else:
        raise StripeActionNotImplemented(
            f'migrate_account_holder_to_stripe_by_siren is not implemented '
            f'on {settings.API_COUNTRY} country',
        )
    account_holder = AccountHolder.objects.get(
        id=PaymentGatewayPort.get_business_wallet(
            business_id=account_holder_siren_entity.business_id,
        ).account_holder_id,
    )
    success = CommonAccountHolderServices.create_provider_account_holder_from_external_source(
        account_holder=account_holder,
        payment_provider_code=PaymentProviderCode.STRIPE,
        additional_data=additional_data,
    )
    if success:
        NotificationsAdapter.send_stripe_pappers_processing_finished_notification(
            account_holder=account_holder,
        )


@async_event_receiver(account_holder_nip_consent_accepted_event)
def migrate_account_holder_to_stripe_by_nip(
    account_holder_nip_entity: dict,
    **kwargs,
):
    if settings.API_COUNTRY != Country.PL:
        raise StripeActionNotImplemented(
            f'migrate_account_holder_to_stripe_by_nip is not implemented '
            f'on {settings.API_COUNTRY} country',
        )
    account_holder_nip_entity = from_dict(
        data_class=AccountHolderNIPEntity,
        data=account_holder_nip_entity,
    )
    additional_data = AccountHolderExternalSourceAdditionalData(
        stripe=StripeAccountHolderExternalSourceAdditionalData(
            provider_data=CreateStripeAccountHolderAdditionalData(
                business_id=account_holder_nip_entity.business_id,
            ),
            external_source_data=ExternalSourceAccountHolderData(
                pl=PLExternalSourceAccountHolderData(
                    NIP=account_holder_nip_entity.nip,
                    date_of_birth=account_holder_nip_entity.date_of_birth,
                ),
            ),
        ),
    )

    account_holder = AccountHolder.objects.get(
        id=PaymentGatewayPort.get_business_wallet(
            business_id=account_holder_nip_entity.business_id,
        ).account_holder_id,
    )

    _success = CommonAccountHolderServices.create_provider_account_holder_from_external_source(
        account_holder=account_holder,
        payment_provider_code=PaymentProviderCode.STRIPE,
        additional_data=additional_data,
    )


@async_event_receiver(account_holder_pesel_consent_accepted_event)
def migrate_account_holder_to_stripe_by_pesel(
    account_holder_pesel_entity: dict,
    **kwargs,
):
    if settings.API_COUNTRY != Country.PL:
        raise StripeActionNotImplemented(
            f'migrate_account_holder_to_stripe_by_pesel is not implemented '
            f'on {settings.API_COUNTRY} country',
        )

    account_holder_pesel_entity = from_dict(
        data_class=AccountHolderPESELEntity,
        data=account_holder_pesel_entity,
    )
    account_holder = AccountHolder.objects.get(
        id=PaymentGatewayPort.get_business_wallet(
            business_id=account_holder_pesel_entity.business_id,
        ).account_holder_id,
    )

    additional_data = AccountHolderExternalSourceAdditionalData(
        stripe=StripeAccountHolderExternalSourceAdditionalData(
            provider_data=CreateStripeAccountHolderAdditionalData(
                business_id=account_holder_pesel_entity.business_id,
            ),
            external_source_data=ExternalSourceAccountHolderData(
                pl=PLExternalSourceAccountHolderData(
                    NIP=None,
                    date_of_birth=account_holder_pesel_entity.date_of_birth,
                    first_name=account_holder_pesel_entity.first_name,
                    last_name=account_holder_pesel_entity.last_name,
                ),
            ),
        ),
    )

    _success = CommonAccountHolderServices.create_provider_account_holder_from_external_source(
        account_holder=account_holder,
        payment_provider_code=PaymentProviderCode.STRIPE,
        additional_data=additional_data,
    )


@async_event_receiver(user_input_kyc_consent_accepted_event)
def create_stripe_account_user_input_kyc(
    us_user_input_kyc_entity: dict,
    **kwargs,
):
    if settings.API_COUNTRY != Country.US:
        raise StripeActionNotImplemented(
            f'create_stripe_account_user_input_kyc is not implemented '
            f'on {settings.API_COUNTRY} country',
        )

    us_user_input_kyc_entity = from_dict(
        data_class=USUserInputKYCEntity,
        data=us_user_input_kyc_entity,
    )
    account_holder = AccountHolder.objects.get(
        id=PaymentGatewayPort.get_business_wallet(
            business_id=us_user_input_kyc_entity.business_id,
        ).account_holder_id,
    )

    additional_data = AccountHolderUserInputKYCAdditionalData(
        stripe=StripeAccountHolderUserInputKYCAdditionalData(
            provider_data=CreateStripeAccountHolderAdditionalData(
                business_id=us_user_input_kyc_entity.business_id,
            ),
            user_input_kyc_data=UserInputKYCAccountHolderData(
                us=USUserInputKYCAccountHolderData(
                    type=us_user_input_kyc_entity.type,
                    filling_ip=us_user_input_kyc_entity.filling_ip,
                    filling_date=us_user_input_kyc_entity.filling_date,
                    first_name=us_user_input_kyc_entity.first_name,
                    last_name=us_user_input_kyc_entity.last_name,
                    company_name=us_user_input_kyc_entity.company_name,
                ),
            ),
        ),
    )
    CommonAccountHolderServices.create_provider_account_holder_with_user_input_kyc(
        account_holder=account_holder,
        payment_provider_code=PaymentProviderCode.STRIPE,
        additional_data=additional_data,
    )
