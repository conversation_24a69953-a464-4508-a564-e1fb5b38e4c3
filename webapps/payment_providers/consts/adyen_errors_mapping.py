from lib.payments.enums import PaymentError
from webapps.adyen.consts import oper_result
from webapps.payment_providers.consts.adyen import ErrorCode, RefusalReason

COMMON_PAYMENT_ERROR_TO_ADYEN_REFUSAL_REASON_OR_ERROR_CODE_MAPPING = {
    PaymentError.CONNECTION_ERROR: [],
    PaymentError.GENERIC_ERROR: [
        RefusalReason.UNKNOWN,
    ],
    PaymentError.GENERIC_PAYMENT_ERROR: [],
    PaymentError.GENERIC_CARD_ERROR: [
        RefusalReason.REVOCATION_OF_AUTH,
    ],
    PaymentError.NOT_PERMITTED: [
        RefusalReason.TRANSACTION_NOT_PERMITTED,
        RefusalReason.REFUSED,
        ErrorCode.PAYMENT_DETAILS_ARE_NOT_SUPPORTED,
    ],
    PaymentError.FRAUDULENT: [
        RefusalReason.FRAUD,
    ],
    PaymentError.RESTRICTED_CARD: [
        RefusalReason.RESTRICTED_CARD,
        RefusalReason.BLOCKED_CARD,
    ],
    PaymentError.MERCHANT_BLACKLIST: [],
    PaymentError.REENTER_TRANSACTION: [
        ErrorCode.CONTRACT_NOT_FOUND,
    ],
    PaymentError.BANK_ACCOUNT_VERIFICATION_FAILED: [],
    PaymentError.DEBIT_NOT_AUTHORIZED: [],
    PaymentError.REFER_TO_CUSTOMER: [],
    PaymentError.ROUTING_NUMBER_INVALID: [],
    PaymentError.ISSUER_NOT_AVAILABLE: [],
    PaymentError.BANK_ACCOUNT_INVALID: [],
    PaymentError.PROCESSING_ERROR_CARD: [],
    PaymentError.AUTHENTICATION_REQUIRED: [],
    PaymentError.TRANSFERS_NOT_ALLOWED: [],
    PaymentError.STRIPE_ACCOUNT_PROBLEM: [],
    PaymentError.ACCOUNT_COUNTRY_INVALID_ADDRESS: [],
    PaymentError.COUNTRY_UNSUPPORTED: [],
    PaymentError.CARD_NOT_SUPPORTED: [
        RefusalReason.NOT_SUPPORTED,
    ],
    PaymentError.CURRENCY_NOT_SUPPORTED: [],
    PaymentError.INVALID_ACCOUNT_CARD: [],
    PaymentError.ACCOUNT_INVALID: [],
    PaymentError.ACCOUNT_NOT_VERIFIED_YET: [],
    PaymentError.EXPIRED_CARD: [
        RefusalReason.EXPIRED_CARD,
    ],
    PaymentError.INCORRECT_CVC: [
        RefusalReason.CVC_DECLINED,
        ErrorCode.INVALID_CVC,
        ErrorCode.INVALID_CVC_LENGTH,
    ],
    PaymentError.INCORRECT_NUMBER: [
        RefusalReason.INVALID_CARD_NUMBER,
        ErrorCode.INVALID_CCN,
    ],
    PaymentError.TAX_ID_INVALID: [],
    PaymentError.TAXES_CALCULATION_FAILED: [],
    PaymentError.INVALID_EXPIRY_MONTH: [
        ErrorCode.INVALID_EXPIRY_MONTH_OR_BEFORE_NOW,
        ErrorCode.INVALID_EXPIRY_MONTH,
    ],
    PaymentError.INVALID_EXPIRY_YEAR: [
        ErrorCode.INVALID_EXPIRY_DATE,
    ],
    PaymentError.INVALID_CHARACTERS: [],
    PaymentError.INCORRECT_PIN: [
        RefusalReason.INVALID_PIN,
    ],
    PaymentError.INCORRECT_ZIP: [],
    PaymentError.PIN_REQUIRED: [
        RefusalReason.PIN_VALIDATION_NOT_POSSIBLE,
    ],
    PaymentError.CARD_DECLINE_RATE_LIMIT_EXCEEDED: [],
    PaymentError.CARD_DECLINED: [],
    PaymentError.PHONE_NUMBER_REQUIRED: [],
    PaymentError.EMAIL_INVALID: [],
    PaymentError.INCORRECT_ADDRESS: [],
    PaymentError.STATE_UNSUPPORTED: [],
    PaymentError.PIN_TRY_EXCEEDED: [
        RefusalReason.PIN_TRIES_EXCEEDED,
    ],
    PaymentError.TERMINAL_LOCATION_COUNTRY_UNSUPPORTED: [],
    PaymentError.INSUFFICIENT_FUNDS: [
        RefusalReason.NOT_ENOUGH_BALANCE,
    ],
    PaymentError.AMOUNT_TOO_LARGE: [],
    PaymentError.AMOUNT_TOO_SMALL: [],
    PaymentError.INVALID_AMOUNT: [
        RefusalReason.INVALID_AMOUNT,
        RefusalReason.WITHDRAWAL_AMOUNT_EXCEEDED,
    ],
    PaymentError.CHARGE_ALREADY_CAPTURED: [],
    PaymentError.CHARGE_ALREADY_REFUNDED: [],
    PaymentError.TECHNICAL_PROBLEM: [],
    PaymentError.INSTANT_PAYOUTS_UNSUPPORTED: [],
    PaymentError.PAYOUTS_NOT_ALLOWED: [],
    PaymentError.TIMEOUT: [],
    PaymentError.ORDER_CREATION_FAILED: [],
    PaymentError.ORDER_REQUIRED_SETTINGS: [],
    PaymentError.ORDER_STATUS_INVALID: [],
    PaymentError.SHIPPING_CALCULATION_FAILED: [],
    PaymentError.OUT_OF_INVENTORY: [],
    PaymentError.PAYMENT_EXPIRED: [],
    PaymentError.PAYMENT_FAILED: [],
    PaymentError.URL_INVALID: [],
    PaymentError.THREE_D_SECURE_PROBLEM: [
        RefusalReason.THREE_DS_AUTHENTICATION_FAILED,
        RefusalReason.AUTHENTICATION_REQUIRED,
    ],
}


ADYEN_REFUSAL_REASON_AND_ERROR_CODE_TO_COMMON_PAYMENT_ERROR_MAPPING = {}
for (
    common_error,
    adyen_errors,
) in COMMON_PAYMENT_ERROR_TO_ADYEN_REFUSAL_REASON_OR_ERROR_CODE_MAPPING.items():
    for adyen_error in adyen_errors:
        ADYEN_REFUSAL_REASON_AND_ERROR_CODE_TO_COMMON_PAYMENT_ERROR_MAPPING[adyen_error] = (
            common_error
        )


COMMON_PAYMENT_ERROR_TO_OPER_RESULT_MAPPING = {
    PaymentError.CONNECTION_ERROR: [],
    PaymentError.GENERIC_ERROR: [
        oper_result.UNKNOWN_ERROR,
    ],
    PaymentError.GENERIC_PAYMENT_ERROR: [],
    PaymentError.GENERIC_CARD_ERROR: [
        oper_result.REVOCATION_OF_AUTH,
    ],
    PaymentError.NOT_PERMITTED: [
        oper_result.TRANSACTION_NOT_PERMITTED,
        oper_result.PAYMENT_DETAILS_ARE_NOT_SUPPORTED,
        oper_result.REFUSED,
    ],
    PaymentError.FRAUDULENT: [
        oper_result.FRAUD,
    ],
    PaymentError.RESTRICTED_CARD: [
        oper_result.RESTRICTED_CARD,
        oper_result.BLOCKED_CARD,
    ],
    PaymentError.MERCHANT_BLACKLIST: [],
    PaymentError.REENTER_TRANSACTION: [
        oper_result.NOT_SUPPORTED,
        oper_result.CONTRACT_NOT_FOUND,
    ],
    PaymentError.BANK_ACCOUNT_VERIFICATION_FAILED: [],
    PaymentError.DEBIT_NOT_AUTHORIZED: [],
    PaymentError.REFER_TO_CUSTOMER: [],
    PaymentError.ROUTING_NUMBER_INVALID: [],
    PaymentError.ISSUER_NOT_AVAILABLE: [],
    PaymentError.BANK_ACCOUNT_INVALID: [],
    PaymentError.PROCESSING_ERROR_CARD: [],
    PaymentError.AUTHENTICATION_REQUIRED: [],
    PaymentError.TRANSFERS_NOT_ALLOWED: [],
    PaymentError.STRIPE_ACCOUNT_PROBLEM: [],
    PaymentError.ACCOUNT_COUNTRY_INVALID_ADDRESS: [],
    PaymentError.COUNTRY_UNSUPPORTED: [],
    PaymentError.CARD_NOT_SUPPORTED: [
        oper_result.NOT_SUPPORTED,
    ],
    PaymentError.CURRENCY_NOT_SUPPORTED: [],
    PaymentError.INVALID_ACCOUNT_CARD: [],
    PaymentError.ACCOUNT_INVALID: [],
    PaymentError.ACCOUNT_NOT_VERIFIED_YET: [],
    PaymentError.EXPIRED_CARD: [
        oper_result.EXPIRED_CARD,
    ],
    PaymentError.INCORRECT_CVC: [
        oper_result.CVC_DECLINED,
        oper_result.INVALID_CVC_LENGTH,
        oper_result.INVALID_CVC,
    ],
    PaymentError.INCORRECT_NUMBER: [
        oper_result.INVALID_CCN,
        oper_result.INVALID_CARD_NUMBER,
    ],
    PaymentError.TAX_ID_INVALID: [],
    PaymentError.TAXES_CALCULATION_FAILED: [],
    PaymentError.INVALID_EXPIRY_MONTH: [
        oper_result.INVALID_EXPIRY_MONTH_OR_BEFORE_NOW,
        oper_result.INVALID_EXPIRY_MONTH,
    ],
    PaymentError.INVALID_EXPIRY_YEAR: [
        oper_result.INVALID_EXPIRY_DATE,
    ],
    PaymentError.INVALID_CHARACTERS: [],
    PaymentError.INCORRECT_PIN: [
        oper_result.INVALID_PIN,
    ],
    PaymentError.INCORRECT_ZIP: [],
    PaymentError.PIN_REQUIRED: [
        oper_result.PIN_VALIDATION_NOT_POSSIBLE,
    ],
    PaymentError.CARD_DECLINE_RATE_LIMIT_EXCEEDED: [],
    PaymentError.CARD_DECLINED: [],
    PaymentError.PHONE_NUMBER_REQUIRED: [],
    PaymentError.EMAIL_INVALID: [],
    PaymentError.INCORRECT_ADDRESS: [],
    PaymentError.STATE_UNSUPPORTED: [],
    PaymentError.PIN_TRY_EXCEEDED: [
        oper_result.PIN_TRIES_EXCEEDED,
    ],
    PaymentError.TERMINAL_LOCATION_COUNTRY_UNSUPPORTED: [],
    PaymentError.INSUFFICIENT_FUNDS: [
        oper_result.NOT_ENOUGH_BALANCE,
    ],
    PaymentError.AMOUNT_TOO_LARGE: [],
    PaymentError.AMOUNT_TOO_SMALL: [],
    PaymentError.INVALID_AMOUNT: [
        oper_result.INVALID_AMOUNT,
        oper_result.WITHDRAWAL_AMOUNT_EXCEEDED,
    ],
    PaymentError.CHARGE_ALREADY_CAPTURED: [],
    PaymentError.CHARGE_ALREADY_REFUNDED: [],
    PaymentError.TECHNICAL_PROBLEM: [],
    PaymentError.INSTANT_PAYOUTS_UNSUPPORTED: [],
    PaymentError.PAYOUTS_NOT_ALLOWED: [],
    PaymentError.TIMEOUT: [],
    PaymentError.ORDER_CREATION_FAILED: [],
    PaymentError.ORDER_REQUIRED_SETTINGS: [],
    PaymentError.ORDER_STATUS_INVALID: [],
    PaymentError.SHIPPING_CALCULATION_FAILED: [],
    PaymentError.OUT_OF_INVENTORY: [],
    PaymentError.PAYMENT_EXPIRED: [],
    PaymentError.PAYMENT_FAILED: [],
    PaymentError.URL_INVALID: [],
    PaymentError.THREE_D_SECURE_PROBLEM: [
        oper_result.THREE_DS_AUTHENTICATION_REQUIRED,
        oper_result.THREE_DS_AUTHENTICATION_FAILED,
    ],
}

OPER_RESULT_TO_COMMON_PAYMENT_ERROR_MAPPING = {}
for common_payment_error, oper_results in COMMON_PAYMENT_ERROR_TO_OPER_RESULT_MAPPING.items():
    for oper_result in oper_results:
        OPER_RESULT_TO_COMMON_PAYMENT_ERROR_MAPPING[oper_result] = common_payment_error
