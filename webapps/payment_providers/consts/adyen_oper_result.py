# pylint: disable=duplicate-code

from webapps.payment_providers.consts.adyen import RefusalReason

###############################################################################
# success 1****
SUCCESS = 10001
PENDING = 10002  # this is "success, but not confirmed by notification"

###############################################################################
# security/transaction problems 5****
FRAUD = 50001
BLOCKED_CARD = 50002
CVC_DECLINED = 50003
EXPIRED_CARD = 50004
INVALID_AMOUNT = 50005
INVALID_CARD_NUMBER = 50006
INVALID_PIN = 50007
ISSUER_UNAVAILABLE = 50008
NOT_ENOUGH_BALANCE = 50009
NOT_SUBMITTED = 50010
NOT_SUPPORTED = 50011
BETWEEN_STATES = 50012
PIN_TRIES_EXCEEDED = 50013
PIN_VALIDATION_NOT_POSSIBLE = 50014
REFERRAL = 50015
RESTRICTED_CARD = 50016
REVOCATION_OF_AUTH = 50017
SHOPPER_CANCELLED = 50018
WITHDRAWAL_COUNT_EXCEEDED = 50019
WITHDRAWAL_AMOUNT_EXCEEDED = 50020
TRANSACTION_NOT_PERMITTED = 50021

###############################################################################
# generic/random/something went wrong 6****
NOT_ALLOWED = 60001
DUPLICATED_SESSION = 60003
UNABLE_TO_PROCESS = 60004
SERVER_COULD_NOT_PROCESS_REQUEST = 60005
ERROR_STORING_DEBTOR = 60006
ERROR_STORING_INVOICE = 60007
ERROR_CHECKING_IF_INVOICE_ALREADY_EXISTS_FOR_CREDIT_OR_ACCOUNT = 60008
ERROR_SEARCHING_INVOICES = 60009
FAILED_TO_DISABLE = 60010
RECURRING_DETAIL_REFERENCE_NOT_AVAILABLE_FOR_RECURRING = 60011
NO_APPLICABLE_CONTRACTTYPES_LEFT_FOR_THIS_PAYMENT_METHOD = 60012
SHOULDNT_HAVE_GOTTEN_HERE_WITHOUT_A_REQUEST = 60013
UNABLE_TO_LOAD_PRIVATE_KEY_FOR_DECRYPTION = 60014
CANCELLED = 60015  # transaction was cancelled
REFUSED = 60016
ACQUIRER_ERROR = 60017

###############################################################################
# bad request 7****

# generic/random 70***
INVALID_REQUEST = 70001
INVALID_PARES = 70002
INVALID_START_MONTH_OR_IN_THE_FUTURE = 70003
MERCHANT_RETURN_URL_TOO_LONG = 70004
INVALID_ISSUER_COUNTRYCODE = 70005
INVALID_NUMBER_OF_INSTALLMENTS = 70006
NO_ACQUIRER_SPECIFIED = 70007
NO_AUTHORISATION_MID_SPECIFIED = 70008
NO_FIELDS_SPECIFIED = 70009
REQUIRED_FIELD_NOT_SPECIFIED = 70010
INVALID_NUMBER_OF_REQUESTS = 70011
NOT_ALLOWED_TO_STORE_PAYOUT_DETAILS = 70012
AUTO_CAPTURE_DELAY_INVALID_OR_OUT_OF_RANGE = 70013
MANDATE_ID_DOES_NOT_MATCH_PATTERN = 70014
AUTHORISATIONCODE_REQUIRED_FOR_THIS_OPERATION = 70015
GENERATION_DATE_REQUIRED_BUT_MISSING = 70016
UNABLE_TO_PARSE_GENERATION_DATE = 70017
ENCRYPTED_DATA_USED_OUTSIDE_OF_VALID_TIME_PERIOD = 70018
UNABLE_TO_DECRYPT_DATA = 70019
UNABLE_TO_PARSE_JSON_DATA = 70020
INVALID_RECURRING_DETAIL_NAME = 70021
INVALID_PSP_ECHO_DATA = 70022
NO_PARAMS_SPECIFIED = 70023
INVALID_FIELD = 70024
NO_PROJECT_CODE_SPECIFIED = 70025
NO_PROJECT_FOUND = 70026
INVALID_AUTHORISATION_TYPE_SUPPLIED = 70027
INVALID_ACQUIRER_ACCOUNT = 70028
NO_METHOD_SPECIFIED = 70029
PROBLEM_PARSING_REQUEST = 70030
INVALID_MERCHANT_ACCOUNT = 70031
THREE_DS_AUTHENTICATION_FAILED = 70032
THREE_DS_AUTHENTICATION_REQUIRED = 70033

# reference 701**
MISSING_REFERENCE = 70101
ORIGINAL_PSPREFERENCE_REQUIRED = 70102
INVALID_ORIGINAL_PSP_REFERENCE = 70103

# company 702**
INVALID_COMPANY_REGISTRATION_NUMBER = 70201
INVALID_COMPANY_NAME = 70202
MISSING_COMPANY_DETAILS = 70203

# contract 703**
INVALID_RECURRING_CONTRACT = 70301
CONTRACT_NOT_FOUND = 70302
INVALID_CONTRACT = 70303

# additional_data 704**
NO_ADDITIONAL_DATA_SPECIFIED = 70401
INVALID_ADDITIONAL_DATA = 70402
MISSING_ADDITIONAL_DATA_FIELD = 70403
INVALID_ADDITIONAL_DATA_FIELD = 70404

# payment details 705**
PAYMENT_DETAILS_ARE_NOT_SUPPORTED = 70501
US_PAYMENT_DETAILS_ARE_NOT_SUPPORTED = 70502
TOO_MANY_PAYMENT_DETAILS_DEFINED = 70503
PAYMENT_DETAIL_NOT_FOUND = 70504

# billing address 71***
BILLING_ADDRESS_PROBLEM = 71001
BILLING_ADDRESS_PROBLEM_CITY = 71002
BILLING_ADDRESS_PROBLEM_STREET = 71003
BILLING_ADDRESS_PROBLEM_HOUSENUMBER_OR_NAME = 71004
BILLING_ADDRESS_PROBLEM_COUNTRY = 71005
BILLING_ADDRESS_PROBLEM_STATE_OR_PROVINCE = 71006
INVALID_BILLING_ADDRESS = 71007
BILLING_ADDRESS_MISSING = 71008

# variant 711**
UNABLE_TO_DETERMINE_VARIANT = 71101
INVALID_VARIANT = 71102

# card/cardholder 72***
INVALID_CCN = 72001
INVALID_CVC_LENGTH = 72002
MISSING_CARD_HOLDER = 72003
INVALID_EXPIRY_DATE = 72004
INVALID_EXPIRY_MONTH_OR_BEFORE_NOW = 72005
INVALID_EXPIRY_MONTH = 72006
INVALID_SELECTED_BRAND = 72007
INVALID_CVC = 72008
BIN_DETAILS_NOT_FOUND_FOR_THE_GIVEN_CARD_NUMBER = 72009
CCN_CANT_BE_SPECIFIED_FOR_INCONTROL_VIRTUAL_CARD_REQUESTS = 72010
RECURRING_NOT_ALLOWED_FOR_INCONTROL_VIRTUAL_CARD_REQUESTS = 72011

# amount 730**
NO_AMOUNT = 73001
INVALID_TOTAL_AMOUNT = 73002
INVALID_AMOUNT_SPECIFIED = 73003
UNSUPPORTED_CURRENCY_SPECIFIED = 73004
AMOUNT_NOT_ALLOWED_FOR_THIS_OPERATION = 73005

# invoice 731**
NO_INVOICE_LINES_PROVIDED = 73101
INCORRECT_INVOICE_LINE = 73102
FAILED_TO_RETRIEVE_OPENINVOICELINES = 73103
NO_INVOICEPROJECT_PROVIDED = 73104
NO_INVOICEBATCH_PROVIDED = 73105
UNABLE_TO_CREATE_INVOICEPROJECT = 73106
INVOICEBATCH_ALREADY_EXISTS = 73107
UNABLE_TO_CREATE_INVOICEBATCH = 73108
INVOICEBATCH_VALIDITY_PERIOD_EXCEEDED = 73109

# bank/account 74***
INVALID_BANK_ACC_NUMBER = 74001
MISSING_BANKDETAILS = 74002
INVALID_BANK_COUNTRY_CODE = 74003
BANK_COUNTRY_NOT_SUPPORTED = 74004
INVALID_OR_MISSING_BANK_ACC_OR_BANK_LOCATION_ID = 74005
BANK_NAME_OR_BANK_LOCATION_NOT_VALID_OR_MISSING = 74006
MISSING_ACCOUNT_HOLDER = 74007
INVALID_IBAN = 74008
INCONSISTENT_IBAN = 74009
INVALID_BIC = 74010
NO_CREDIT_OR_ACCOUNT_SPECIFIED = 74011
NO_CREDIT_OR_ACCOUNT_FOUND = 74012

# shopper 750**
INVALID_SHOPPER_NAME = 75001
MISSING_SHOPPER_EMAIL = 75002
MISSING_SHOPPER_REFERENCE = 75003
MISSING_PHONENUMBER = 75004
THE_PHONENUMBER_SHOULD_BE_MOBILE = 75005
INVALID_PHONENUMBER = 75006
RECURRING_REQUIRES_SHOPPERE_MAIL_AND_SHOPPE_RREFERENCE = 75007
INVALID_SHOPPER_REFERENCE = 75008
INVALID_SHOPPER_EMAIL = 75009
INVALID_SHOPPER_STATEMENT = 75010
INVALID_SHOPPER_IP = 75011
INVALID_DATE_OF_BIRTH = 75012
INVALID_SOCIAL_SECURITY_NUMBER = 75013

# delivery address 751**
INVALID_DELIVERY_ADDRESS = 75101
DELIVERY_ADDRESS_PROBLEM_CITY = 75102
DELIVERY_ADDRESS_PROBLEM_STREET = 75103
DELIVERY_ADDRESS_PROBLEM_HOUSE_NUMBER_OR_NAME = 75104
DELIVERY_ADDRESS_PROBLEM_COUNTRY = 75105
DELIVERY_ADDRESS_PROBLEM_STATE_OR_PROVINCE = 75106

###############################################################################
# account/configuration realated 8****
RECURRING_NOT_ENABLED = 80001
NO_DUNNING_CONFIGURATION_FOUND = 80002
INVALID_DUNNING_CONFIGURATION = 80003
CONFIGURATION_ERROR_ACQUIRERIDENTIFICATION = 80004
CONFIGURATION_ERROR_ACQUIRERPASSWORD = 80005
CONFIGURATION_ERROR_APIKEY = 80006
CONFIGURATION_ERROR_REDIRECTURL = 80007
CONFIGURATION_ERROR_ACQUIRERACCOUNTDATA = 80008
CONFIGURATION_ERROR_CURRENCYCODE = 80009
CONFIGURATION_ERROR_TERMINALID = 80010
CONFIGURATION_ERROR_SERIALNUMBER = 80011
CONFIGURATION_ERROR_PASSWORD = 80012
CONFIGURATION_ERROR_PROJECTID = 80013
CONFIGURATION_ERROR_MERCHANTCATEGORYCODE = 80014
CONFIGURATION_ERROR_MERCHANTNAME = 80015
CONFIGURATION_ERROR_PRIVATEKEYALIAS = 80016
CONFIGURATION_ERROR_PUBLICKEYALIAS = 80017
NO_INVOICE_CONFIGURATION_CONFIGURED_FOR_CREDIT_ACCOUNT = 80018
INVALID_INVOICE_CONFIGURATION_CONFIGURED_FOR_CREDIT_ACCOUNT = 80019
UNAUTHORIZED = 80020

###############################################################################
# server/connection related 90***
INTERNAL_ERROR = 90001
CONN_ERR = 90002
CONN_TIMEOUT = 90003
UNKNOWN_CONN_ERR = 90004

###############################################################################
# generic fails 999**
# cannot decode Adyen response
JSON_ERROR = 99_996

# failed, but it's (probably) totally normal
FAILED = 99_997

# unknown, but we know it's unknown because psp told so
UNKNOWN_ERROR = 99_998

# wtf just happend?
WTF_ERROR = 99_999

__FRAUD__ = [FRAUD, BLOCKED_CARD, RESTRICTED_CARD]

# without CONN_TIMEOUT, because it's safe to retry
__CONN_ERR__ = [CONN_ERR, UNKNOWN_CONN_ERR]

error_code_2_operation_result: dict[str, int] = {
    '000': UNKNOWN_ERROR,
    '010': NOT_ALLOWED,
    '100': NO_AMOUNT,
    '101': INVALID_CCN,
    '102': UNABLE_TO_DETERMINE_VARIANT,
    '103': INVALID_CVC_LENGTH,
    '104': BILLING_ADDRESS_PROBLEM,
    '105': INVALID_PARES,
    '106': DUPLICATED_SESSION,
    '107': RECURRING_NOT_ENABLED,
    '108': INVALID_BANK_ACC_NUMBER,
    '109': INVALID_VARIANT,
    '110': MISSING_BANKDETAILS,
    '111': INVALID_BANK_COUNTRY_CODE,
    '112': BANK_COUNTRY_NOT_SUPPORTED,
    '113': NO_INVOICE_LINES_PROVIDED,
    '114': INCORRECT_INVOICE_LINE,
    '115': INVALID_TOTAL_AMOUNT,
    '116': INVALID_DATE_OF_BIRTH,
    '117': INVALID_BILLING_ADDRESS,
    '118': INVALID_DELIVERY_ADDRESS,
    '119': INVALID_SHOPPER_NAME,
    '120': MISSING_SHOPPER_EMAIL,
    '121': MISSING_SHOPPER_REFERENCE,
    '122': MISSING_PHONENUMBER,
    '123': THE_PHONENUMBER_SHOULD_BE_MOBILE,
    '124': INVALID_PHONENUMBER,
    '125': INVALID_RECURRING_CONTRACT,
    '126': INVALID_OR_MISSING_BANK_ACC_OR_BANK_LOCATION_ID,
    '127': MISSING_ACCOUNT_HOLDER,
    '128': MISSING_CARD_HOLDER,
    '129': INVALID_EXPIRY_DATE,
    '130': MISSING_REFERENCE,
    '131': BILLING_ADDRESS_PROBLEM_CITY,
    '132': BILLING_ADDRESS_PROBLEM_STREET,
    '133': BILLING_ADDRESS_PROBLEM_HOUSENUMBER_OR_NAME,
    '134': BILLING_ADDRESS_PROBLEM_COUNTRY,
    '135': BILLING_ADDRESS_PROBLEM_STATE_OR_PROVINCE,
    '136': FAILED_TO_RETRIEVE_OPENINVOICELINES,
    '137': INVALID_AMOUNT_SPECIFIED,
    '138': UNSUPPORTED_CURRENCY_SPECIFIED,
    '139': RECURRING_REQUIRES_SHOPPERE_MAIL_AND_SHOPPE_RREFERENCE,
    '140': INVALID_EXPIRY_MONTH_OR_BEFORE_NOW,
    '141': INVALID_EXPIRY_MONTH,
    '142': BANK_NAME_OR_BANK_LOCATION_NOT_VALID_OR_MISSING,
    '143': MERCHANT_RETURN_URL_TOO_LONG,
    '144': INVALID_START_MONTH_OR_IN_THE_FUTURE,
    '145': INVALID_ISSUER_COUNTRYCODE,
    '146': INVALID_SOCIAL_SECURITY_NUMBER,
    '147': DELIVERY_ADDRESS_PROBLEM_CITY,
    '148': DELIVERY_ADDRESS_PROBLEM_STREET,
    '149': DELIVERY_ADDRESS_PROBLEM_HOUSE_NUMBER_OR_NAME,
    '150': DELIVERY_ADDRESS_PROBLEM_COUNTRY,
    '151': DELIVERY_ADDRESS_PROBLEM_STATE_OR_PROVINCE,
    '152': INVALID_NUMBER_OF_INSTALLMENTS,
    '153': INVALID_CVC,
    '154': NO_ADDITIONAL_DATA_SPECIFIED,
    '155': NO_ACQUIRER_SPECIFIED,
    '156': NO_AUTHORISATION_MID_SPECIFIED,
    '157': NO_FIELDS_SPECIFIED,
    '158': REQUIRED_FIELD_NOT_SPECIFIED,
    '159': INVALID_NUMBER_OF_REQUESTS,
    '160': NOT_ALLOWED_TO_STORE_PAYOUT_DETAILS,
    '161': INVALID_IBAN,
    '162': INCONSISTENT_IBAN,
    '163': INVALID_BIC,
    '164': AUTO_CAPTURE_DELAY_INVALID_OR_OUT_OF_RANGE,
    '165': MANDATE_ID_DOES_NOT_MATCH_PATTERN,
    '166': AMOUNT_NOT_ALLOWED_FOR_THIS_OPERATION,
    '167': ORIGINAL_PSPREFERENCE_REQUIRED,
    '168': AUTHORISATIONCODE_REQUIRED_FOR_THIS_OPERATION,
    '170': GENERATION_DATE_REQUIRED_BUT_MISSING,
    '171': UNABLE_TO_PARSE_GENERATION_DATE,
    '172': ENCRYPTED_DATA_USED_OUTSIDE_OF_VALID_TIME_PERIOD,
    '173': UNABLE_TO_LOAD_PRIVATE_KEY_FOR_DECRYPTION,
    '174': UNABLE_TO_DECRYPT_DATA,
    '175': UNABLE_TO_PARSE_JSON_DATA,
    '180': INVALID_SHOPPER_REFERENCE,
    '181': INVALID_SHOPPER_EMAIL,
    '182': INVALID_SELECTED_BRAND,
    '183': INVALID_RECURRING_CONTRACT,
    '184': INVALID_RECURRING_DETAIL_NAME,
    '185': INVALID_ADDITIONAL_DATA,
    '186': MISSING_ADDITIONAL_DATA_FIELD,
    '187': INVALID_ADDITIONAL_DATA_FIELD,
    '188': INVALID_PSP_ECHO_DATA,
    '189': INVALID_SHOPPER_STATEMENT,
    '190': INVALID_SHOPPER_IP,
    '191': NO_PARAMS_SPECIFIED,
    '192': INVALID_FIELD,
    '193': BIN_DETAILS_NOT_FOUND_FOR_THE_GIVEN_CARD_NUMBER,
    '194': BILLING_ADDRESS_MISSING,
    '600': NO_INVOICEPROJECT_PROVIDED,
    '601': NO_INVOICEBATCH_PROVIDED,
    '602': NO_CREDIT_OR_ACCOUNT_SPECIFIED,
    '603': NO_PROJECT_CODE_SPECIFIED,
    '604': NO_CREDIT_OR_ACCOUNT_FOUND,
    '605': NO_PROJECT_FOUND,
    '606': UNABLE_TO_CREATE_INVOICEPROJECT,
    '607': INVOICEBATCH_ALREADY_EXISTS,
    '608': UNABLE_TO_CREATE_INVOICEBATCH,
    '609': INVOICEBATCH_VALIDITY_PERIOD_EXCEEDED,
    '610': NO_DUNNING_CONFIGURATION_FOUND,
    '611': INVALID_DUNNING_CONFIGURATION,
    '690': ERROR_STORING_DEBTOR,
    '691': ERROR_STORING_INVOICE,
    '692': ERROR_CHECKING_IF_INVOICE_ALREADY_EXISTS_FOR_CREDIT_OR_ACCOUNT,
    '693': ERROR_SEARCHING_INVOICES,
    '694': NO_INVOICE_CONFIGURATION_CONFIGURED_FOR_CREDIT_ACCOUNT,
    '695': INVALID_INVOICE_CONFIGURATION_CONFIGURED_FOR_CREDIT_ACCOUNT,
    '700': NO_METHOD_SPECIFIED,
    '701': SERVER_COULD_NOT_PROCESS_REQUEST,
    '702': PROBLEM_PARSING_REQUEST,
    '800': CONTRACT_NOT_FOUND,
    '801': TOO_MANY_PAYMENT_DETAILS_DEFINED,
    '802': INVALID_CONTRACT,
    '803': PAYMENT_DETAIL_NOT_FOUND,
    '804': FAILED_TO_DISABLE,
    '805': RECURRING_DETAIL_REFERENCE_NOT_AVAILABLE_FOR_RECURRING,
    '806': NO_APPLICABLE_CONTRACTTYPES_LEFT_FOR_THIS_PAYMENT_METHOD,
    '901': INVALID_MERCHANT_ACCOUNT,
    '902': SHOULDNT_HAVE_GOTTEN_HERE_WITHOUT_A_REQUEST,
    '903': INTERNAL_ERROR,
    '904': UNABLE_TO_PROCESS,
    '905': PAYMENT_DETAILS_ARE_NOT_SUPPORTED,
    '906': INVALID_ORIGINAL_PSP_REFERENCE,
    '907': US_PAYMENT_DETAILS_ARE_NOT_SUPPORTED,
    '908': INVALID_REQUEST,
    '950': INVALID_ACQUIRER_ACCOUNT,
    '951': CONFIGURATION_ERROR_ACQUIRERIDENTIFICATION,
    '952': CONFIGURATION_ERROR_ACQUIRERPASSWORD,
    '953': CONFIGURATION_ERROR_APIKEY,
    '954': CONFIGURATION_ERROR_REDIRECTURL,
    '955': CONFIGURATION_ERROR_ACQUIRERACCOUNTDATA,
    '956': CONFIGURATION_ERROR_CURRENCYCODE,
    '957': CONFIGURATION_ERROR_TERMINALID,
    '958': CONFIGURATION_ERROR_SERIALNUMBER,
    '959': CONFIGURATION_ERROR_PASSWORD,
    '960': CONFIGURATION_ERROR_PROJECTID,
    '961': CONFIGURATION_ERROR_MERCHANTCATEGORYCODE,
    '962': CONFIGURATION_ERROR_MERCHANTNAME,
    '963': INVALID_COMPANY_REGISTRATION_NUMBER,
    '964': INVALID_COMPANY_NAME,
    '965': MISSING_COMPANY_DETAILS,
    '966': CONFIGURATION_ERROR_PRIVATEKEYALIAS,
    '967': CONFIGURATION_ERROR_PUBLICKEYALIAS,
    '1000': CCN_CANT_BE_SPECIFIED_FOR_INCONTROL_VIRTUAL_CARD_REQUESTS,
    '1001': RECURRING_NOT_ALLOWED_FOR_INCONTROL_VIRTUAL_CARD_REQUESTS,
    '1002': INVALID_AUTHORISATION_TYPE_SUPPLIED,
}

refusal_reason_2_operation_result: dict[str, int] = {
    RefusalReason.THREE_DS_AUTHENTICATION_FAILED: THREE_DS_AUTHENTICATION_FAILED,
    RefusalReason.ACQUIRER_FRAUD: FRAUD,
    RefusalReason.AUTHENTICATION_REQUIRED: THREE_DS_AUTHENTICATION_REQUIRED,
    RefusalReason.BLOCKED_CARD: BLOCKED_CARD,
    RefusalReason.CANCELLED: CANCELLED,
    RefusalReason.CVC_DECLINED: CVC_DECLINED,
    RefusalReason.REFUSED: REFUSED,
    RefusalReason.DECLINED_NON_GENERIC: REFUSED,
    RefusalReason.ACQUIRER_ERROR: ACQUIRER_ERROR,
    RefusalReason.EXPIRED_CARD: EXPIRED_CARD,
    RefusalReason.FRAUD: FRAUD,
    RefusalReason.FRAUD_CANCELLED: FRAUD,
    RefusalReason.INVALID_AMOUNT: INVALID_AMOUNT,
    RefusalReason.INVALID_CARD_NUMBER: INVALID_CARD_NUMBER,
    RefusalReason.INVALID_PIN: INVALID_PIN,
    RefusalReason.ISSUER_UNAVAILABLE: ISSUER_UNAVAILABLE,
    RefusalReason.NOT_ENOUGH_BALANCE: NOT_ENOUGH_BALANCE,
    RefusalReason.NOT_SUBMITTED: NOT_SUBMITTED,
    RefusalReason.NOT_SUPPORTED: NOT_SUPPORTED,
    RefusalReason.PENDING: PENDING,
    RefusalReason.PIN_TRIES_EXCEEDED: PIN_TRIES_EXCEEDED,
    RefusalReason.PIN_VALIDATION_NOT_POSSIBLE: PIN_VALIDATION_NOT_POSSIBLE,
    RefusalReason.REFERRAL: REFERRAL,
    RefusalReason.RESTRICTED_CARD: RESTRICTED_CARD,
    RefusalReason.REVOCATION_OF_AUTH: REVOCATION_OF_AUTH,
    RefusalReason.SHOPPER_CANCELLED: SHOPPER_CANCELLED,
    RefusalReason.WITHDRAWAL_COUNT_EXCEEDED: WITHDRAWAL_COUNT_EXCEEDED,
    RefusalReason.WITHDRAWAL_AMOUNT_EXCEEDED: WITHDRAWAL_AMOUNT_EXCEEDED,
    RefusalReason.TRANSACTION_NOT_PERMITTED: TRANSACTION_NOT_PERMITTED,
    RefusalReason.UNKNOWN: UNKNOWN_ERROR,
}
