# pylint: disable=cyclic-import
from typing import Optional

from django.conf import settings
from rest_framework import status

from lib.payment_providers.entities import (
    AdyenAuthAdditionalDataEntity,
)
from lib.payment_providers.enums import TokenizedPaymentMethodType
from lib.payments.enums import PaymentError
from webapps.payment_providers.consts.adyen import (
    AuthResultCode,
)
from webapps.payment_providers.models import (
    AdyenAuth,
    AdyenCapture,
    Payment,
)
from webapps.payment_providers.providers.adyen.utils import (
    AdyenReferenceUtils,
)
from webapps.payment_providers.providers.adyen.services import AdyenRequestService
from webapps.payment_providers.types.adyen import (
    AuthThreeDSecureData,
)

# pylint: disable=unused-argument


class AdyenAuthCardProvider:
    @classmethod
    def make_auth(
        cls,
        payment: Payment,
        additional_data: AdyenAuthAdditionalDataEntity,
        payment_token: str = None,
    ) -> (Optional[AuthThreeDSecureData], Optional[PaymentError]):
        external_id = AdyenReferenceUtils.generate_uniq_reference()
        split_external_id = AdyenReferenceUtils.generate_uniq_reference()
        auth = AdyenAuth.objects.create(
            amount=payment.amount,
            external_id=external_id,
            adyen_payment=payment.adyen_payment,
            additional_data={
                'split_external_id': split_external_id,
            },
        )

        action_required_details = None
        resp, exc = AdyenRequestService.process_request(
            {},
            settings.ADYEN_AUTH_URL,
        )
        if exc:
            return action_required_details, PaymentError.CONNECTION_ERROR

        resp_json = resp.json()
        auth.raw_error_code = resp_json.get('errorCode')
        auth.refusal_reason = resp_json.get('refusalReason')
        auth.psp_reference = resp_json.get('pspReference')
        auth.save(update_fields=['psp_reference', 'raw_error_code', 'refusal_reason'])

        if auth.refusal_reason or auth.raw_error_code:
            from webapps.payment_providers.services.adyen.adyen import AdyenPaymentServices

            return None, AdyenPaymentServices.map_adyen_errors_to_payment_error(
                adyen_refusal_reason=auth.refusal_reason,
                adyen_error_code=auth.raw_error_code,
            )

        if (
            resp.status_code == status.HTTP_200_OK
            and resp_json.get('resultCode') == AuthResultCode.REDIRECT_SHOPPER
        ):
            action_required_details = AuthThreeDSecureData(
                auth_psp_reference=auth.psp_reference,
                issuer_url=resp_json.get('issuerUrl'),
                md=resp_json.get('md'),
                pa_request=resp_json.get('paRequest'),
                additional_data=resp_json.get('additionalData'),
            ).to_dict()

        return action_required_details, None

    @staticmethod
    def get_payment_method_related_data(
        payment: Payment,
        additional_data: AdyenAuthAdditionalDataEntity,
        payment_token: Optional[str] = None,
    ):
        return {}

    @staticmethod
    def get_shopper_related_data(
        payment: Payment,
        additional_data: AdyenAuthAdditionalDataEntity,
    ) -> (str, str, Optional[str]):
        if payment.payment_method in TokenizedPaymentMethodType.values():
            adyen_tokenized_payment_method = (
                payment.tokenized_payment_method.adyen_tokenized_payment_method
            )
            shopper_ip = additional_data.device_data.ip or adyen_tokenized_payment_method.ip
            shopper_reference = adyen_tokenized_payment_method.shopper_reference
            customer_reference = (
                payment.tokenized_payment_method.adyen_tokenized_payment_method.cardholder_v1_id
            )
        else:
            shopper_ip = additional_data.device_data.ip
            shopper_reference = AdyenReferenceUtils.generate_shopper_reference(
                additional_data.user_v1_id
            )
            customer_reference = None

        return shopper_ip, shopper_reference, customer_reference


class AdyenCaptureCardProvider:
    @classmethod
    def make_capture(
        cls,
        payment: Payment,
    ) -> Optional[PaymentError]:
        auth = payment.adyen_payment.auth
        external_id = AdyenReferenceUtils.generate_uniq_reference()

        capture = AdyenCapture.objects.create(
            auth=auth,
            external_id=external_id,
        )
        resp, exc = AdyenRequestService.process_request(
            {},
            settings.ADYEN_CAPTURE_URL,
        )
        if exc:
            return PaymentError.CONNECTION_ERROR

        resp_json = resp.json()
        capture.psp_reference = resp_json.get('pspReference')
        capture.raw_error_code = resp_json.get('errorCode')
        capture.save(update_fields=['psp_reference', 'raw_error_code'])

        if capture.raw_error_code:
            from webapps.payment_providers.services.adyen.adyen import AdyenPaymentServices

            return AdyenPaymentServices.map_adyen_errors_to_payment_error(
                adyen_refusal_reason=auth.refusal_reason,
                adyen_error_code=capture.raw_error_code,
            )

        return None
