import typing as t
from typing import Optional

from lib.payment_providers.entities import (
    AdyenAuthAdditionalDataEntity,
)
from lib.payment_providers.enums import (
    PaymentMethodType,
    PaymentOperationStatus,
    PaymentOperationType,
    PaymentStatus,
)
from lib.payments.enums import PaymentError
from webapps.payment_providers.consts.adyen_errors_mapping import (
    ADYEN_REFUSAL_REASON_AND_ERROR_CODE_TO_COMMON_PAYMENT_ERROR_MAPPING,
    OPER_RESULT_TO_COMMON_PAYMENT_ERROR_MAPPING,
)
from webapps.payment_providers.models import (
    AdyenPayment,
    Payment,
    PaymentOperation,
    TokenizedPaymentMethod,
)
from webapps.payment_providers.providers.adyen.auth_capture import (
    AdyenAuthCardProvider,
    AdyenCaptureCardProvider,
)
from webapps.payment_providers.providers.adyen.refund_cancel import (
    AdyenRefundOrCancelProvider,
)
from webapps.payment_providers.services.base import (
    BasePaymentServices,
)


class AdyenPaymentServices(BasePaymentServices):
    @staticmethod
    def check_if_available() -> bool:
        """
        When the feature flag is activated, the AdyenProvider is disabled.
        :return: True if processing payment is allowed
        """
        return True

    @staticmethod
    def mark_payment_as_failed(
        payment: Payment,
        error_code: Optional[str] = None,
    ) -> None:
        raise NotImplementedError

    @staticmethod
    def modify_payment(
        payment: Payment,
        fee_amount: int,
    ) -> None:
        raise NotImplementedError

    @staticmethod
    def get_payment_client_token(payment: Payment) -> str:
        raise NotImplementedError

    @staticmethod
    def get_provider_payment_details(payment: Payment) -> str | None:
        return None

    @staticmethod
    def initialize_payment(payment: Payment, payment_token: str = None) -> AdyenPayment:
        """
        Create adyen payment based on common Payment
        """
        adyen_payment = AdyenPayment.objects.create(
            payment=payment,
        )

        return adyen_payment

    @staticmethod
    def authorize_payment(  # pylint: disable=signature-differs, too-many-arguments, too-many-positional-arguments
        payment: Payment,
        tokenized_pm: TokenizedPaymentMethod,
        additional_data: AdyenAuthAdditionalDataEntity = None,
        payment_token: str = None,
        gift_cards_ids: list[str] = None,
        off_session: bool | None = None,
    ) -> (Payment, PaymentError | None):
        """
        Tries to authorize Payment with given TokenizedPaymentMethod
        using adyen api
        :param payment
        :param tokenized_pm
        :param additional_data
        :param payment_token
        :param off_session
        """
        if tokenized_pm and tokenized_pm.method_type != PaymentMethodType.CARD:
            raise NotImplementedError

        action_required_details, payment_error = AdyenAuthCardProvider.make_auth(
            payment=payment,
            additional_data=additional_data,
            payment_token=payment_token,
        )

        if action_required_details:
            payment.action_required_details = action_required_details
            payment.save(
                update_fields=[
                    'action_required_details',
                ]
            )

        return payment, payment_error

    @staticmethod
    def capture_payment(
        payment: Payment,
    ) -> Payment:
        """
        Tries to capture Payment using adyen api
        :param payment: Payment.id
        """

        payment_error = AdyenCaptureCardProvider.make_capture(
            payment,
        )

        if payment_error:
            from webapps.payment_providers.services.common import (  # pylint: disable=cyclic-import
                CommonPaymentServices,
            )

            CommonPaymentServices.update_status(
                payment=payment,
                status=PaymentStatus.CAPTURE_FAILED,
                error_code=payment_error,
            )
        else:
            from webapps.payment_providers.services.common import (  # pylint: disable=cyclic-import
                CommonPaymentServices,
            )

            CommonPaymentServices.update_status(
                payment=payment,
                status=PaymentStatus.CAPTURED,
            )

        return payment

    @staticmethod
    def map_provider_error_to_common_payment_error(error: str) -> PaymentError:
        """
        Method have wrong name and is deprecated
        Map old adyen oper_result to payment_provider error
        :param error: oper_result
        """
        try:
            result_code = int(error)
        except ValueError:
            return PaymentError.GENERIC_ERROR

        return OPER_RESULT_TO_COMMON_PAYMENT_ERROR_MAPPING.get(
            result_code,
            PaymentError.GENERIC_ERROR,
        )

    @staticmethod
    def map_adyen_errors_to_payment_error(
        adyen_refusal_reason: t.Optional[str] = None,
        adyen_error_code: t.Optional[str] = None,
    ) -> PaymentError:
        if adyen_refusal_reason:
            return ADYEN_REFUSAL_REASON_AND_ERROR_CODE_TO_COMMON_PAYMENT_ERROR_MAPPING.get(
                adyen_refusal_reason,
                PaymentError.GENERIC_ERROR,
            )
        if adyen_error_code:
            return ADYEN_REFUSAL_REASON_AND_ERROR_CODE_TO_COMMON_PAYMENT_ERROR_MAPPING.get(
                adyen_error_code,
                PaymentError.GENERIC_ERROR,
            )

        return PaymentError.GENERIC_ERROR

    @staticmethod
    def cancel_payment(payment: Payment) -> bool:
        """
        Tries to cancel adyen_payment
        if there is no auth we always return true because
        cancel happened before we created payment on the psp side

        we can safely call multiple times because canceling payment is redundant
        :param payment: Payment object
        """
        if not payment.adyen_payment.auth:
            return True

        payment_operation, _ = PaymentOperation.objects.get_or_create(
            payment=payment,
            type=PaymentOperationType.CANCEL_OR_REFUND,
            amount=payment.amount,
        )
        if payment_operation.status == PaymentOperationStatus.SUCCESS:
            return True

        is_canceled = AdyenRefundOrCancelProvider.make_cancel_or_refund(
            auth=payment.adyen_payment.auth,
        )

        payment_operation.status = (
            PaymentOperationStatus.SUCCESS if is_canceled else PaymentOperationStatus.FAILED
        )
        payment_operation.save(update_fields=['status'])

        return is_canceled
