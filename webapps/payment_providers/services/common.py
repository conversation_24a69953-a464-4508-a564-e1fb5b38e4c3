import datetime
import json
import logging
import traceback
import uuid
from dataclasses import asdict
from typing import (
    Optional,
    Type,
    TypedDict,
    Union,
    Tuple,
)

import requests
from django.conf import settings
from django.db import transaction
from django.db.models import Q

from lib.db import PAYMENTS_DB
from lib.feature_flag.feature.payment import DisableNewAdyenProvider, NethoneAttemptReferenceSendingFlag
from lib.payment_providers.entities import (
    AdyenAuthAdditionalDataEntity,
    AuthorizePaymentMethodDataEntity,
    CardData,
    PaymentTokenExternalData,
    PayoutDetailsEntity,
    TokenizedPMExternalData,
)
from lib.payment_providers.enums import (
    PaymentMethodType,
    PaymentOperationStatus,
    PaymentOperationType,
    PaymentStatus,
    SetupIntentStatus,
    TokenizedPaymentMethodType,
    TransferFundStatus,
)
from lib.payment_providers.events import (
    payment_providers_payment_operation_created_event,
    payment_providers_payment_operation_updated_event,
    payment_providers_payment_updated_event,
    payment_providers_payout_created_event,
    payment_providers_payout_updated_event,
    payment_providers_transfer_fund_updated_event,
)
from lib.payments.enums import (
    PaymentError,
    PaymentProviderCode,
    PayoutError,
    PayoutStatus,
    PayoutType,
    ProductionPaymentProviderCode,
)
from lib.serializers import safe_get
from lib.tools import chunker
from webapps.payment_providers.exceptions import common as common_exceptions
from webapps.payment_providers.exceptions.common import (
    ProviderServiceClassNotImplementedException,
)
from webapps.payment_providers.models import (
    AccountHolder,
    Customer,
    Payment,
    PaymentHistory,
    PaymentOperation,
    PaymentOperationHistory,
    Payout,
    SetupIntent,
    TokenizedPaymentMethod,
    TransferFund,
)
from webapps.payment_providers.serializers.common import (
    PaymentHistorySerializer,
    PaymentOperationHistorySerializer,
)
from webapps.payment_providers.services.adyen.adyen import (
    AdyenPaymentServices,
)
from webapps.payment_providers.services.base import (
    BaseCustomerServices,
    BasePaymentOperationServices,
    BasePaymentServices,
    BasePayoutServices,
    BaseTransferFundServices,
)
from webapps.payment_providers.services.booksy_gift_cards.customer_services import (
    BooksyGiftCardsCustomerServices,
)
from webapps.payment_providers.services.booksy_gift_cards.payment_services import (
    BooksyGiftCardsPaymentServices,
)
from webapps.payment_providers.services.stripe.stripe import (
    StripeCustomerServices,
    StripePaymentMethodServices,
    StripePaymentOperationServices,
    StripePaymentServices,
    StripePayoutServices,
    StripeTerminalServices,
    StripeTransferFundServices,
)
from webapps.payment_providers.tasks import (  # pylint: disable=cyclic-import
    check_tokenized_payment_method_menu_warnings_for_customer,
)


nethone_logger = logging.getLogger('booksy.payment_providers.nethone')


class CommonPaymentServices:
    PROVIDERS = {
        PaymentProviderCode.ADYEN: AdyenPaymentServices,
        PaymentProviderCode.STRIPE: StripePaymentServices,
        PaymentProviderCode.BOOKSY_GIFT_CARDS: BooksyGiftCardsPaymentServices,
    }

    @staticmethod
    def _get_service_class(
        payment_provider_code: PaymentProviderCode,
    ) -> Type[BasePaymentServices]:
        provider = CommonPaymentServices.PROVIDERS.get(payment_provider_code)

        if provider is None or (
            DisableNewAdyenProvider() and provider == PaymentProviderCode.ADYEN
        ):
            raise ProviderServiceClassNotImplementedException

        return provider

    @staticmethod
    def initialize_payment(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        customer: Customer,
        account_holder: AccountHolder,
        payment_provider_code: PaymentProviderCode,
        payment_method_type: PaymentMethodType,
        amount: int,
        fee_amount: int,
        metadata: dict = None,
        additional_data: dict = None,
        auto_capture: bool = False,
        payment_token: str = None,
    ) -> Payment:
        """
        Creates Payment object (money transfer from Customer to AccountHolder)

        :param customer: Customer object
        :param account_holder: AccountHolder object
        :param payment_provider_code: payment provider implementation to be used
        :param payment_method_type: Payment method
        :param amount: Payment amount
        :param fee_amount: Payment fee amount
        :param metadata: any custom data to be stored in Payment object
        :param additional_data: additional data that have to be passed due to concrete psp
            implementation (i.e. psp radar data like customers IP address)
        :param auto_capture: determines if Payment should be automatically captured
            after receiving proper webhook notification
        :param payment_token: payment token to be used for payment
        :return: payment object
        """
        payment = Payment.objects.create(
            account_holder=account_holder,
            customer=customer,
            provider_code=payment_provider_code,
            payment_method=payment_method_type,
            amount=amount,
            fee_amount=fee_amount,
            metadata=metadata or {},
            additional_data=additional_data or {},
            auto_capture=auto_capture,
            currency=settings.CURRENCY_CODE,
        )
        payment_provider = CommonPaymentServices._get_service_class(payment_provider_code)
        payment_provider.initialize_payment(payment, payment_token)

        return payment

    @staticmethod
    def authorize_payment(
        payment: Payment,
        payment_method_data: AuthorizePaymentMethodDataEntity,
        additional_data: AdyenAuthAdditionalDataEntity | None = None,
        off_session: bool | None = None,
    ) -> Payment:
        """
        Tries to authorize Payment with given TokenizedPaymentMethod

        :param payment: Payment object
        :param payment_method_data: Data related to payment method
        :param additional_data: data need to proces auth specific for psp
        :param off_session: Set to true to indicate that the customer
            is not in checkout flow during this payment attempt
        """
        # While we are in monolith we can raise error, but when we migrate into microservices
        # we should handle errors in PortResponse.errors
        if payment.status == PaymentStatus.SENT_FOR_AUTHORIZATION:
            raise common_exceptions.AlreadySentForAuthorizationException()
        if payment.status == PaymentStatus.AUTHORIZED:
            raise common_exceptions.AlreadyAuthorizedException()
        if payment.status not in [
            PaymentStatus.NEW,
            PaymentStatus.AUTHORIZATION_FAILED,
            PaymentStatus.ACTION_REQUIRED,
        ]:
            raise common_exceptions.WrongPaymentActionException(
                'Payment in wrong status '
                '(can authorize only Payments in statuses: NEW, AUTHORIZATION_FAILED)',
            )
        payment_provider = CommonPaymentServices._get_service_class(payment.provider_code)
        if not payment_provider.check_if_available():
            CommonPaymentServices.update_status(
                payment=payment,
                status=PaymentStatus.AUTHORIZATION_FAILED,
                error_code=PaymentError.NOT_PERMITTED,
            )
            return payment

        tokenized_pm, payment_token = CommonPaymentMethodServices.get_payment_method(
            payment,
            payment_method_data,
        )
        if tokenized_pm:
            payment.tokenized_payment_method = tokenized_pm
            payment.save(update_fields=['tokenized_payment_method'])

        payment_error = CommonPaymentMethodServices.validate_payment_method(
            tokenized_pm=tokenized_pm,
            payment_token=payment_token,
            gift_cards_ids=payment_method_data.gift_cards_ids,
        )

        if payment_error:
            CommonPaymentServices.update_status(
                payment=payment,
                status=PaymentStatus.AUTHORIZATION_FAILED,
                error_code=payment_error,
            )
            return payment

        if NethoneAttemptReferenceSendingFlag():
            nethone_attempt_reference = payment.additional_data.get("fraud_prevention", {}).get("nethone_attempt_reference")
            if not nethone_attempt_reference or payment.payment_method != PaymentMethodType.CARD:
                pass  # in the POC stage it is not strictly enforced, and is only for PBA (cards)
            else:
                try:
                    url = "https://api.nethone.io/v1/inquiries"

                    payload = json.dumps({
                        "reference": payment.id,
                        "profiling_reference": nethone_attempt_reference,
                        "type": "transaction",
                        "origin": "payment",
                        "source": {},
                        "payment_methods": [
                            {
                                "method": "card",
                                "payment_gateway": payment.provider_code,
                                "card_token": payment_method_data.payment_token,
                            }
                        ],
                        "transaction_amount": payment.amount,
                        "transaction_currency": payment.currency,
                        "customer": {
                            "person_type": "natural",
                            "email": payment.customer.email,
                            "user_reference": payment.customer.metadata.get("user_id"),
                        },
                        "device": {
                            "ipaddr": additional_data.device_data.ip,
                        },
                        "merchant_country": settings.COUNTRY_CONFIG.country_code,
                    })
                    headers = {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': f'Basic {settings.NETHONE_BASIC_AUTH_KEY}'
                    }

                    response = requests.request("POST", url, headers=headers, data=payload)
                    nethone_logger.info(
                        "Nethone response code [%s] body: %s",
                        response.status_code,
                        response.text,
                    )
                except Exception as exc:
                    nethone_logger.exception(
                        "Unable to send Nethone attempt reference: %s",
                        exc,
                    )

        if payment.action_required_details:
            CommonPaymentServices.update_status(
                payment=payment,
                status=PaymentStatus.ACTION_REQUIRED,
            )
            return payment

        # all checks passed, we can authorize payment
        CommonPaymentServices.update_status(
            payment=payment,
            status=PaymentStatus.SENT_FOR_AUTHORIZATION,
        )
        payment, payment_error = payment_provider.authorize_payment(
            payment=payment,
            tokenized_pm=tokenized_pm,
            payment_token=payment_token,
            additional_data=getattr(
                additional_data,
                payment.provider_code,
                None,
            ),
            gift_cards_ids=payment_method_data.gift_cards_ids,
            off_session=off_session,
        )

        return payment

    @staticmethod
    def modify_payment(payment: Payment, fee_amount: int) -> Payment:
        payment_provider = CommonPaymentServices._get_service_class(payment.provider_code)

        payment_provider.modify_payment(
            payment=payment,
            fee_amount=fee_amount,
        )

        payment.fee_amount = fee_amount
        payment.save(update_fields=['fee_amount'])

        CommonPaymentServices.save_payment_to_history(payment)
        payment_providers_payment_updated_event.send(asdict(payment.entity))
        return payment

    @staticmethod
    def capture_payment(
        payment: Payment,
    ) -> Payment:
        """
        Captures Payment

        :param payment: Payment object
        """
        if payment.status == PaymentStatus.SENT_FOR_CAPTURE:
            raise common_exceptions.AlreadySentForCaptureException()
        if payment.status == PaymentStatus.CAPTURED:
            raise common_exceptions.AlreadyCapturedException()
        if payment.status != PaymentStatus.AUTHORIZED:
            raise common_exceptions.WrongPaymentActionException(
                'Payment in wrong status (can capture only Payments in status AUTHORIZED',
            )
        payment_provider = CommonPaymentServices._get_service_class(payment.provider_code)

        CommonPaymentServices.update_status(
            payment=payment,
            status=PaymentStatus.SENT_FOR_CAPTURE,
        )
        payment_provider.capture_payment(payment)

        return payment

    @staticmethod
    def cancel_payment(payment: Payment) -> Payment:
        """
        Tries to cancel Payment

        :param payment: Payment object
        """
        payment_provider = CommonPaymentServices._get_service_class(payment.provider_code)
        is_canceled = payment_provider.cancel_payment(payment)

        if is_canceled:
            CommonPaymentServices.update_status(
                payment=payment,
                status=PaymentStatus.CANCELED,
            )

        return payment

    @staticmethod
    def get_payment_client_token(payment: Payment) -> str:
        """
        Retrieves Payment.client_secret token used in BCR flow

        :param payment: Payment object
        """
        payment_provider = CommonPaymentServices._get_service_class(payment.provider_code)
        return payment_provider.get_payment_client_token(payment=payment)

    @staticmethod
    def get_provider_payment_details(payment: Payment) -> str:
        payment_provider = CommonPaymentServices._get_service_class(payment.provider_code)
        return payment_provider.get_provider_payment_details(
            payment=payment,
        )

    @staticmethod
    def update_status(
        payment: Payment,
        status: PaymentStatus,
        error_code: Optional[PaymentError] = None,
    ) -> None:
        """
        Update Payment.status and Payment.error_code

        :param payment: Payment object
        :param status: new PaymentStatus
        :param error_code: error_code
        """
        if payment.status == status and payment.error_code == error_code:
            return

        payment.status = status
        payment.error_code = error_code
        payment.save(update_fields=['status', 'error_code'])

        CommonPaymentServices.save_payment_to_history(payment)
        payment_providers_payment_updated_event.send(asdict(payment.entity))

    @staticmethod
    def update_amount(
        payment: Payment,
        new_amount: int,
        tip_amount: int,
    ) -> None:
        """
        Update Payment.amount and save tip_amount in metadata

        :param payment: Payment object
        :param new_amount: new amount
        :param tip_amount: new tip_amount
        """
        if payment.amount == new_amount and safe_get(payment.metadata, ['tip']) == tip_amount:
            return

        payment.amount = new_amount
        payment.metadata['tip_amount'] = tip_amount
        # deactivate auto-capture because amount has changed (allow gateway to act accordingly)
        payment.auto_capture = False
        payment.save(update_fields=['amount', 'metadata', 'auto_capture'])

        CommonPaymentServices.save_payment_to_history(payment)
        payment_providers_payment_updated_event.send(asdict(payment.entity))

    @staticmethod
    def save_payment_to_history(payment: Payment) -> None:
        serializer = PaymentHistorySerializer(payment)
        history = PaymentHistory(
            data=serializer.data,
            payment=payment,
            traceback=traceback.format_stack(),
        )
        history.save()

    @staticmethod
    def mark_payment_as_failed(
        payment: Payment,
        error_code: Optional[str] = None,
    ) -> None:
        if payment.payment_method not in [
            PaymentMethodType.TERMINAL,
            PaymentMethodType.TAP_TO_PAY,
            PaymentMethodType.KEYED_IN_PAYMENT,
        ]:
            raise common_exceptions.WrongPaymentActionException

        if payment.status not in [
            PaymentStatus.NEW,
        ]:
            raise common_exceptions.WrongPaymentActionException

        payment_provider = CommonPaymentServices._get_service_class(
            payment_provider_code=PaymentProviderCode(payment.provider_code),
        )
        return payment_provider.mark_payment_as_failed(
            payment=payment,
            error_code=error_code,
        )


class CommonPaymentOperationServices:
    PROVIDERS = {
        PaymentProviderCode.STRIPE: StripePaymentOperationServices,
        PaymentProviderCode.BOOKSY_GIFT_CARDS: StripePaymentOperationServices,
    }

    @staticmethod
    def _get_service_class(
        payment_provider_code: PaymentProviderCode,
    ) -> Type[BasePaymentOperationServices]:
        provider = CommonPaymentOperationServices.PROVIDERS.get(payment_provider_code)

        if provider is None:
            raise ProviderServiceClassNotImplementedException

        return provider

    @staticmethod
    def send_for_refund(
        payment: Payment,
        amount: int,
    ) -> PaymentOperation:
        """
        Sends given amount for refund and creates PaymentOperation object

        :param payment: Payment object
        :param amount: amount to be refunded (could not be greater than Payment.amount)
        """
        payment_operation = PaymentOperation.objects.create(
            payment=payment,
            amount=amount,
            type=PaymentOperationType.REFUND,
            status=PaymentOperationStatus.PROCESSING,
        )
        payment_provider = CommonPaymentOperationServices._get_service_class(payment.provider_code)
        payment_provider.send_for_refund(
            payment=payment,
            payment_operation=payment_operation,
        )
        return payment_operation

    @staticmethod
    def process_dispute(
        original_payment: Payment,
        amount: int,
        dispute_type: Union[
            PaymentOperationType.CHARGEBACK,
            PaymentOperationType.REVERSED_CHARGEBACK,
            PaymentOperationType.SECOND_CHARGEBACK,
        ],
        external_id: str = None,
    ) -> PaymentOperation:
        dispute_operation = PaymentOperation(
            payment=original_payment,
            type=dispute_type,
            amount=amount,
            status=PaymentOperationStatus.SUCCESS,
        )
        dispute_operation.save()
        payment_provider = CommonPaymentOperationServices._get_service_class(
            original_payment.provider_code
        )
        payment_provider.process_dispute(
            payment_operation=dispute_operation,
            external_id=external_id,
        )
        payment_providers_payment_operation_created_event.send(asdict(dispute_operation.entity))
        return dispute_operation

    @staticmethod
    def update_status(
        payment_operation: PaymentOperation,
        status: PaymentOperationStatus,
    ) -> None:
        if payment_operation.status == status:
            return

        payment_operation.status = status
        payment_operation.save(update_fields=['status'])

        CommonPaymentOperationServices.save_payment_operation_to_history(payment_operation)
        payment_providers_payment_operation_updated_event.send(asdict(payment_operation.entity))

    @staticmethod
    def save_payment_operation_to_history(payment_operation: PaymentOperation):
        serializer = PaymentOperationHistorySerializer(payment_operation)
        history = PaymentOperationHistory(
            data=serializer.data,
            payment_operation=payment_operation,
            traceback=traceback.format_stack(),
        )
        history.save()


class CommonCustomerServices:
    PROVIDERS = {
        PaymentProviderCode.STRIPE: StripeCustomerServices,
        PaymentProviderCode.BOOKSY_GIFT_CARDS: BooksyGiftCardsCustomerServices,
    }

    @staticmethod
    def _get_service_class(
        payment_provider_code: PaymentProviderCode,
    ) -> Type[BaseCustomerServices]:
        provider = CommonCustomerServices.PROVIDERS.get(payment_provider_code)

        if provider is None:
            raise ProviderServiceClassNotImplementedException

        return provider

    @staticmethod
    def create_common_customer(
        email: str,
        name: str,
        phone: str,
        metadata: dict = None,
    ) -> Customer:
        """
        Creates instance of Customer object

        :param email: email
        :param name: name
        :param phone: phone
        :param metadata: custom data to be stored in Customer object
        """
        customer = Customer(
            email=email,
            name=name,
            phone=phone,
            metadata=metadata,
        )
        customer.save()
        return customer

    # TODO for init script purpose - START, after phase2
    class CustomerData(TypedDict):
        email: str
        name: str
        phone: str
        metadata: dict = None

    @staticmethod
    def bulk_create_common_customer(
        data: list[CustomerData],
        chunk_size: int = 1000,
    ) -> list:
        resp_ids = []
        for customers in chunker(data, chunk_size):
            new_customers = []
            for customer in customers:
                new_customers.append(
                    Customer(
                        email=customer['email'],
                        name=customer['name'],
                        phone=customer['phone'],
                        metadata=customer.get('metadata', {}),
                    )
                )
            resp_ids.extend(
                list(Customer.objects.bulk_create(new_customers).values_list('id', flat=True))
            )
        return resp_ids

    # TODO for init script purpose - END

    @staticmethod
    def update_common_customer(
        customer: Customer,
        email: str = None,
        name: str = None,
        phone: str = None,
        metadata: dict = None,
    ) -> Customer:
        """
        Modifies existing Customer object
        This is partial update

        :param customer: Customer object
        :param email: email
        :param name: name
        :param phone: phone
        :param metadata: custom data to be stored in Customer object
        """
        fields = {
            'email': email,
            'name': name,
            'phone': phone,
            'metadata': metadata,
        }
        fields = {k: v for k, v in fields.items() if v is not None}
        for key, value in fields.items():
            setattr(customer, key, value)
        customer.save(update_fields=list(fields.keys()))

        # TODO move to celery task after phase3
        for provider_code in ProductionPaymentProviderCode:
            payment_provider = CommonCustomerServices._get_service_class(provider_code)
            payment_provider.update_customer(
                customer=customer,
            )

        return customer

    @staticmethod
    def create_provider_customer(
        customer: Customer,
        payment_provider_code: PaymentProviderCode,
    ) -> Customer:
        """
        Creates instance of concrete ProviderCustomer-implementation object for existing
        Customer i.e. StripeCustomer
        It can result with external API call

        :param customer: Customer object
        :param payment_provider_code: payment provider implementation to be used
        """
        payment_provider = CommonCustomerServices._get_service_class(payment_provider_code)
        payment_provider.get_or_create_customer(customer=customer)
        return customer


class CommonTransferFundServices:
    PROVIDERS = {
        PaymentProviderCode.STRIPE: StripeTransferFundServices,
    }

    @staticmethod
    def _get_service_class(
        payment_provider_code: PaymentProviderCode,
    ) -> Type[BaseTransferFundServices]:
        provider = CommonTransferFundServices.PROVIDERS.get(payment_provider_code)

        if provider is None:
            raise ProviderServiceClassNotImplementedException

        return provider

    @staticmethod
    def initialize_transfer_fund(  # pylint: disable=too-many-arguments
        sender_account_holder: AccountHolder,
        receiver_account_holder: AccountHolder,
        amount: int,
        payment_provider_code: PaymentProviderCode,
    ) -> TransferFund:
        """
        Creates TransferFund object (money transfer from AccountHolder to AccountHolder)

        :param sender_account_holder: sender AccountHolder object
        :param receiver_account_holder: receiver AccountHolder object
        :param amount: TransferFund amount
        :param payment_provider_code: payment provider implementation to be used
        """
        transfer_fund = TransferFund.objects.create(
            sender=sender_account_holder,
            receiver=receiver_account_holder,
            amount=amount,
            provider_code=payment_provider_code,
            currency=settings.CURRENCY_CODE,
            status=TransferFundStatus.NEW,
        )
        payment_provider = CommonTransferFundServices._get_service_class(payment_provider_code)
        payment_provider.initialize_transfer_fund(
            transfer_fund=transfer_fund,
        )

        return transfer_fund

    @staticmethod
    def process_transfer_fund(
        transfer_fund: TransferFund,
        additional_data=None,
        refund_fee_metadata: dict[str, str] = None,
        metadata: dict[str, str] | None = None,
    ) -> TransferFund:
        """
        Process TransferFund object

        :param transfer_fund: TransferFund object
        :param additional_data: additional data that have to be passed due to concrete psp
            implementation (i.e. psp radar data like customers IP address)
        :param metadata: values expected to be in psp Transfer metadata
        """
        CommonTransferFundServices.update_status(
            transfer_fund,
            status=TransferFundStatus.PROCESSING,
        )
        payment_provider = CommonTransferFundServices._get_service_class(
            transfer_fund.provider_code,
        )
        try:
            payment_provider.process_transfer_fund(
                transfer_fund=transfer_fund,
                additional_data=additional_data,
                refund_fee_metadata=refund_fee_metadata,
                metadata=metadata,
            )
        except Exception:  # pylint: disable=broad-except
            CommonTransferFundServices.update_status(
                transfer_fund,
                status=TransferFundStatus.FAILED,
            )
        else:
            CommonTransferFundServices.update_status(
                transfer_fund,
                status=TransferFundStatus.SUCCESS,
            )
        return transfer_fund

    @staticmethod
    def update_status(
        transfer_fund: TransferFund,
        status: TransferFundStatus,
    ) -> None:
        """
        Updates TransferFund.status

        :param transfer_fund: TransferFund object
        :param status: new TransferFundStatus
        """
        if transfer_fund.status == status:
            return

        transfer_fund.status = status
        transfer_fund.save(update_fields=['status'])

        payment_providers_transfer_fund_updated_event.send(asdict(transfer_fund.entity))

    @staticmethod
    def get_provider_transfer_fund_details(
        transfer_fund: TransferFund,
    ) -> Union[Tuple[str, datetime], Tuple[None, None]]:

        payment_provider = CommonTransferFundServices._get_service_class(
            transfer_fund.provider_code,
        )
        return payment_provider.get_provider_transfer_fund_details(
            transfer_fund=transfer_fund,
        )

    @staticmethod
    def get_transfer_fund(
        transfer_fund_id: uuid.UUID,
    ):
        return TransferFund.objects.filter(id=transfer_fund_id).first()


class CommonPayoutServices:
    PROVIDERS = {
        PaymentProviderCode.STRIPE: StripePayoutServices,
    }

    @staticmethod
    def _get_service_class(
        payment_provider_code: PaymentProviderCode,
    ) -> Type[BasePayoutServices]:
        provider = CommonPayoutServices.PROVIDERS.get(payment_provider_code)

        if provider is None:
            raise ProviderServiceClassNotImplementedException

        return provider

    @staticmethod
    def add_payout(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        external_id: str,
        account_holder: AccountHolder,
        payment_provider_code: PaymentProviderCode,
        status: PayoutStatus,
        payout_type: PayoutType,
        amount: int,
        error_code: PayoutError = None,
        expected_arrival_date: datetime.datetime = None,
        metadata: Optional[dict] = None,
    ) -> Payout:
        payout = Payout(
            account_holder=account_holder,
            payment_provider_code=payment_provider_code,
            status=status,
            payout_type=payout_type,
            error_code=error_code,
            amount=amount,
            expected_arrival_date=expected_arrival_date,
        )
        payout.save()

        payment_provider = CommonPayoutServices._get_service_class(
            payment_provider_code,
        )
        payment_provider.add_payout(
            payout,
            external_id,
            metadata or {},
        )

        payment_providers_payout_created_event.send(asdict(payout.entity))
        return payout

    @staticmethod
    def update_payout(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        payout: Payout,
        status: PayoutStatus,
        amount: int,
        payout_type: PayoutType,
        error_code: PayoutError = None,
        expected_arrival_date: datetime.datetime = None,
    ):
        payout.status = status
        payout.amount = amount
        payout.payout_type = payout_type
        payout.error_code = error_code
        payout.expected_arrival_date = expected_arrival_date
        payout.save(
            update_fields=[
                'status',
                'amount',
                'payout_type',
                'error_code',
                'expected_arrival_date',
            ]
        )
        payment_providers_payout_updated_event.send(asdict(payout.entity))

    @staticmethod
    def initialize_payout(
        account_holder: AccountHolder,
        payment_provider_code: PaymentProviderCode,
        amount: int,
        payout_type: PayoutType,
    ) -> Payout:
        """
        Initializes Payout

        :param account_holder: AccountHolder object
        :param payment_provider_code: payment provider implementation to be used
        :param amount: amount to be paid out
        :param payout_type: how the payout should be processed
        """
        payment_provider = CommonPayoutServices._get_service_class(payment_provider_code)

        external_id, status, arrival_date = payment_provider.initialize_payout(
            account_holder=account_holder,
            amount=amount,
            payout_type=payout_type,
        )

        payout = CommonPayoutServices.add_payout(
            external_id=external_id,
            account_holder=account_holder,
            payment_provider_code=PaymentProviderCode.STRIPE,
            amount=amount,
            status=status,
            expected_arrival_date=arrival_date,
            payout_type=payout_type,
        )
        return payout

    @staticmethod
    def get_available_payout_amount(
        account_holder: AccountHolder,
        payment_provider_code: PaymentProviderCode,
    ) -> dict:
        """
        Retrieves AccountHolder available balances

        :param account_holder: AccountHolder object
        :param payment_provider_code: payment provider implementation to be used
        """
        payment_provider = CommonPayoutServices._get_service_class(payment_provider_code)
        return payment_provider.get_available_payout_amount(account_holder=account_holder)

    @staticmethod
    def get_payout_details(payout: Payout) -> PayoutDetailsEntity:
        payment_provider = CommonPayoutServices._get_service_class(
            payment_provider_code=PaymentProviderCode(payout.payment_provider_code),
        )
        return payment_provider.get_payout_details(payout=payout)


class CommonTerminalServices:
    PROVIDERS = {
        PaymentProviderCode.STRIPE: StripeTerminalServices,
    }

    @staticmethod
    def _get_service_class(
        payment_provider_code: PaymentProviderCode,
    ) -> Type['BaseTerminalServices']:
        provider = CommonTerminalServices.PROVIDERS.get(payment_provider_code)

        if provider is None or (
            DisableNewAdyenProvider() and provider == PaymentProviderCode.ADYEN
        ):
            raise ProviderServiceClassNotImplementedException

        return provider

    @staticmethod
    def get_terminal_connection_token(
        payment_provider_code: PaymentProviderCode,
    ) -> str:
        """
        Return connection token for initialization terminal connection (BCR)

        :param payment_provider_code: payment provider implementation to be used
        """
        payment_provider = CommonTerminalServices._get_service_class(payment_provider_code)
        return payment_provider.get_terminal_connection_token()


class CommonPaymentMethodServices:
    PROVIDERS = {
        PaymentProviderCode.STRIPE: StripePaymentMethodServices,
    }

    @staticmethod
    def _get_service_class(
        payment_provider_code: PaymentProviderCode,
    ) -> Type[BasePayoutServices]:
        provider = CommonPaymentMethodServices.PROVIDERS.get(payment_provider_code)

        if provider is None:
            raise ProviderServiceClassNotImplementedException

        return provider

    @staticmethod
    def list_tokenized_pms(
        customer: Customer,
        payment_provider_code: Optional[PaymentProviderCode] = None,
        method_type: Optional[PaymentMethodType] = None,
        default: Optional[bool] = None,
    ):
        filters = [Q(customer=customer)]
        if method_type:
            filters.append(Q(method_type=method_type))
        if default:
            filters.append(Q(default=default))
        if payment_provider_code:
            filters.append(Q(provider_code=payment_provider_code))

        return TokenizedPaymentMethod.objects.filter(*filters).order_by('-default', '-created')

    @staticmethod
    def create_payment_token(
        payment_provider_code: PaymentProviderCode,
        external_data: PaymentTokenExternalData,
    ):
        payment_provider = CommonPaymentMethodServices._get_service_class(payment_provider_code)
        return payment_provider.create_payment_token(
            external_data=getattr(external_data, payment_provider_code),
        )

    @staticmethod
    def add_tokenized_pm(
        customer: Customer,
        external_data: TokenizedPMExternalData,
        payment_provider_code: PaymentProviderCode,
        method_type: PaymentMethodType,
    ) -> TokenizedPaymentMethod:
        with transaction.atomic(using=PAYMENTS_DB):
            TokenizedPaymentMethod.objects.filter(
                customer=customer,
                provider_code=payment_provider_code,
                method_type=method_type,
                default=True,
            ).update(default=False)
            tokenized_pm = TokenizedPaymentMethod.objects.create(
                customer=customer,
                provider_code=payment_provider_code,
                method_type=method_type,
                default=True,
                details=asdict(external_data.card_data) if external_data.card_data else {},
            )
            payment_provider = CommonPaymentMethodServices._get_service_class(payment_provider_code)
            payment_provider.add_tokenized_pm(
                tokenized_pm=tokenized_pm,
                external_data=getattr(external_data, payment_provider_code),
            )

        return tokenized_pm

    @staticmethod
    def update_tokenized_pm(
        tokenized_payment_method: TokenizedPaymentMethod,
        card_data: CardData,
    ):
        tokenized_payment_method.details = asdict(card_data)
        tokenized_payment_method.save(update_fields=['details'])

    @staticmethod
    def remove_tokenized_pm(
        tokenized_payment_method: TokenizedPaymentMethod,
        make_psp_call: bool = True,
    ):
        tokenized_payment_method.soft_delete()
        if tokenized_payment_method.default:
            customer = tokenized_payment_method.customer
            last_tokenized_pm = CommonPaymentMethodServices.list_tokenized_pms(
                customer=customer,
                payment_provider_code=tokenized_payment_method.provider_code,
                method_type=tokenized_payment_method.method_type,
            ).first()
            if last_tokenized_pm:
                CommonPaymentMethodServices.set_default_tokenized_pm(
                    customer=customer,
                    tokenized_payment_method=last_tokenized_pm,
                )
        payment_provider = CommonPaymentMethodServices._get_service_class(
            tokenized_payment_method.provider_code,
        )
        payment_provider.remove_tokenized_pm(
            tokenized_pm=tokenized_payment_method,
            make_psp_call=make_psp_call,
        )

    @staticmethod
    def set_default_tokenized_pm(
        customer: Customer,
        tokenized_payment_method: TokenizedPaymentMethod,
    ) -> TokenizedPaymentMethod:
        TokenizedPaymentMethod.objects.filter(
            customer=customer,
            method_type=tokenized_payment_method.method_type,
            provider_code=tokenized_payment_method.provider_code,
        ).update(default=False)
        tokenized_payment_method.default = True
        tokenized_payment_method.save(update_fields=['default'])
        return tokenized_payment_method

    @staticmethod
    def initialize_setup_intent(
        customer: Customer,
        payment_provider_code: PaymentProviderCode,
        method_type: PaymentMethodType = PaymentMethodType.CARD,
    ) -> SetupIntent:
        setup_intent = SetupIntent.objects.create(
            customer=customer,
            provider_code=payment_provider_code,
            method_type=method_type,
        )
        payment_provider = CommonPaymentMethodServices._get_service_class(payment_provider_code)
        client_secret = payment_provider.initialize_setup_intent(
            setup_intent=setup_intent,
            customer=customer,
            method_type=method_type,
        )
        setup_intent.client_secret = client_secret
        setup_intent.save(update_fields=['client_secret'])
        return setup_intent

    @staticmethod
    def update_setup_intent_status(
        setup_intent: SetupIntent,
        status: SetupIntentStatus,
    ):
        setup_intent.status = status
        setup_intent.save(update_fields=['status'])

    @staticmethod
    def get_payment_method(
        payment: Payment,
        payment_method_data: AuthorizePaymentMethodDataEntity,
    ) -> (TokenizedPaymentMethod | None, str | None):
        tokenized_pm = None
        if payment.payment_method in TokenizedPaymentMethodType.values():
            if payment_method_data.tokenized_pm_id:
                tokenized_pm = TokenizedPaymentMethod.objects.get(
                    id=payment_method_data.tokenized_pm_id,
                )
            else:
                tokenized_pm = CommonPaymentMethodServices.list_tokenized_pms(
                    customer=payment.customer,
                    payment_provider_code=payment.provider_code,
                    method_type=payment.payment_method,
                    default=True,
                ).first()

        return tokenized_pm, payment_method_data.payment_token

    @classmethod
    def validate_payment_method(
        cls,
        tokenized_pm: TokenizedPaymentMethod | None,
        payment_token: str | None,
        gift_cards_ids: list[str] | None,
    ) -> PaymentError | None:
        if not tokenized_pm and not payment_token and not gift_cards_ids:
            return PaymentError.GENERIC_ERROR

        if tokenized_pm:
            return cls.validate_tokenized_pm(tokenized_pm=tokenized_pm)

        return None

    @staticmethod
    def validate_tokenized_pm(
        tokenized_pm: TokenizedPaymentMethod | None,
        expiration_date_validation: datetime.date = None,
    ) -> PaymentError | None:
        if not tokenized_pm:
            return None
        if (
            tokenized_pm.method_type == TokenizedPaymentMethod.METHOD_TYPE.CARD
            and tokenized_pm.is_expired(expiration_date_validation)
        ):
            check_tokenized_payment_method_menu_warnings_for_customer.delay(
                customer_id=tokenized_pm.customer.id,
            )
            return (
                PaymentError.EXPIRED_CARD_AT_APPOINTMENT
                if expiration_date_validation
                else PaymentError.EXPIRED_CARD
            )
        if tokenized_pm.internal_status == TokenizedPaymentMethod.INTERNAL_STATUS.INVALID:
            check_tokenized_payment_method_menu_warnings_for_customer.delay(
                customer_id=tokenized_pm.customer.id,
            )
            return PaymentError.GENERIC_ERROR  # intentionally flat logic
        return None
