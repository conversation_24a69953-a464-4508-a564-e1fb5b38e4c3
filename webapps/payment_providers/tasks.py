import logging

from django.db.models import Q
from tqdm import tqdm

from lib.celery_tools import celery_task
from lib.feature_flag.feature import WarningCirclesFlag
from lib.inapp_notifications.ports import InnAppNotificationsPort
from lib.payments.enums import PaymentProviderCode
from lib.payments.events import (
    payment_method_invalid_or_expired_event,
    payment_method_expires_soon_event,
)
from lib.tools import tznow
from webapps.payment_providers.consts.common import NotificationStatus
from webapps.payment_providers.consts.stripe import StripeEventCode
from webapps.payment_providers.models import (
    Notification,
    TokenizedPaymentMethod,
)
from webapps.payments.enums import ChangeStatusEventSender


logger = logging.getLogger('booksy.payment_providers.tasks')


@celery_task
def capture_payment_task(payment_id: str):
    from webapps.payment_providers.models import Payment
    from webapps.payment_providers.services.common import CommonPaymentServices

    CommonPaymentServices.capture_payment(
        payment=Payment.objects.get(id=payment_id),
    )


@celery_task
def check_tokenized_payment_method_menu_warnings_for_customer(customer_id):
    from lib.inapp_notifications.ports import WarningCircle
    from webapps.notification.enums import RecipientType
    from webapps.payment_gateway.models import Wallet

    if customer_id is None or not WarningCirclesFlag():
        return
    wallet = Wallet.objects.filter(
        customer_id=customer_id,
    ).first()
    if not wallet:
        return
    app_type = RecipientType.CUSTOMER
    country_user_id = wallet.user_id
    warning_circle_name = WarningCircle.WarningCirclesNames.PROFILE_PAYMENTS
    if not TokenizedPaymentMethod.objects.filter(
        customer_id=wallet.customer_id,
    ).exists():
        InnAppNotificationsPort.remove(
            app_type=app_type,
            country_user_id=country_user_id,
            warning_circle_name=warning_circle_name,
        )
        return
    default_tpm = TokenizedPaymentMethod.objects.filter(
        customer_id=wallet.customer_id,
        default=True,
    ).first()
    if default_tpm and default_tpm.is_valid:
        InnAppNotificationsPort.remove(
            app_type=app_type,
            country_user_id=country_user_id,
            warning_circle_name=warning_circle_name,
        )
        return
    InnAppNotificationsPort.create(
        app_type=app_type,
        country_user_id=country_user_id,
        warning_circle_name=warning_circle_name,
        warning_circle_type=WarningCircle.WarningCirclesTypes.WARNING,
    )


@celery_task(time_limit=16 * 60, soft_time_limit=15 * 60)
def mark_cards_invalid_because_of_payment_history(start_date, end_date):
    from webapps.payment_providers.card_validation import validate_card

    notifications = Notification.objects.filter(
        created__gte=start_date,
        created__lte=end_date,
        event_code=StripeEventCode.PAYMENT_INTENT_PAYMENT_FAILED,
        provider_code=PaymentProviderCode.STRIPE,
        status=NotificationStatus.SUCCESS,
    )
    invalid_cards_detected = 0
    for notification in tqdm(notifications):
        result = validate_card(
            notification=notification,
            payment_intent=None,
            event_sender=ChangeStatusEventSender.CELERY_TASK,
        )
        if result is False:
            invalid_cards_detected += 1
    # pylint: disable=consider-using-f-string
    logger.info(
        "mark_cards_invalid_because_of_payment_history | "
        "Number of 'new' invalid cards detected between {start_date} and {end_date}"
        " is {invalid_cards_detected}".format(
            start_date=start_date.date(),
            end_date=end_date.date(),
            invalid_cards_detected=invalid_cards_detected,
        )
    )


@celery_task(time_limit=16 * 60, soft_time_limit=15 * 60)
def check_and_update_card_expiry_status_task():
    tokenized_pms = (
        TokenizedPaymentMethod.objects.filter(
            internal_status__in=[
                TokenizedPaymentMethod.INTERNAL_STATUS.VALID,
                TokenizedPaymentMethod.INTERNAL_STATUS.EXPIRES_SOON,
            ],
            method_type=TokenizedPaymentMethod.METHOD_TYPE.CARD,
            deleted__isnull=True,
        )
        .filter(Q(details__has_key='expiry_year') & Q(details__has_key='expiry_month'))
        .filter(
            Q(
                details__expiry_month__lte=tznow().month,
                details__expiry_year=tznow().year,
            )
            | Q(
                details__expiry_year__lt=tznow().year,
            )
        )
    )

    for tokenized_pm in tokenized_pms.iterator():
        try:
            check_if_update_card_status_and_send_notification(tokenized_pm)
        except Exception:  # pylint: disable=broad-except
            logger.exception(  # nosemgrep: python-logger-credential-disclosure
                'Update TokenizedPaymentMethod %s internal_status failed', tokenized_pm.id
            )
            continue


def check_if_update_card_status_and_send_notification(tokenized_pm: TokenizedPaymentMethod):
    new_status = None
    if tokenized_pm.is_expired():
        new_status = TokenizedPaymentMethod.INTERNAL_STATUS.EXPIRED
        payment_method_invalid_or_expired_event.send(
            tokenized_pm.customer_id,
            internal_status=new_status,
            event_sender=ChangeStatusEventSender.CELERY_TASK,
            tokenized_pm_id=tokenized_pm.id,
        )
    elif tokenized_pm.is_expires_soon():
        new_status = TokenizedPaymentMethod.INTERNAL_STATUS.EXPIRES_SOON
        payment_method_expires_soon_event.send(
            tokenized_pm.customer_id, tokenized_pm_id=tokenized_pm.id
        )

    if new_status and new_status != tokenized_pm.internal_status:
        tokenized_pm.internal_status = new_status
        tokenized_pm.save(update_fields=['internal_status'])
