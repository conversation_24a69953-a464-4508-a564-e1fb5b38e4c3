from collections import defaultdict
from django.conf import settings

from country_config.enums import Country
from lib import jinja_renderer
from lib.celery_tools import celery_task
from lib.email import send_email
from lib.point_of_sale.entities import ExtendedBasketEntity
from lib.tools import format_currency
from webapps.payments.serializers.payment import CustomerBasketDetailsSerializer
from webapps.point_of_sale.ports import BasketPort
from webapps.pos.ports import TransactionPort
from webapps.voucher.utils import VoucherMigrationConfig


def _prepare_basket_tax_summary(basket: ExtendedBasketEntity) -> list:
    tax_summaries = defaultdict(
        lambda: {
            'total_tax_amount': 0,
            'total_net_value': 0,
            'total_gross_value': 0,
        }
    )
    for item in basket.items:
        tax_summaries[item.tax_rate]['total_tax_amount'] += item.tax_amount
        tax_summaries[item.tax_rate]['total_net_value'] += item.net_total
        tax_summaries[item.tax_rate]['total_gross_value'] += item.gross_total

    data = [{'tax_rate': rate, **values} for rate, values in tax_summaries.items()]
    return sorted(data, key=lambda x: x['tax_rate'] or 0)


def _should_show_taxes_info(basket: ExtendedBasketEntity) -> bool:
    return not (
        VoucherMigrationConfig.is_strict()
        and len(basket.items) == 1
        and BasketPort.is_basket_voucher_egift_card(basket_id=basket.id)
    )


@celery_task
def send_basket_payment_details_via_email(basket_id: str, email: str, language: str):
    basket = BasketPort.get_basket_details(basket_id=basket_id)
    serialized_basket = CustomerBasketDetailsSerializer(instance=basket).data

    receipt_details = TransactionPort.get_receipt_details(basket_id=basket_id)
    business_details = serialized_basket.get('business_details')

    template_args = {
        'receipt': {
            'id': receipt_details.id,
            'receipt_number': receipt_details.receipt_number,
            'assigned_number': receipt_details.assigned_number,
            'transaction_id': receipt_details.transaction_id,
            'customer_data': receipt_details.customer_data,
        },
        'business_details': {
            'business_name': business_details.get('name'),
            'business_address': business_details.get('address'),
        },
        'basket': {
            'remaining': basket.remaining,
            'total': basket.total,
        },
        'basket_items': basket.items,
        'basket_payments': basket.payments,
        'tax_summary': _prepare_basket_tax_summary(basket),
        'summaries': serialized_basket.get('total_elements'),
        'created': basket.created,
        'show_fiscal_disclaimer': settings.API_COUNTRY in (Country.FR, Country.PL),
        'disclaimer': serialized_basket.get('disclaimer'),
        'show_taxes_info': _should_show_taxes_info(basket),
        'receipt_footer_line_1': serialized_basket.get('receipt_foot_line_1'),
        'receipt_footer_line_2': serialized_basket.get('receipt_foot_line_2'),
        'format_currency': format_currency,
    }

    sjr = jinja_renderer.ScenariosJinjaRenderer()
    body = sjr.render(
        scenario_name='basket',
        template_name='basket_details',
        language=language,
        template_args=template_args,
        extension='html',
        default=(),
    )

    from_data = (
        business_details.get('name'),
        settings.NO_REPLY_EMAIL,
    )

    send_email(
        to_addr=email,
        body=body,
        history_data={
            'sender': 'TODO',
            'business_id': basket.business_id,
            # 'customer_id': transaction.customer_id,
            'task_id': f'pos:receipt:transaction_id={receipt_details.transaction_id}',
            'meta_receipt_number': receipt_details.receipt_number,
        },
        from_data=from_data,
        # to_name=(transaction.customer and transaction.customer.get_full_name()),  # todo
    )
