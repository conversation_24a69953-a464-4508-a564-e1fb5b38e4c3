from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionAPIView
from drf_api_utils.serializers import EmptyRequestSerializer
from webapps.payment_gateway.ports import PaymentGatewayPort


class CustomerCheckBalanceTransactionSecretView(BaseBooksySessionAPIView, GenericAPIView):
    permission_classes = (IsAuthenticated,)
    lookup_url_kwarg = 'balance_transaction_id'
    serializer_class = EmptyRequestSerializer
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    def get(self, request, balance_transaction_id: str):
        customer_wallet = PaymentGatewayPort.get_customer_wallet(
            user_id=self.request.user.id,
        )
        client_token = PaymentGatewayPort.get_payment_client_token(
            balance_transaction_id=balance_transaction_id,
            wallet_id=customer_wallet.id,
        )
        data = {'client_secret': client_token}
        return Response(
            data=data,
            status=status.HTTP_200_OK,
        )
