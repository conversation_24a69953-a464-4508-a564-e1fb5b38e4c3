from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionAPIView
from webapps.payments.serializers.payment import CustomerBasketDetailsSerializer
from webapps.point_of_sale.ports import BasketPort


class CustomerBasketDetailsView(
    BaseBooksySessionAPIView,
    GenericAPIView,
):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    permission_classes = (IsAuthenticated,)
    serializer_class = CustomerBasketDetailsSerializer

    def get(self, request, basket_payment_id: str):
        result = BasketPort.get_basket_details(basket_id=basket_payment_id)
        if result is None:
            return Response(status=status.HTTP_404_NOT_FOUND)

        serializer = self.serializer_class(result)

        return Response(
            data={
                'result': serializer.data,
            },
            status=status.HTTP_200_OK,
        )
