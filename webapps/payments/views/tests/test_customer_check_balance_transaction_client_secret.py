import uuid
from unittest.mock import patch

from django.urls import reverse
from model_bakery import baker
from rest_framework import status

from drf_api.lib.base_drf_test_case import CustomerAPITestCase
from lib.baker_utils import get_or_create_booking_source
from lib.payment_gateway.enums import BalanceTransactionType
from lib.payments.enums import PaymentProviderCode
from service.tests import dict_assert
from webapps.booking.models import BookingSources
from webapps.payment_gateway.models import BalanceTransaction
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import Payment, StripePaymentIntent
from webapps.user.models import User


class CustomerCheckBalanceTransactionStatusViewTestCase(CustomerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+48 697 850 000',
        )
        cls.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )

    def setUp(self):
        super().setUp()
        self.wallet, _ = PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=self.user.id,
            email=self.user.email,
            phone=self.user.cell_phone,
            statement_name='',
        )

    @patch('webapps.payment_providers.providers.stripe.StripeProvider.get_payment_client_token')
    def test_balance_transaction_secret(self, get_payment_client_token_mock):
        get_payment_client_token_mock.return_value = "client_token"

        payment = baker.make(
            Payment,
            provider_code=PaymentProviderCode.STRIPE,
        )
        baker.make(
            StripePaymentIntent,
            payment_id=payment.id,
        )
        balance_transaction = baker.make(
            BalanceTransaction,
            external_id=payment.id,
            transaction_type=BalanceTransactionType.PAYMENT,
            sender_id=self.wallet.id,
            payment_provider_code=PaymentProviderCode.STRIPE,
        )
        response = self._make_get_request(balance_transaction.id)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        dict_assert(response.json(), {'client_secret': 'client_token'})

    def _make_get_request(self, balance_transaction_id: uuid.UUID):
        return self.client.get(
            reverse(
                'payments__check_balance_transaction_client_secret',
                kwargs={
                    'balance_transaction_id': balance_transaction_id,
                },
            ),
            content_type='application/json',
        )
