import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional

from lib.payment_providers.entities import (
    AuthorizePaymentMethodDataEntity,
    DeviceDataEntity,
    PortResponse,
    AuthAdditionalDataEntity,
)
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.entities import (
    BasketEntity,
    BasketPaymentEntity,
    POSEntity,
    BasketItemEntity,
    RelatedBasketItemEntity,
    ExtendedBasketEntity,
)
from lib.point_of_sale.enums import (
    BasketCustomerPaymentAction,
    BasketItemType,
    BasketPaymentAnalyticsTrigger,
    PaymentMethodType,
    RelatedBasketItemType,
    ResponseEntityType,
    BasketPaymentStatus,
    BasketStatus,
)
from lib.tools import major_unit
from webapps.point_of_sale.adapters import (
    get_customer_wallet_id_adapter,
    get_minimal_pba_amount_adapter,
    is_voucher_egift_card_adapter,
)
from webapps.point_of_sale.exceptions import BasketPaymentNotFound
from webapps.point_of_sale.models import Ba<PERSON><PERSON>ay<PERSON>, <PERSON>sket, POS, BasketItem, RelatedBasketItem
from webapps.point_of_sale.services.basket import BasketService
from webapps.point_of_sale.services.basket_item import BasketItemService
from webapps.point_of_sale.services.basket_payment import BasketPaymentService
from webapps.point_of_sale.services.discount import DiscountService
from webapps.point_of_sale.services.pos import POSService


class POSPort:
    @staticmethod
    def get_pos(
        pos_id: Optional[uuid.UUID] = None,
        business_id: Optional[int] = None,
    ) -> Optional[POSEntity]:
        # get pos by either its id or id of related business
        pos: POS = POSService.get_pos(pos_id=pos_id, business_id=business_id)
        if pos is None:
            return None
        return pos.entity

    @staticmethod
    def get_or_create_pos(business_id: int) -> POSEntity:
        pos: POS = POSService.get_or_create_pos(business_id=business_id)
        return pos.entity

    @staticmethod
    def is_method_available(
        pos_id: uuid.UUID,
        payment_method_type: PaymentMethodType,
        payment_provider_code: PaymentProviderCode,
    ) -> bool:
        pos = POSService.get_pos(pos_id=pos_id)
        if pos is None:
            raise ValueError
        return POSService.is_method_available(
            pos=pos,
            payment_method_type=payment_method_type,
            payment_provider_code=payment_provider_code,
        )


class BasketPort:
    @staticmethod
    def get_basket(
        basket_id: Optional[uuid.UUID] = None,
        business_id: Optional[int] = None,
    ) -> Optional[BasketEntity]:
        # TODO https://booksy.atlassian.net/browse/POS-1967
        # get basket by either its id or id of related business
        basket: Basket = BasketService.get_basket(
            basket_id=basket_id,
            business_id=business_id,
        )
        if basket is None:
            raise ValueError(
                f"Can not find Basket with the following "
                f"basket_id: {basket_id} and business_id: {business_id}."
            )
        return basket.entity

    @staticmethod
    def get_basket_ids_with_related_basket_item(
        *,
        external_ids: list[int],
        relation_type: RelatedBasketItemType,
    ) -> list[uuid.UUID]:
        query = RelatedBasketItem.objects.filter(
            external_id__in=external_ids,
            type=relation_type,
        ).values_list("basket_item__basket_id", flat=True)
        return list(query)

    @staticmethod
    def cancel_basket_payment(
        basket_payment_id: uuid.UUID | str,
        user_id: int,
    ) -> PortResponse:
        basket_payment = BasketPaymentService.get_basket_payment(
            basket_payment_id=basket_payment_id,
            user_id=user_id,
        )
        result = BasketPaymentService.cancel_payment(
            basket_payment=basket_payment,
        )
        return PortResponse(
            entity_type=ResponseEntityType.BASKET_PAYMENT_CANCEL_RESULT_ENTITY,
            entity=result,
        )

    @staticmethod
    def get_baskets(
        customer_card_ids: list[int],
        page: int,
        per_page: int,
        basket_status: Optional[BasketStatus] = None,
    ) -> list[ExtendedBasketEntity]:
        if page < 1:  # sanity check in addition to checks on higher levels
            raise ValueError(page)
        if per_page < 1:
            raise ValueError(page)

        qs = (
            Basket.objects.filter(
                customer_card_id__in=customer_card_ids,
                archived=False,
            )
            .prefetch_related('payments')
            .order_by('-created')
        )

        # Filter by basket_status if provided
        if basket_status is not None:
            qs = qs.with_status().filter(status=basket_status)

        results = qs[(page - 1) * per_page : page * per_page]
        entities = [basket.extended_entity for basket in results]
        return entities

    @staticmethod
    def get_basket_details(
        basket_id: str,
    ) -> ExtendedBasketEntity | None:
        basket = (
            Basket.objects.filter(id=basket_id)
            .prefetch_related('payments')
            .prefetch_related('items')
            .first()
        )

        return basket.extended_entity if basket else None

    @staticmethod
    def is_basket_voucher_egift_card(
        basket_id: str,
    ) -> bool:
        voucher_id = (
            RelatedBasketItem.objects.filter(
                basket_item__basket=basket_id,
                basket_item__type=BasketItemType.VOUCHER,
                type=RelatedBasketItemType.VOUCHER,
            )
            .values_list("external_id", flat=True)
            .first()
        )
        if not voucher_id:
            return False
        return is_voucher_egift_card_adapter(voucher_id=voucher_id)


class BasketPaymentPort:
    @staticmethod
    def get_basket_payments(ids: list[uuid.UUID]) -> list[BasketPaymentEntity]:
        return [p.entity for p in BasketPaymentService.get_basket_payments(ids=ids)]

    @staticmethod
    def get_basket_payment_entity(
        basket_payment_id: uuid.UUID | str = None,
        user_id: int = None,
        balance_transaction_id: uuid.UUID = None,
    ) -> BasketPaymentEntity | None:
        try:
            basket_payment = BasketPaymentService.get_basket_payment(
                basket_payment_id=basket_payment_id,
                user_id=user_id,
                balance_transaction_id=balance_transaction_id,
            )
        except BasketPaymentNotFound:
            return None

        return basket_payment.entity

    @staticmethod
    def get_latest_refund_child(basket_payment_id: uuid.UUID) -> BasketPaymentEntity | None:
        latest_refund_child = BasketPaymentService.get_latest_refund_child(basket_payment_id)
        if latest_refund_child:
            return latest_refund_child.entity
        return None

    @staticmethod
    def make_basket_payment(
        basket_payment_id: uuid.UUID,
        user_id: int,
        payment_method_data: AuthorizePaymentMethodDataEntity,
        trigger: BasketPaymentAnalyticsTrigger,
        device_data: DeviceDataEntity,
        additional_data: AuthAdditionalDataEntity | None = None,
    ):
        basket_payment: BasketPayment = BasketPaymentService.get_basket_payment(
            basket_payment_id=basket_payment_id,
            user_id=user_id,
        )
        wallet_id = get_customer_wallet_id_adapter(
            user_id=user_id,
        )

        BasketPaymentService.make_basket_payment(
            basket_payment=basket_payment,
            wallet_id=wallet_id,
            payment_method_data=payment_method_data,
            trigger=trigger,
            device_data=device_data,
            additional_data=additional_data,
        )

    @staticmethod
    def is_customer_action_allowed(
        basket_payment_id: uuid.UUID,
        user_id: int,
        action: BasketCustomerPaymentAction,
    ) -> bool:
        basket_payment: BasketPayment = BasketPaymentService.get_basket_payment(
            basket_payment_id=basket_payment_id,
            user_id=user_id,
        )

        return BasketPaymentService.is_customer_action_allowed(
            basket_payment=basket_payment,
            action=action,
        )

    @staticmethod
    def validate_pba_amount(
        amount: int,
    ) -> (bool, Decimal):
        minimal_amount = get_minimal_pba_amount_adapter()

        return major_unit(amount) >= minimal_amount, minimal_amount

    @staticmethod
    def get_successful_payments(ids: list[uuid.UUID]) -> list[BasketPaymentEntity]:
        basket_payments = BasketPayment.objects.filter(
            id__in=ids, status=BasketPaymentStatus.SUCCESS
        )
        return [basket_payment.entity for basket_payment in basket_payments]

    @staticmethod
    def get_payments_for_basket(basket_id: uuid.UUID):
        basket_payments = BasketPayment.objects.filter(basket_id=basket_id)

        return [basket_payment.entity for basket_payment in basket_payments]


class BasketItemPort:
    @staticmethod
    def get_basket_items(ids: list[uuid.UUID]) -> list[BasketItemEntity]:
        return [basket_item.entity for basket_item in BasketItem.all_objects.filter(id__in=ids)]


class RelatedBasketItemPort:
    @staticmethod
    def get_related_basket_items_by_basket_items(
        ids: list[uuid.UUID], relation_types: list[RelatedBasketItemType]
    ) -> list[RelatedBasketItemEntity]:
        return [
            related_basket_item.entity
            for related_basket_item in RelatedBasketItem.objects.filter(
                basket_item_id__in=ids,
                type__in=relation_types,
            )
        ]

    @staticmethod
    def get_related_basket_items_by_external_ids(
        external_ids: list[int], relation_type: RelatedBasketItemType
    ) -> list[RelatedBasketItemEntity]:
        return [
            related_basket_item.entity
            for related_basket_item in RelatedBasketItem.objects.filter(
                external_id__in=external_ids, type=relation_type
            )
        ]

    @staticmethod
    def get_appointment_id_for_basket(basket_id: uuid.UUID) -> int | None:
        return BasketItemService.get_appointment_id_for_basket(basket_id=basket_id)


class DiscountPort:
    @staticmethod
    def get_tap_to_pay_promo_start(business_id: int) -> datetime | None:
        return DiscountService.get_tap_to_pay_promo_start(business_id)

    @staticmethod
    def is_tap_to_pay_promo_active(business_id: int) -> bool:
        return DiscountService.is_tap_to_pay_promo_active(business_id)
