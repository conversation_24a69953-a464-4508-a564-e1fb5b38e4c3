import datetime
import uuid
from dataclasses import asdict
from decimal import Decimal

from django.db import transaction
from django.utils import timezone

from lib.feature_flag.feature.payment import TapToPayDiscountFlag, BlikDiscountFlag
from lib.payment_gateway.entities import (
    BalanceTransactionEntity,
    DisputeSplitsEntity,
    PaymentSplitsEntity,
    RefundSplitsEntity,
    WalletEntity,
)
from lib.payment_gateway.enums import (
    BalanceTransactionStatus,
    DisputeType,
    PaymentStatus,
)
from lib.payment_providers.entities import (
    AuthAdditionalDataEntity,
    AuthorizePaymentMethodDataEntity,
    DeviceDataEntity,
)
from lib.payments.enums import (
    PaymentError,
    PaymentProviderCode,
    RefundError,
)
from lib.point_of_sale.enums import (
    BasketCustomerPaymentAction,
    BasketPaymentAnalyticsTrigger,
    BasketPaymentSource,
    BasketPaymentStatus,
    BasketPaymentType,
    BasketTipSource,
    PaymentMethodType,
)
from lib.point_of_sale.events import (
    basket_payment_added_event,
    basket_payment_amount_updated_event,
    basket_payment_details_updated_event,
)
from lib.smartlock import SmartLock
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.point_of_sale.adapters import (
    authorize_payment_adapter,
    calculate_fees_adapter,
    cancel_balance_transaction_adapter,
    capture_payment_adapter,
    get_anonymous_wallet_id_adapter,
    get_business_wallet_id_adapter,
    get_customer_wallet_id_adapter,
    get_last_payment_row_entity_adapter,
    initialize_payment_adapter,
    initialize_refund_adapter,
    is_off_session_transaction_adapter,
    is_refund_possible_adapter,
    synchronize_stripe_payment_intent_adapter,
)
from webapps.point_of_sale.consts import (
    BALANCE_TRANSACTION_STATUS_INTO_BASKET_PAYMENT_STATUS_MAP,
    BASKET_PAYMENT_CUSTOMER_ALLOWED_ACTIONS_STATUSES,
    DISPUTE_TYPE_INTO_BASKET_PAYMENT_TYPE_MAP,
    PAYMENT_STATUS_INTO_BASKET_PAYMENT_STATUS_MAP,
)
from webapps.point_of_sale.exceptions import (
    BasketPaymentNotFound,
    InvalidBasketPayment,
    OperationNotAllowed,
)
from webapps.point_of_sale.models import (
    Basket,
    BasketPayment,
)
from webapps.point_of_sale.services.basket import BasketService
from webapps.point_of_sale.services.basket_payment_analytics import BasketPaymentAnalyticsService
from webapps.point_of_sale.services.basket_tip import BasketTipService
from webapps.point_of_sale.services.discount import DiscountService
from webapps.pos.enums import PaymentTypeEnum


class BasketPaymentService:
    @staticmethod
    def _change_basket_payment_status(
        basket_payment: BasketPayment,
        new_status: BasketPaymentStatus,
    ) -> BasketPayment:
        """
        Changes status of basket payment.
        SUCCESS, FAILED and CANCELED states are terminal.

        :param basket_payment: BasketPayment object
        :param new_status: status we want to set in provided BasketPayment
        """
        allowed_new_status = {
            BasketPaymentStatus.PENDING: [
                BasketPaymentStatus.PENDING,
                BasketPaymentStatus.ACTION_REQUIRED,
                BasketPaymentStatus.SUCCESS,
                BasketPaymentStatus.FAILED,
                BasketPaymentStatus.CANCELED,
            ],
            BasketPaymentStatus.ACTION_REQUIRED: [
                BasketPaymentStatus.PENDING,
                BasketPaymentStatus.SUCCESS,
                BasketPaymentStatus.FAILED,
                BasketPaymentStatus.CANCELED,
            ],
            BasketPaymentStatus.SUCCESS: [],
            BasketPaymentStatus.FAILED: (
                [BasketPaymentStatus.PENDING]
                if basket_payment.payment_method == PaymentMethodType.KEYED_IN_PAYMENT
                else []
            ),
            BasketPaymentStatus.CANCELED: [],
        }[basket_payment.status]

        if new_status not in allowed_new_status:
            raise OperationNotAllowed(f"Invalid transition {basket_payment.status} -> {new_status}")

        basket_payment.status = new_status
        return basket_payment

    @staticmethod
    def get_basket_payment(
        basket_payment_id: uuid.UUID | str = None,
        user_id: int = None,
        balance_transaction_id: uuid.UUID = None,
    ) -> BasketPayment | None:
        """
        Returns Basket Payment or None

        :param basket_payment_id: BasketPayment id
        :param user_id: User.id
        :param balance_transaction_id: BalanceTransaction id
        """
        if not (basket_payment_id or balance_transaction_id):
            return None

        query_params = {}

        if basket_payment_id:
            query_params['id'] = basket_payment_id
        if user_id:
            query_params['user_id'] = user_id
        if balance_transaction_id:
            query_params['balance_transaction_id'] = balance_transaction_id

        basket_payment = BasketPayment.objects.filter(**query_params).first()
        if not basket_payment:
            raise BasketPaymentNotFound("Couldn't find BasketPayment for given parameters")
        return basket_payment

    @staticmethod
    def get_basket_payments(ids: list[uuid.UUID]) -> list[BasketPayment]:
        return list(BasketPayment.objects.filter(id__in=ids))

    # pylint: disable=too-many-return-statements
    @staticmethod
    def _get_splits_after_discounts(
        basket_payment: BasketPayment,
        payment_splits: PaymentSplitsEntity | None = None,
        refund_splits: RefundSplitsEntity | None = None,
        dispute_splits: DisputeSplitsEntity | None = None,
    ) -> tuple[PaymentSplitsEntity | None, RefundSplitsEntity | None, DisputeSplitsEntity | None]:
        if basket_payment.payment_method == PaymentMethodType.TAP_TO_PAY:
            return BasketPaymentService._get_splits_after_ttp_discount(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )

        if basket_payment.payment_method == PaymentMethodType.BLIK:
            return BasketPaymentService._get_splits_after_blik_discount(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )

        # if other payment method, then apply no changes
        return payment_splits, refund_splits, dispute_splits

    @staticmethod
    def _get_splits_after_ttp_discount(
        basket_payment: BasketPayment,
        payment_splits: PaymentSplitsEntity | None = None,
        refund_splits: RefundSplitsEntity | None = None,
        dispute_splits: DisputeSplitsEntity | None = None,
    ) -> tuple[PaymentSplitsEntity | None, RefundSplitsEntity | None, DisputeSplitsEntity | None]:
        discounted_payment_method = PaymentMethodType.TAP_TO_PAY

        ttp_discount_data = TapToPayDiscountFlag()
        if not ttp_discount_data:
            # feature disabled
            return payment_splits, refund_splits, dispute_splits

        try:
            ttp_discount_max_payment_amount = int(ttp_discount_data.get('max_payment_amount'))
            ttp_discount_payments_count_limit = int(ttp_discount_data.get('payments_count_limit'))
            ttp_discount_max_time_to_use = datetime.timedelta(
                minutes=ttp_discount_data.get('max_time_to_use')
            )
        except Exception as e:  # pylint: disable=broad-exception-caught
            print(f"TapToPayDiscountFlag decoding error: {e}, defaulting to no discount")
            return payment_splits, refund_splits, dispute_splits

        # only for tap to pay
        if basket_payment.payment_method != discounted_payment_method:
            return payment_splits, refund_splits, dispute_splits

        # only up to a given amount
        if basket_payment.amount > ttp_discount_max_payment_amount:
            return payment_splits, refund_splits, dispute_splits

        # TODO use DiscountService.is_tap_to_pay_promo_active instead for checks below
        # Checks below make decitions based on business state rather than transaction itself.

        # only for a given amount of time
        business_id = basket_payment.basket.business_id
        accepted_at = DiscountService.get_tap_to_pay_promo_start(business_id)
        if not accepted_at:
            return payment_splits, refund_splits, dispute_splits

        if timezone.now() - accepted_at > ttp_discount_max_time_to_use:
            return payment_splits, refund_splits, dispute_splits

        # only for a limited number of payments
        payment_count = DiscountService.get_tap_to_pay_promo_transactions(business_id).count()
        if payment_count > ttp_discount_payments_count_limit:
            return payment_splits, refund_splits, dispute_splits

        # if all conditions are met, set payment and refund splits to 0 (disputes remain the same)
        payment_splits = PaymentSplitsEntity(
            fixed_fee=0,
            percentage_fee=Decimal(0),
        )
        refund_splits = RefundSplitsEntity(
            fixed_fee=0,
            percentage_fee=Decimal(0),
        )
        return payment_splits, refund_splits, dispute_splits

    @staticmethod
    def _get_splits_after_blik_discount(
        basket_payment: BasketPayment,
        payment_splits: PaymentSplitsEntity | None = None,
        refund_splits: RefundSplitsEntity | None = None,
        dispute_splits: DisputeSplitsEntity | None = None,
    ) -> tuple[PaymentSplitsEntity | None, RefundSplitsEntity | None, DisputeSplitsEntity | None]:
        discounted_payment_method = PaymentMethodType.BLIK

        # only for blik
        if basket_payment.payment_method != discounted_payment_method:
            return payment_splits, refund_splits, dispute_splits

        # only for prepayments and checkout
        if basket_payment.source not in {
            BasketPaymentSource.PREPAYMENT,
            BasketPaymentSource.PAYMENT,
        }:
            return payment_splits, refund_splits, dispute_splits

        # parse settings from the feature flag
        blik_discount_data = BlikDiscountFlag()
        if not blik_discount_data:
            # feature disabled
            return payment_splits, refund_splits, dispute_splits
        try:
            prepayment_promo_start = int(blik_discount_data.get('prepayment_promo_start'))
            prepayment_promo_end = int(blik_discount_data.get('prepayment_promo_end'))
            checkout_promo_start = int(blik_discount_data.get('checkout_promo_start'))
            checkout_promo_end = int(blik_discount_data.get('checkout_promo_end'))
        except Exception as e:  # pylint: disable=broad-exception-caught
            print(f"BlikDiscountFlag decoding error: {e}, defaulting to no discount")
            return payment_splits, refund_splits, dispute_splits

        # check campaign time settings and blacklists
        current_timestamp = int(datetime.datetime.now().timestamp())
        if basket_payment.source == BasketPaymentSource.PAYMENT:
            # enable all checkout payments, just make sure it's within campaign time limits
            if current_timestamp < checkout_promo_start or current_timestamp > checkout_promo_end:
                return payment_splits, refund_splits, dispute_splits

        elif basket_payment.source == BasketPaymentSource.PREPAYMENT:
            # if it's a prepayment, then in addition to checking time limits, also
            # make sure that the merchant is not blacklisted
            if (
                current_timestamp < prepayment_promo_start
                or current_timestamp > prepayment_promo_end
            ):
                return payment_splits, refund_splits, dispute_splits

            from webapps.pos.ports import POSPort  # pylint: disable=cyclic-import

            if not POSPort.blik_in_prepayment_promo_enabled(
                business_id=basket_payment.basket.business_id
            ):
                return payment_splits, refund_splits, dispute_splits

        # if all conditions are met, set payment and refund splits to 0 (disputes remain the same)
        payment_splits = PaymentSplitsEntity(
            fixed_fee=0,
            percentage_fee=Decimal(0),
        )
        refund_splits = RefundSplitsEntity(
            fixed_fee=0,
            percentage_fee=Decimal(0),
        )
        return payment_splits, refund_splits, dispute_splits

    @staticmethod
    def initialize_basket_payment(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        basket: Basket,
        basket_payment_type: BasketPaymentType,
        amount: int,
        payment_method_type: PaymentMethodType,
        payment_initialization: bool,
        status: BasketPaymentStatus,
        auto_capture: bool,
        source: BasketPaymentSource,
        payment_splits: PaymentSplitsEntity | None = None,
        refund_splits: RefundSplitsEntity | None = None,
        dispute_splits: DisputeSplitsEntity | None = None,
        payment_provider_code: PaymentProviderCode | None = None,
        user_id: int | None = None,  # User.User
        operator_id: int | None = None,  # Business.Resource
        parent_basket_payment: BasketPayment | None = None,
        payment_token: str = None,
    ) -> BasketPayment:
        """
        Main method initializing BasketPayments and
        if it is needed trigger creation flow in PaymentGateway app.

        Method should be used to initialize payment from Customer/Merchant side.
        To create refund use - initialize_refund_basket_payment.
        To create dispute use - create_dispute_basket_payment.

        Make sure that after calling initialize_basket_payment there is BasketService.save_history
        called.

        :param basket: Basket object
        :param basket_payment_type: BasketPaymentType - main purpose of BasketPayment
        :param amount: Amount of BasketPayment, minor_unit
        :param payment_method_type: PaymentMethodTYpe - what method type uses this BasketPayment
        :param payment_initialization: if true PaymentGateway objects creation will be triggered.
            It is false for PBA payment creation in TransactionSerializer, because we should create
            BalanceTransaction only if BasketPayment won't change.
            In this case Customer can change Tip before accepting the payment. After accepting
            payment by Customer initialize_payment_balance_transaction is triggered directly.
        :param status: status of BasketPayment
        :param auto_capture: After successful authorization automatic capture will be processed.
            False is actually for only one scenario - Prepayment in semi-auto Business.
        :param source: Source of BasketPayment - event which initialized it
        :param payment_splits: fixed_fee + percentage_fee - fees that will be applied to this amount
        :param refund_splits: fixed_fee + percentage_fee used during Refund process
        :param dispute_splits: fixed_fee + percentage_fee used during Chargeback process
        :param payment_provider_code: PaymentProviderCode - required for online payments
        :param user_id: user_id - who sends money
        :param operator_id: StafferId who process payment
        :param parent_basket_payment: original payment - used only in creation of refund
        :param payment_token: payment token used for PaymentMethodType.KEYED_IN_PAYMENT
        :return: BasketPayment object
        """
        basket_payment = BasketPayment.objects.create(
            basket=basket,
            type=basket_payment_type,
            amount=amount,
            payment_method=payment_method_type,
            payment_provider_code=payment_provider_code,
            user_id=user_id,
            operator_id=operator_id,
            status=status,
            parent_basket_payment=parent_basket_payment,
            auto_capture=auto_capture,
            source=source,
        )

        if (
            payment_initialization
            and basket_payment_type == BasketPaymentType.PAYMENT
            and payment_provider_code
        ):
            payment_splits, refund_splits, dispute_splits = (
                BasketPaymentService._get_splits_after_discounts(
                    basket_payment=basket_payment,
                    payment_splits=payment_splits,
                    refund_splits=refund_splits,
                    dispute_splits=dispute_splits,
                )
            )
            BasketPaymentService.initialize_payment_balance_transaction(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
                payment_token=payment_token,
            )

        basket_payment_added_event.send(asdict(basket_payment.entity))
        return basket_payment

    @staticmethod
    def change_basket_payment_payment_method(
        basket_payment: BasketPayment,
        new_payment_method: PaymentMethodType,
    ):
        """
        Method allows to change PaymentMethod during Card payment.
        Business selects PayByApp as a payment method, customer can change it to
        Apple Pay/GooglePay. BasketHistory is saved after saving BasketPayment.

        :param basket_payment: BasketPayment with Card PaymentMethod.
        :param new_payment_method: Should be APPLE_PAY / GOOGLE_PAY
        """
        allowed_new_pm = {
            PaymentMethodType.CARD: [
                PaymentMethodType.APPLE_PAY,
                PaymentMethodType.GOOGLE_PAY,
                PaymentMethodType.BLIK,
                PaymentMethodType.KEYED_IN_PAYMENT,
            ],
            PaymentMethodType.BLIK: [
                PaymentMethodType.BLIK,
            ],
            PaymentMethodType.KEYED_IN_PAYMENT: [
                PaymentMethodType.KEYED_IN_PAYMENT,
            ],
        }.get(basket_payment.payment_method, [])

        if new_payment_method not in allowed_new_pm:
            raise OperationNotAllowed(
                f"Invalid PM transition {basket_payment.payment_method} -> {new_payment_method}"
            )

        basket_payment.payment_method = new_payment_method
        basket_payment.save(update_fields=['payment_method'])
        BasketService.save_history(basket_payment.basket)

        basket_payment_details_updated_event.send(asdict(basket_payment.entity))

    @staticmethod
    def initialize_payment_balance_transaction(
        basket_payment: BasketPayment,
        payment_splits: PaymentSplitsEntity,
        refund_splits: RefundSplitsEntity,
        dispute_splits: DisputeSplitsEntity,
        payment_token: str = None,
    ):
        """
        Method handles creation of payment in PaymentGateway app.
        Fees need to be calculated upfront. Future refunds/chargeback will use fees calculated
        at the moment of initialization of payment.

        If Sender wallet is not provided Anonymous wallet is used.
        It saves BasketPayment.

        :param basket_payment: object of BasketPayment, type Payment
        :param payment_splits: fixed_fee + percentage_fee - fees that will be applied to this amount
        :param refund_splits: fixed_fee + percentage_fee used during Refund process
        :param dispute_splits: fixed_fee + percentage_fee used during Chargeback process
        :param payment_token: payment token
        """
        if (
            basket_payment.type != BasketPaymentType.PAYMENT
            or not basket_payment.payment_provider_code
        ):
            raise InvalidBasketPayment

        # get sender wallet
        if basket_payment.user_id:
            sender_wallet_id = get_customer_wallet_id_adapter(basket_payment.user_id)
        else:
            sender_wallet_id = get_anonymous_wallet_id_adapter()

        # get receiver wallet
        receiver_wallet_id = get_business_wallet_id_adapter(basket_payment.basket.business_id)

        balance_transaction_id = initialize_payment_adapter(
            amount=basket_payment.amount,
            sender_id=sender_wallet_id,
            receiver_id=receiver_wallet_id,
            payment_method=PaymentMethodType(basket_payment.payment_method),
            payment_provider_code=PaymentProviderCode(basket_payment.payment_provider_code),
            auto_capture=basket_payment.auto_capture,
            payment_splits=payment_splits,
            refund_splits=refund_splits,
            dispute_splits=dispute_splits,
            payment_token=payment_token,
        )
        basket_payment.balance_transaction_id = balance_transaction_id
        basket_payment.save(update_fields=['balance_transaction_id'])

    @staticmethod
    def _initialize_refund_balance_transaction(
        basket_payment: BasketPayment,
    ):
        """
        Handles creation of refund in PaymentGateway app.
        It saves BasketPayment

        :param basket_payment: not saved object of BasketPayment, type Refund
        """
        if (
            basket_payment.type != BasketPaymentType.REFUND
            or not basket_payment.payment_provider_code
        ):
            raise InvalidBasketPayment

        # balance_transaction owner wallet
        wallet_id = get_business_wallet_id_adapter(basket_payment.basket.business_id)

        refund_balance_transaction_id = initialize_refund_adapter(
            payment_balance_transaction_id=(
                basket_payment.parent_basket_payment.balance_transaction_id
            ),
            wallet_id=wallet_id,
            amount=basket_payment.amount,
        )
        basket_payment.balance_transaction_id = refund_balance_transaction_id
        basket_payment.save(update_fields=['balance_transaction_id'])

    @staticmethod
    @transaction.atomic
    def update_basket_payment_details(
        basket_payment: BasketPayment,
        new_status: BasketPaymentStatus,
        error_code: PaymentError | None = None,
        action_required_details: dict | None = None,
    ) -> BasketPayment:
        """
        Updates status of BasketPayment. Validation of status transition included.
        If error_code/action_required_details are provided - saves them,
        otherwise these fields stays untouched.

        It saves history.

        :param basket_payment: BasketPayment which will be edited
        :param new_status: status we want to set in provided BasketPayment
        :param error_code: errors returned during process
        :param action_required_details: for now 3ds process details
        """
        with SmartLock(key=str(basket_payment.id)):
            basket_payment.refresh_from_db()

            # If basket_payment is already in canceled status we don't want to set it second time.
            # Cancel process triggers it twice, because:
            # 1. First time is synchronous action in BasketPaymentService.cancel_payment
            # 2. Second can be triggered as signal callback after canceling BalanceTransaction
            #    - we want to omit it
            if (
                basket_payment.status == BasketPaymentStatus.CANCELED
                and new_status == BasketPaymentStatus.CANCELED
            ):
                return basket_payment

            basket_payment = BasketPaymentService._change_basket_payment_status(
                basket_payment=basket_payment,
                new_status=new_status,
            )

            basket_payment.error_code = error_code
            basket_payment.action_required_details = action_required_details
            basket_payment.save(update_fields=['error_code', 'status', 'action_required_details'])
            BasketService.save_history(basket_payment.basket)

            basket_payment_details_updated_event.send(asdict(basket_payment.entity))

            return basket_payment

    @staticmethod
    @transaction.atomic
    def update_basket_payment_amount(
        basket_payment: BasketPayment,
        new_amount: int,
    ):
        """
        Updates amount of BasketPayment. Should be used only in update tip amount scenario.
        It saves history.

        :param basket_payment: BasketPayment which will be edited
        :param new_amount: new amount
        """
        with SmartLock(key=str(basket_payment.id)):
            basket_payment.refresh_from_db()
            if basket_payment.amount == new_amount:
                return basket_payment

            basket_payment.amount = new_amount
            basket_payment.save(update_fields=['amount'])
            BasketService.save_history(basket_payment.basket)

            return basket_payment

    @staticmethod
    @transaction.atomic
    def update_basket_payment_amount_bcr_flow(
        basket_payment: BasketPayment,
        new_amount: int,
        metadata: dict | None = None,
    ) -> BasketPayment:
        """
        Add tip from BCR. There is only add scenario. Update is not possible, so we don't need
        to delete old BasketTips.

        Updates amount of BasketPayment and creates new BasketTip. It saves history.

        :param basket_payment: BasketPayment which will be edited
        :param new_amount: new amount in minor unit
        :param metadata: In metadata there should be 'tip_amount', describing amount of tip
            in minor unit and 'summed_tip_amount' - summ of tip related to that Basket
        """
        with SmartLock(key=str(basket_payment.id)):
            basket_payment.refresh_from_db()
            if (
                basket_payment.amount == new_amount
                and metadata is None
                or basket_payment.metadata == metadata
            ):
                return basket_payment

            basket_payment.amount = new_amount
            basket_payment.metadata = metadata if metadata is not None else basket_payment.metadata

            basket_tip = BasketTipService.create_basket_tip(
                basket=basket_payment.basket,
                staffer_id=basket_payment.operator_id,
                amount=basket_payment.metadata['tip_amount'],
                source=BasketTipSource.BCR,
            )
            basket_payment.save(update_fields=['amount', 'metadata'])

            BasketService.save_history(basket_payment.basket)

            # Basket tip is required to save related PaymentRow, but we don't want to save it in
            # BasketPayment, so that data is injected only to entity
            basket_payment_entity_dict = asdict(basket_payment.entity)
            basket_payment_entity_dict['metadata']['basket_tip_id'] = str(basket_tip.id)

            summed_tip_amount = BasketTipService.get_tip_sum(basket_payment.basket)
            basket_payment_entity_dict['metadata']['summed_tip_amount'] = summed_tip_amount
            basket_payment_amount_updated_event.send(basket_payment_entity_dict)

            return basket_payment

    @staticmethod
    def initialize_refund_basket_payment(
        original_basket_payment: BasketPayment,
        amount: int | None = None,
    ):
        """Method handles creating of Refund BasketPayment.
        Refund will be initialized in PaymentGateway / PaymentProviders.

        :param original_basket_payment: Source of refund
        :param amount: If amount is provided partial refund will be handled. If not amount is
            the same as in original BasketPayment
        """
        basket_payment = BasketPayment.objects.create(
            basket=original_basket_payment.basket,
            type=BasketPaymentType.REFUND,
            amount=original_basket_payment.amount if amount is None else amount,
            payment_method=original_basket_payment.payment_method,
            payment_provider_code=original_basket_payment.payment_provider_code,
            user_id=original_basket_payment.user_id,
            status=BasketPaymentStatus.PENDING,
            parent_basket_payment=original_basket_payment,
            source=original_basket_payment.source,
        )

        with SmartLock(key=str(basket_payment.id)):
            BasketPaymentService._initialize_refund_balance_transaction(
                basket_payment=basket_payment
            )

        basket_payment_added_event.send(asdict(basket_payment.entity))
        return basket_payment

    @staticmethod
    def is_refund_possible(
        original_basket_payment: BasketPayment,
    ) -> tuple[bool, RefundError | None]:
        """
        Returns information about the refund in form of a tuple
        (is possible, error)

        error is present only if refund is not possible

        :param original_basket_payment: basket payment to be checked
        """
        if original_basket_payment.type != BasketPaymentType.PAYMENT:
            raise InvalidBasketPayment('Wrong original basket payment type')

        if not original_basket_payment.balance_transaction_id:
            raise InvalidBasketPayment('Basket Payment has no related Balance Transaction')

        if (
            original_basket_payment.payment_method == PaymentMethodType.BOOKSY_GIFT_CARD
            or original_basket_payment.payment_provider_code
            == PaymentProviderCode.BOOKSY_GIFT_CARDS
        ):
            raise InvalidBasketPayment("Booksy Gift Cards payment method is not refundable")

        receiver_wallet_id = get_business_wallet_id_adapter(
            original_basket_payment.basket.business_id
        )
        return is_refund_possible_adapter(
            balance_transaction_id=original_basket_payment.balance_transaction_id,
            wallet_id=receiver_wallet_id,
            amount=original_basket_payment.amount,
        )

    @staticmethod
    def is_customer_action_allowed(
        basket_payment: BasketPayment,
        action: BasketCustomerPaymentAction,
    ):
        allowed_type, allowed_statuses = BASKET_PAYMENT_CUSTOMER_ALLOWED_ACTIONS_STATUSES[action]
        if not allowed_type or not allowed_statuses:
            raise NotImplementedError(
                f'Fof action: {action}, method is_customer_action_allowed is not implemented'
            )
        if basket_payment.type == allowed_type and basket_payment.status in allowed_statuses:
            return True

        return False

    @staticmethod
    def create_dispute_basket_payment(
        balance_transaction: BalanceTransactionEntity,
    ) -> BasketPayment:
        """
        Handles creation of dispute BasketPayment.

        This is "reversed" flow - Dispute object is firstly created in
        PaymentProviders and PaymentGateway app after receiving information on webhook.

        It saves history.

        :param balance_transaction: Dataclass of BalanceTransaction from PaymentGateway app
        """
        original_basket_payment: BasketPayment = BasketPayment.objects.get(
            balance_transaction_id=balance_transaction.parent_balance_transaction_id
        )

        basket_type = BasketPaymentService.map_balance_transaction_dispute_type(
            balance_transaction.dispute.type
        )

        basket_payment = BasketPayment(
            basket=original_basket_payment.basket,
            type=basket_type,
            amount=balance_transaction.amount,
            payment_method=balance_transaction.payment_method,
            payment_provider_code=balance_transaction.payment_provider_code,
            user_id=original_basket_payment.user_id,
            status=BasketPaymentService.map_balance_transaction_status(balance_transaction.status),
            parent_basket_payment=original_basket_payment,
            balance_transaction_id=balance_transaction.id,
            source=original_basket_payment.source,
        )
        basket_payment.save()
        BasketService.save_history(basket_payment.basket)
        basket_payment_added_event.send(asdict(basket_payment.entity))

        return basket_payment

    @staticmethod
    def make_basket_payment(
        basket_payment: BasketPayment,
        wallet_id: uuid.UUID,
        payment_method_data: AuthorizePaymentMethodDataEntity,
        trigger: BasketPaymentAnalyticsTrigger,
        device_data: DeviceDataEntity,
        additional_data: AuthAdditionalDataEntity | None = None,
    ):
        payment_row_entity = get_last_payment_row_entity_adapter(
            basket_payment_id=basket_payment.id,
        )

        payment_splits, refund_splits, dispute_splits = calculate_fees_adapter(
            payment_row_id=payment_row_entity.id,
            payment_provider_code=basket_payment.payment_provider_code,
        )

        if payment_method_data.is_external_payment_method():
            BasketPaymentService.change_basket_payment_payment_method(
                basket_payment=basket_payment,
                new_payment_method=PaymentMethodType[
                    payment_method_data.external_token_type.upper()
                ],
            )

        payment_splits, refund_splits, dispute_splits = (
            BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
        )
        BasketPaymentService.initialize_payment_balance_transaction(
            basket_payment=basket_payment,
            payment_splits=payment_splits,
            refund_splits=refund_splits,
            dispute_splits=dispute_splits,
        )

        basket_payment.refresh_from_db()
        synchronize_stripe_payment_intent_adapter(
            basket_payment_entity=basket_payment.entity,
        )

        BasketPaymentService.authorize_payment(
            basket_payment=basket_payment,
            wallet_id=wallet_id,
            additional_data=additional_data,
            off_session=is_off_session_transaction_adapter(
                receipt_id=payment_row_entity.receipt_id,
            ),
            payment_method_data=payment_method_data,
        )

        BasketPaymentAnalyticsService.create_analytics_data(
            basket_payment=basket_payment,
            device_data=device_data,
            trigger=trigger,
            payment_link=payment_row_entity.payment_link,
        )

    @staticmethod
    def authorize_payment(
        basket_payment: BasketPayment,
        wallet_id: uuid.UUID,
        payment_method_data: AuthorizePaymentMethodDataEntity,
        additional_data: AuthAdditionalDataEntity | None = None,
        off_session: bool | None = None,
    ) -> None:
        with SmartLock(key=str(basket_payment.id)):
            authorize_payment_adapter(
                balance_transaction_id=basket_payment.balance_transaction_id,
                wallet_id=wallet_id,
                additional_data=additional_data,
                payment_method_data=payment_method_data,
                off_session=off_session,
            )

    @staticmethod
    def capture_payment(
        basket_payment: BasketPayment,
        wallet_id: uuid.UUID,
    ) -> None:
        with SmartLock(key=str(basket_payment.id)):
            capture_payment_adapter(
                balance_transaction_id=basket_payment.balance_transaction_id,
                wallet_id=wallet_id,
            )

    @staticmethod
    def cancel_payment(
        basket_payment: BasketPayment,
    ) -> bool:
        with SmartLock(key=str(basket_payment.id)):
            result = True
            if (
                basket_payment.payment_method == PaymentTypeEnum.KEYED_IN_PAYMENT
                and basket_payment.status == BasketPaymentStatus.FAILED
                and basket_payment.source == BasketPaymentSource.PREPAYMENT
            ):
                return True
            if not BasketPaymentService.is_customer_action_allowed(
                basket_payment=basket_payment,
                action=BasketCustomerPaymentAction.CANCEL_PAYMENT,
            ):
                raise OperationNotAllowed(
                    f'Cant cancel BasketPayment in status: {basket_payment.status}',
                )

            if basket_payment.balance_transaction_id:
                wallet_entity: WalletEntity = PaymentGatewayPort.get_customer_wallet(
                    user_id=basket_payment.user_id,
                )
                # Mobile payments cannot be initialized in PaymentGateway, so if balanceTransaction
                # doesn't exist we don't want to trigger whole flow
                result = cancel_balance_transaction_adapter(
                    balance_transaction_id=basket_payment.balance_transaction_id,
                    wallet_id=wallet_entity.id,
                )

            if result:
                BasketPaymentService.update_basket_payment_details(
                    basket_payment=basket_payment,
                    new_status=BasketPaymentStatus.CANCELED,
                )
            return result

    @staticmethod
    def map_balance_transaction_status(status: BalanceTransactionStatus) -> BasketPaymentStatus:
        """Maps BalanceTransaction status into BasketPaymentStatus."""
        return BALANCE_TRANSACTION_STATUS_INTO_BASKET_PAYMENT_STATUS_MAP[status]

    @staticmethod
    def map_balance_transaction_payment_status(status: PaymentStatus) -> BasketPaymentStatus:
        """Maps PaymentStatus of Balance Transaction into BasketPaymentStatus."""
        return PAYMENT_STATUS_INTO_BASKET_PAYMENT_STATUS_MAP[status]

    @staticmethod
    def map_balance_transaction_dispute_type(bt_dispute_type: DisputeType) -> BasketPaymentType:
        """Maps DisputeType of BalanceTransaction into BasketPaymentType"""
        return DISPUTE_TYPE_INTO_BASKET_PAYMENT_TYPE_MAP[bt_dispute_type]

    @classmethod
    def get_latest_refund_child(cls, basket_payment_id: uuid.UUID) -> BasketPayment:
        payment = BasketPayment.objects.get(id=basket_payment_id)
        return payment.basketpayment_set.filter(type=BasketPaymentType.REFUND).last()
