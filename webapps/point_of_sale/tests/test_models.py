from django.test import TestCase
from model_bakery import baker

from lib.point_of_sale.enums import (
    BasketPaymentStatus,
    BasketPaymentType,
    BasketStatus,
    PaymentMethodType,
)
from webapps.point_of_sale.models import Basket, BasketPayment


class BasketStatusTests(TestCase):
    def setUp(self):
        self.basket = baker.make(Basket, business_id=1)

    def test_empty_basket_status(self):
        self.assertEqual(self.basket.status, BasketStatus.PENDING)

    def test_single_successful_payment(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        self.assertEqual(self.basket.status, BasketStatus.SUCCESS)

    def test_single_pending_payment(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.PENDING,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.PENDING)

    def test_single_failed_payment(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.FAILED,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.FAILED)

    def test_single_canceled_payment(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.CANCELED,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.CANCELED)

    def test_action_required_payment(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.ACTION_REQUIRED,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.ACTION_REQUIRED)

    def test_multiple_successful_payments(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.SUCCESS)

    def test_mixed_payment_statuses_with_action_required(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.ACTION_REQUIRED,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.ACTION_REQUIRED)

    def test_mixed_payment_statuses_success_and_pending(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.PENDING,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.PENDING)

    def test_mixed_payment_statuses_success_and_failed(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.FAILED,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.PENDING)

    def test_all_failed_payments(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.FAILED,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.FAILED,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.FAILED)

    def test_all_canceled_payments(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.CANCELED,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.CANCELED,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.CANCELED)

    def test_pending_refund(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.REFUND,
            status=BasketPaymentStatus.PENDING,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        self.assertEqual(self.basket.status, BasketStatus.SENT_FOR_REFUND)

    def test_successful_full_refund(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.REFUND,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        self.assertEqual(self.basket.status, BasketStatus.REFUNDED)

    def test_successful_partial_refund(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.REFUND,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CASH,
        )
        self.assertEqual(self.basket.status, BasketStatus.REFUNDED)

    def test_successful_over_refund(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.REFUND,
            status=BasketPaymentStatus.SUCCESS,
            amount=1200,
            payment_method=PaymentMethodType.CASH,
        )
        self.assertEqual(self.basket.status, BasketStatus.REFUNDED)

    def test_chargeback_priority(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.CHARGEBACK)

    def test_chargeback_reversed_priority(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK_REVERSED,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.CHARGEBACK_REVERSED)

    def test_second_chargeback_highest_priority(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK_REVERSED,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.SECOND_CHARGEBACK,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.SECOND_CHARGEBACK)

    def test_failed_chargeback_no_priority(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK,
            status=BasketPaymentStatus.FAILED,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.SUCCESS)

    def test_no_payment_type_payments(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.REFUND,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CASH,
        )
        self.assertEqual(self.basket.status, BasketStatus.REFUNDED)

    def test_complex_scenario_with_multiple_payment_types(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CASH,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=500,
            payment_method=PaymentMethodType.CARD,
        )
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.REFUND,
            status=BasketPaymentStatus.SUCCESS,
            amount=300,
            payment_method=PaymentMethodType.CASH,
        )
        self.assertEqual(self.basket.status, BasketStatus.REFUNDED)

    def test_priority_order_verification(self):
        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.PAYMENT,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.SUCCESS)

        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(self.basket.status, BasketStatus.SUCCESS)  # because it was not refreshed
        self.basket.refresh_status()
        self.assertEqual(self.basket.status, BasketStatus.CHARGEBACK)

        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.CHARGEBACK_REVERSED,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(
            self.basket.status, BasketStatus.CHARGEBACK
        )  # because it was not refreshed
        self.basket.refresh_status()
        self.assertEqual(self.basket.status, BasketStatus.CHARGEBACK_REVERSED)

        baker.make(
            BasketPayment,
            basket=self.basket,
            type=BasketPaymentType.SECOND_CHARGEBACK,
            status=BasketPaymentStatus.SUCCESS,
            amount=1000,
            payment_method=PaymentMethodType.CARD,
        )
        self.assertEqual(
            self.basket.status, BasketStatus.CHARGEBACK_REVERSED
        )  # because it was not refreshed
        self.basket.refresh_status()
        self.assertEqual(self.basket.status, BasketStatus.SECOND_CHARGEBACK)
