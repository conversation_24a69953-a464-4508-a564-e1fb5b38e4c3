import pytest
from model_bakery import baker

from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.experiment.booking import SuggestiveReviewsExperiment
from lib.tests.utils import override_eppo_feature_flag
from webapps.booking.models import Appointment
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import bci_recipe, business_recipe
from webapps.pop_up_notification.models import ShortReviewNotification
from webapps.pop_up_notification.serializers import ShortReviewSerializer


class TestShortReviewSerializer:
    @pytest.mark.django_db
    def test_suggestive_reviews_experiment(self):
        business = business_recipe.make()
        bci = bci_recipe.make(business=business)
        appointment = create_appointment(
            {}, booked_for=bci, business=business, status=Appointment.STATUS.FINISHED
        )
        popup = baker.make(
            ShortReviewNotification,
            user_id=appointment.booked_for.user_id,
            business_id=appointment.business_id,
            subbooking=appointment.subbookings[0],
        )

        with override_eppo_feature_flag(
            {
                SuggestiveReviewsExperiment.flag_name: None,
            }
        ):
            data = ShortReviewSerializer(instance=popup).data
            assert 'in_experiment' not in data

        with override_eppo_feature_flag(
            {
                SuggestiveReviewsExperiment.flag_name: ExperimentVariants.VARIANT_A,
            }
        ):
            data = ShortReviewSerializer(instance=popup).data
            assert 'in_experiment' in data
            assert data['in_experiment'] is True
