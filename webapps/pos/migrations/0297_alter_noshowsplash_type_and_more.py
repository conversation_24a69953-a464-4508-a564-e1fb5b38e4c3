# Generated by Django 4.2.18 on 2025-06-09 11:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("pos", "0296_alter_noshowsplash_type_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="noshowsplash",
            name="type",
            field=models.CharField(
                choices=[
                    (
                        "SETTINGS_PROFILE_USE_TAP_TO_PAY",
                        "SETTINGS_PROFILE_USE_TAP_TO_PAY",
                    ),
                    ("SPLASH_USE_TAP_TO_PAY", "SPLASH_USE_TAP_TO_PAY"),
                    ("SPLASH_NO_SHOW_PROTECTION", "SPLASH_NO_SHOW_PROTECTION"),
                    ("SPLASH_NO_SHOWS", "SPLASH_NO_SHOWS"),
                    ("SPLASH_BLIK_FOR_FREE", "SPLASH_BLIK_FOR_FREE"),
                    ("SPLASH_REMINDER_TAP_TO_PAY", "SPLASH_REMINDER_TAP_TO_PAY"),
                    ("SPLASH_HIGHER_PREPAYMENT", "SPLASH_HIGHER_PREPAYMENT"),
                    ("SPLASH_TAP_TO_PAY_APPETITE", "SPLASH_TAP_TO_PAY_APPETITE"),
                    ("SPLASH_KEEP_PREPAYMENT", "SPLASH_KEEP_PREPAYMENT"),
                    ("SPLASH_PX_BP_ONBOARDING", "SPLASH_PX_BOOKSY_PAY_ONBOARDING"),
                    (
                        "SPLASH_BOOKSY_PAY_REFUNDS_ANNOUNCEMENT",
                        "SPLASH_BOOKSY_PAY_REFUNDS_ANNOUNCEMENT",
                    ),
                    (
                        "SPLASH_BOOST_PRE_SUSPENSION_WARNING",
                        "SPLASH_BOOST_PRE_SUSPENSION_WARNING",
                    ),
                    ("SPLASH_PREMIUM_HOURS", "SPLASH_PREMIUM_HOURS"),
                ],
                max_length=48,
            ),
        ),
        migrations.AlterField(
            model_name="postcheckoutsplash",
            name="splash_type",
            field=models.CharField(
                choices=[
                    (
                        "SETTINGS_PROFILE_USE_TAP_TO_PAY",
                        "SETTINGS_PROFILE_USE_TAP_TO_PAY",
                    ),
                    ("SPLASH_USE_TAP_TO_PAY", "SPLASH_USE_TAP_TO_PAY"),
                    ("SPLASH_NO_SHOW_PROTECTION", "SPLASH_NO_SHOW_PROTECTION"),
                    ("SPLASH_NO_SHOWS", "SPLASH_NO_SHOWS"),
                    ("SPLASH_BLIK_FOR_FREE", "SPLASH_BLIK_FOR_FREE"),
                    ("SPLASH_REMINDER_TAP_TO_PAY", "SPLASH_REMINDER_TAP_TO_PAY"),
                    ("SPLASH_HIGHER_PREPAYMENT", "SPLASH_HIGHER_PREPAYMENT"),
                    ("SPLASH_TAP_TO_PAY_APPETITE", "SPLASH_TAP_TO_PAY_APPETITE"),
                    ("SPLASH_KEEP_PREPAYMENT", "SPLASH_KEEP_PREPAYMENT"),
                    ("SPLASH_PX_BP_ONBOARDING", "SPLASH_PX_BOOKSY_PAY_ONBOARDING"),
                    (
                        "SPLASH_BOOKSY_PAY_REFUNDS_ANNOUNCEMENT",
                        "SPLASH_BOOKSY_PAY_REFUNDS_ANNOUNCEMENT",
                    ),
                    (
                        "SPLASH_BOOST_PRE_SUSPENSION_WARNING",
                        "SPLASH_BOOST_PRE_SUSPENSION_WARNING",
                    ),
                    ("SPLASH_PREMIUM_HOURS", "SPLASH_PREMIUM_HOURS"),
                ],
                max_length=48,
            ),
        ),
        migrations.AlterField(
            model_name="splash",
            name="type",
            field=models.CharField(
                choices=[
                    (
                        "SETTINGS_PROFILE_USE_TAP_TO_PAY",
                        "SETTINGS_PROFILE_USE_TAP_TO_PAY",
                    ),
                    ("SPLASH_USE_TAP_TO_PAY", "SPLASH_USE_TAP_TO_PAY"),
                    ("SPLASH_NO_SHOW_PROTECTION", "SPLASH_NO_SHOW_PROTECTION"),
                    ("SPLASH_NO_SHOWS", "SPLASH_NO_SHOWS"),
                    ("SPLASH_BLIK_FOR_FREE", "SPLASH_BLIK_FOR_FREE"),
                    ("SPLASH_REMINDER_TAP_TO_PAY", "SPLASH_REMINDER_TAP_TO_PAY"),
                    ("SPLASH_HIGHER_PREPAYMENT", "SPLASH_HIGHER_PREPAYMENT"),
                    ("SPLASH_TAP_TO_PAY_APPETITE", "SPLASH_TAP_TO_PAY_APPETITE"),
                    ("SPLASH_KEEP_PREPAYMENT", "SPLASH_KEEP_PREPAYMENT"),
                    ("SPLASH_PX_BP_ONBOARDING", "SPLASH_PX_BOOKSY_PAY_ONBOARDING"),
                    (
                        "SPLASH_BOOKSY_PAY_REFUNDS_ANNOUNCEMENT",
                        "SPLASH_BOOKSY_PAY_REFUNDS_ANNOUNCEMENT",
                    ),
                    (
                        "SPLASH_BOOST_PRE_SUSPENSION_WARNING",
                        "SPLASH_BOOST_PRE_SUSPENSION_WARNING",
                    ),
                    ("SPLASH_PREMIUM_HOURS", "SPLASH_PREMIUM_HOURS"),
                ],
                max_length=48,
            ),
        ),
    ]
