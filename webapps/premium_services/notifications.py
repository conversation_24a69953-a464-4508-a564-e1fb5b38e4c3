from django.utils.translation import gettext_lazy as _

from webapps.notification.base import (
    BaseNotification,
    PopupTemplate,
    PushTarget,
)
from webapps.notification.channels import (
    PopupChannel,
    PushChannel,
)
from webapps.notification.enums import (
    NotificationCategory,
    NotificationGroup,
    NotificationIcon,
    NotificationSize,
    NotificationTarget,
)
from webapps.notification.recipients import (
    Managers,
    SystemSender,
)
from webapps.premium_services.public import PeakHourServiceFactory


class PremiumHoursNotification(BaseNotification):
    category = NotificationCategory.PREMIUM_HOURS
    recipients = (Managers,)
    sender = SystemSender
    channels = [
        PushChannel,
        PopupChannel,
    ]
    popup_template = PopupTemplate(
        icon=NotificationIcon.SUGGESTION,
        group=NotificationGroup.MONETISATION,
        crucial=False,
        relevance=3,
        size=NotificationSize.NORMAL,
        messages=[
            _('New feature alert: Premium Hours'),
            _('Charge more for high-demand times and non-standard workdays.'),
        ],
    )
    push_template = _('Charge more for high-demand times and non-standard workdays.')

    def __init__(self, business):
        super().__init__(business)
        self.business = business

    @property
    def identity(self):
        return f'{self.notif_type},{self.business.id}'

    def get_target(self):
        return PushTarget(
            type=NotificationTarget.PREMIUM_HOURS.value,
            title=_('New feature alert: Premium Hours'),
        )

    def should_skip_with_plea(self):
        # pylint: disable=too-many-return-statements
        skip, reason = super().should_skip_with_plea()
        if skip:
            return skip, reason

        if not self.business.active:
            return True, 'Business is not active'

        if self.schedule_record.exists():
            return True, 'Already sent'

        peak_hour_service = PeakHourServiceFactory.get_service()
        if peak_hour_service.get_active_services(self.business.id):
            return True, 'Already using premium hours'

        return False, None
