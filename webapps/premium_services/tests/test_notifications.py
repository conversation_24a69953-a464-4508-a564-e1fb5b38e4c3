from datetime import time
from decimal import Decimal

from django.test import TestCase
from django.utils import translation
from mock import patch
from model_bakery import baker

from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import (
    business_recipe,
    business_user,
    service_variant_recipe,
    staffer_recipe,
)
from webapps.business.models import Resource
from webapps.notification.channels import PopupChannel, PushChannel
from webapps.notification.enums import NotificationTarget
from webapps.notification.models import Reciever, UserNotification
from webapps.premium_services.notifications import PremiumHoursNotification
from webapps.premium_services.public import DayOfWeek, PeakHour, PeakHourServiceFactory, Service
from webapps.user.models import UserProfile


class PremiumHoursNotificationTest(TestCase):

    @classmethod
    def setUpTestData(cls):
        cls.business = business_recipe.make(
            owner=business_user.make(email='<EMAIL>', cell_phone='+***********'),
        )
        cls.owner = staffer_recipe.make(
            business=cls.business,
            staff_user=cls.business.owner,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        )
        cls.manager = staffer_recipe.make(
            business=cls.business,
            staff_user=business_user.make(email='<EMAIL>', cell_phone='+***********'),
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )
        cls.create_receiver(cls.owner)
        cls.create_receiver(cls.manager)

    @staticmethod
    def create_receiver(staffer: Resource) -> Reciever:
        assert staffer.staff_user is not None

        booking_source, _ = BookingSources.objects.get_or_create(
            name='Android',
            app_type=BookingSources.BUSINESS_APP,
            api_key='xx312',
        )
        user_profile, _ = UserProfile.objects.update_or_create(
            user=staffer.staff_user,
            profile_type=UserProfile.Type.BUSINESS,
            defaults={
                'source': booking_source,
            },
        )
        user_notification = baker.make(
            UserNotification,
            profile=user_profile,
            type=UserNotification.PUSH_NOTIFICATION,
        )

        return baker.make(
            Reciever,
            identifier='xxx',
            business=staffer.business,
            device='android',
            customer_notifications=user_notification,
        )

    def enable_premium_hours(self):
        variant = service_variant_recipe.make(service__business=self.business)

        peak_hour_service = PeakHourServiceFactory.get_service()
        peak_hour_service.enable(
            self.business.id,
            [
                PeakHour(
                    business_id=self.business.id,
                    day_of_week=DayOfWeek.SATURDAY,
                    service_variants=[
                        Service(
                            elevation_rate=Decimal(20),
                            hour_from=time(10, 0),
                            hour_till=time(14, 0),
                            service_variant_id=variant.id,
                        ),
                    ],
                ),
            ],
        )

    def test_skip_with_plea__premium_hours_not_used(self):
        notification = PremiumHoursNotification(self.business)
        self.assertFalse(notification.should_skip_with_plea()[0])

    def test_skip_with_plea__premium_hours_used(self):
        self.enable_premium_hours()

        notification = PremiumHoursNotification(self.business)
        self.assertTrue(notification.should_skip_with_plea()[0])

    @patch('webapps.notification.channels.send_push_notification')
    def test_skip_with_plea__already_sent(self, push_mock):
        notification = PremiumHoursNotification(self.business)
        notification.send()

        notification = PremiumHoursNotification(self.business)
        self.assertTrue(notification.should_skip_with_plea()[0])

    @translation.override(None)
    @patch('webapps.notification.channels.send_push_notification')
    def test_send(self, push_mock):
        notification = PremiumHoursNotification(self.business)

        self.assertEqual(notification.get_target().type, NotificationTarget.PREMIUM_HOURS.value)
        self.assertFalse(notification.should_skip_with_plea()[0])

        push_body = PushChannel(notification).get_content()
        self.assertEqual(notification.get_target().title, 'New feature alert: Premium Hours')
        self.assertEqual(
            push_body,
            'Charge more for high-demand times and non-standard workdays.',
        )

        notification_document = PopupChannel(notification).get_content()
        self.assertListEqual(
            list(notification_document.content.messages),
            [
                'New feature alert: Premium Hours',
                'Charge more for high-demand times and non-standard workdays.',
            ],
        )

        notification.send()
        notification.schedule_record.assert_success()

        push_mock.assert_called_once()
