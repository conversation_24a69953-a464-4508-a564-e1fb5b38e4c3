from django.conf import settings
from django.core.exceptions import ImproperlyConfigured, ValidationError
from django.db import models, transaction
from django.utils.translation import gettext_lazy as _
from dirtyfields import DirtyFieldsMixin
from jwcrypto import jwk
from jwcrypto.common import base64url_encode
from oauth2_provider.models import (
    AbstractApplication,
    AbstractGrant,
    AbstractAccessToken,
    AbstractRefreshToken,
    AbstractIDToken,
)

from lib.models import ArchiveModel
from lib.serializers import safe_get
from lib.tools import tznow
from webapps.business.models.category import BusinessCategory
from webapps.public_partners.consts import STATUS_TRANSITIONS
from webapps.public_partners.enum import (
    OAuth2InstallationStatusEnum,
    CallToActionTargetEnum,
    ApplicationOwnership,
)


class OAuth2ApplicationCategory(ArchiveModel):
    class Meta(AbstractApplication.Meta):
        verbose_name = "OAuth2 application category"
        verbose_name_plural = "OAuth2 application categories"

    name = models.CharField(
        _('name'),
        max_length=150,
        unique=True,
        db_index=True,
    )
    description = models.TextField(
        null=True,
        blank=True,
        max_length=1500,
    )
    is_active = models.BooleanField(
        _('active'),
        default=True,
    )

    def __str__(self):
        return self.name


class OAuth2Application(AbstractApplication):
    class Meta(AbstractApplication.Meta):
        swappable = "OAUTH2_PROVIDER_APPLICATION_MODEL"
        verbose_name = "OAuth2 application"

    GRANT_JWT_BEARER = 'urn:ietf:params:oauth:grant-type:jwt-bearer'
    GRANT_TYPES = (
        (AbstractApplication.GRANT_AUTHORIZATION_CODE, _("Authorization code")),
        (AbstractApplication.GRANT_IMPLICIT, _("Implicit")),
        (AbstractApplication.GRANT_PASSWORD, _("Resource owner password-based")),
        (AbstractApplication.GRANT_CLIENT_CREDENTIALS, _("Client credentials")),
        (AbstractApplication.GRANT_OPENID_HYBRID, _("OpenID connect hybrid")),
        (GRANT_JWT_BEARER, _("JWT Assertion")),
    )

    authorization_grant_type = models.CharField(
        max_length=150,
        choices=GRANT_TYPES,
    )
    jwk_public_key = models.JSONField(
        null=True,
        blank=True,
    )
    category = models.ForeignKey(
        OAuth2ApplicationCategory,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    business_category = models.ForeignKey(
        'business.BusinessCategory',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    is_catalogable = models.BooleanField(
        _('catalogable'),
        default=True,
        help_text='Visible in catalog',
    )
    is_active = models.BooleanField(
        _('active'),
        default=True,
        help_text='Active application',
    )
    throttling_limit = models.IntegerField(
        default=200,
        null=False,
        help_text='Requests per minute (default value is 200)',
    )
    description = models.TextField(
        null=True,
        blank=True,
        max_length=1500,
    )
    company_name = models.CharField(
        null=True,
        blank=True,
        max_length=200,
    )
    company_url = models.URLField(
        null=True,
        blank=True,
    )
    support_url = models.URLField(
        null=True,
        blank=True,
    )
    support_email = models.EmailField(
        null=True,
        blank=True,
        max_length=255,
    )
    privacy_policy_url = models.URLField(
        null=True,
        blank=True,
    )

    def clean(self):
        super().clean()

        if self.authorization_grant_type == self.GRANT_JWT_BEARER and not self.jwk_public_key:
            raise ValidationError(
                _(f"You must set jwk_public_key to use {self.GRANT_JWT_BEARER} grant type")
            )

    def allows_grant_type(self, *grant_types):
        if self.client_id in (settings.FIZJOREJESTRACJA_APPLICATION_CLIENT_ID,):
            return bool(
                set([self.authorization_grant_type, self.GRANT_PASSWORD]) & set(grant_types)
            )
        return super().allows_grant_type(*grant_types)

    def is_usable(self, request):
        return self.is_active

    def is_client_credential(self):
        return self.authorization_grant_type == self.GRANT_CLIENT_CREDENTIALS

    @property
    def notification_email(self):
        return (
            self.support_email
            or safe_get(self.user, 'email')
            or settings.WEBHOOK_FAILURE_NOTIFICATION_DEFAULT_RECIPIENT
        )

    @property
    def jwk_key(self):
        if self.algorithm == AbstractApplication.RS256_ALGORITHM:
            private_key = settings.OAUTH2_PROVIDER.get('OIDC_RSA_PRIVATE_KEY')
            if not private_key:
                raise ImproperlyConfigured("You must set OIDC_RSA_PRIVATE_KEY to use RSA algorithm")
            return private_key
        elif self.algorithm == AbstractApplication.HS256_ALGORITHM:
            return jwk.JWK(kty="oct", k=base64url_encode(self.client_secret))
        else:
            raise ImproperlyConfigured("This application does not support signed tokens")


class OAuth2Grant(AbstractGrant):
    class Meta(AbstractGrant.Meta):
        swappable = "OAUTH2_PROVIDER_GRANT_MODEL"
        verbose_name = "OAuth2 grant"

    application = models.ForeignKey(
        'public_partners.OAuth2Application',
        on_delete=models.CASCADE,
    )

    superuser = models.TextField(blank=True, null=True)

    def __str__(self):
        return f'{self.id}'


class OAuth2AccessToken(AbstractAccessToken):
    class Meta(AbstractAccessToken.Meta):
        swappable = "OAUTH2_PROVIDER_ACCESS_TOKEN_MODEL"
        verbose_name = "OAuth2 access token"

    source_refresh_token = models.OneToOneField(
        # unique=True implied by the OneToOneField
        'public_partners.OAuth2RefreshToken',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="refreshed_access_token",
    )
    id_token = models.OneToOneField(
        'public_partners.OAuth2IDToken',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name="access_token",
    )
    application = models.ForeignKey(
        'public_partners.OAuth2Application',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )

    def __str__(self):
        return f'{self.id}'


class OAuth2RefreshToken(AbstractRefreshToken):
    class Meta(AbstractRefreshToken.Meta):
        swappable = "OAUTH2_PROVIDER_REFRESH_TOKEN_MODEL"
        verbose_name = "OAuth2 refresh token"

    application = models.ForeignKey(
        'public_partners.OAuth2Application',
        on_delete=models.CASCADE,
    )
    access_token = models.OneToOneField(
        'public_partners.OAuth2AccessToken',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name="refresh_token",
    )

    def __str__(self):
        return f'{self.id}'


class OAuth2IDToken(AbstractIDToken):
    class Meta(AbstractIDToken.Meta):
        swappable = "OAUTH2_PROVIDER_ID_TOKEN_MODEL"
        verbose_name = "OAuth2 ID token"

    application = models.ForeignKey(
        'public_partners.OAuth2Application',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
    )


class OAuth2ApplicationConfig(DirtyFieldsMixin, ArchiveModel):
    class Meta(AbstractApplication.Meta):
        verbose_name = "OAuth2 application config"

    CALL_TO_ACTION_TARGETS = CallToActionTargetEnum
    OWNERSHIP = ApplicationOwnership

    application = models.OneToOneField(
        'public_partners.OAuth2Application',
        on_delete=models.CASCADE,
        db_index=True,
        related_name='config',
    )
    main_iframe_url = models.URLField(
        null=True,
        blank=True,
    )
    call_to_actions = models.JSONField(default=dict)
    call_to_action_target = models.CharField(
        choices=CALL_TO_ACTION_TARGETS.choices(),
        null=True,
        blank=True,
        max_length=10,
    )
    ownership = models.CharField(
        default=OWNERSHIP.USER,
        choices=OWNERSHIP.choices(),
        max_length=20,
    )


class OAuth2Installation(DirtyFieldsMixin, ArchiveModel):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                name='unique_application_business_user_not_deleted',
                fields=['application', 'business', 'user'],
                condition=models.Q(deleted__isnull=True),
            ),
            models.UniqueConstraint(
                name='unique_application_business_not_parent_and_deleted',
                fields=['application', 'business'],
                condition=models.Q(parent__isnull=True, deleted__isnull=True),
            ),
        ]
        verbose_name = "OAuth2 installation"

    STATUSES = OAuth2InstallationStatusEnum
    ACTIVE_STATUSES = (
        STATUSES.ACTIVE,
        STATUSES.PAID,
        STATUSES.SUSPENDED,
        STATUSES.PENDING_CANCELLATION,
        STATUSES.OVERDUE_BLOCKED,
        STATUSES.CHURNED,
    )

    parent = models.ForeignKey(
        'public_partners.OAuth2Installation',
        related_name='sub_installations',
        on_delete=models.CASCADE,
        db_index=True,
        null=True,
    )
    application = models.ForeignKey(
        'public_partners.OAuth2Application',
        on_delete=models.CASCADE,
        db_index=True,
        related_name='installations',
    )
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.CASCADE,
        db_index=True,
        related_name='partner_apps',
        null=True,
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        db_index=True,
        related_name='+',
    )
    status = models.CharField(
        choices=STATUSES.choices(),
        null=False,
        blank=False,
        default=STATUSES.ACTIVE,
        max_length=10,
    )
    status_changed = models.DateTimeField(
        null=True,
    )

    @property
    def is_booksy_med(self):
        return (
            self.application
            and self.application.client_id == settings.FIZJOREJESTRACJA_APPLICATION_CLIENT_ID
        )

    @transaction.atomic
    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        dirty_status = self.get_dirty_fields(verbose=True).get('status')
        status_changed = (
            dirty_status and dirty_status.get('saved', None) and dirty_status.get('current', None)
        )
        if status_changed:
            self.status_changed = tznow()
        super().save(
            force_insert=False,
            force_update=False,
            using=None,
            update_fields=None,
            **kwargs,
        )

        if status_changed and (
            sub_installations_query := self.sub_installations.filter(deleted__isnull=True)
        ):

            if self.is_booksy_med:
                sub_installations_query = sub_installations_query.filter(
                    status=dirty_status.get('saved')
                )
            else:
                sub_installations_query = sub_installations_query.filter(
                    status__in=STATUS_TRANSITIONS['default'].get(self.status)
                )
            sub_installations_query.update(
                status=self.status,
                status_changed=self.status_changed,
                updated=self.updated,
            )

    @transaction.atomic
    def soft_delete(self):
        super().soft_delete()
        self.sub_installations.filter(deleted__isnull=True).update(
            updated=self.updated,
            deleted=self.deleted,
        )

    def is_active(self):
        return self.status in self.ACTIVE_STATUSES

    def reactivate(self):
        if self.status == self.STATUSES.DISABLED:
            self.status = self.STATUSES.ACTIVE
            self.save()


class OAuth2ApplicationCategoryLink(DirtyFieldsMixin, ArchiveModel):
    class Meta:
        verbose_name = "Links Business and OAuth2App cat"

    business_category = models.ForeignKey(
        BusinessCategory, on_delete=models.CASCADE, related_name="business_category_link"
    )
    application_category = models.ForeignKey(
        OAuth2ApplicationCategory, on_delete=models.CASCADE, related_name="oauth2_category_link"
    )

    def __str__(self):
        business_name = (
            self.business_category.internal_name if self.business_category else "Unknown"
        )
        app_name = self.application_category.name if self.application_category else "Unknown"
        return f"{business_name} → {app_name}"
