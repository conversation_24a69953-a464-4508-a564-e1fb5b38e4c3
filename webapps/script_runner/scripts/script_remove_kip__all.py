from django.conf import settings

from country_config import Country
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import PaymentType
from webapps.script_runner.runners import DBScriptRunner


class Script(DBScriptRunner):

    def run(self):
        if settings.API_COUNTRY == Country.US:
            return

        deleted_count = PaymentType.objects.filter(code=PaymentTypeEnum.KEYED_IN_PAYMENT).delete()

        print(f'DELETED KIP COUNT: {deleted_count}')
