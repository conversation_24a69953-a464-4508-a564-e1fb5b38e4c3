import logging

from webapps.business.models import Business
from webapps.premium_services.notifications import PremiumHoursNotification
from webapps.script_runner.runners import DBScriptRunner

logger = logging.getLogger('booksy.script_runner')

BUSINESS_IDS = [
    243300,
    274782,
    241278,
    241180,
    111335,
    104371,
    99398,
    121556,
    162764,
    38243,
    250476,
    156666,
    260387,
    236749,
    169961,
    167513,
]


class Script(DBScriptRunner):
    atomic = False

    def run(self):
        logger.warning('START script_send_premium_hours_notification__pl')
        businesses = Business.objects.filter(
            id__in=BUSINESS_IDS,
            active=True,
        )

        for business in businesses.iterator():
            PremiumHoursNotification(business).send()

        logger.warning('END script_send_premium_hours_notification__pl')
