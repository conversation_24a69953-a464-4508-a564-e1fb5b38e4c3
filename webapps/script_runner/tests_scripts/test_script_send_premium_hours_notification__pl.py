from unittest.mock import patch

import pytest

from webapps.business.baker_recipes import business_recipe
from webapps.script_runner.scripts.script_send_premium_hours_notification__pl import (
    BUSINESS_IDS,
    Script,
)


@pytest.mark.django_db
def test_script_basic():
    business_ids = BUSINESS_IDS[:3]

    for business_id in business_ids:
        business_recipe.make(id=business_id)

    with patch(
        'webapps.premium_services.notifications.PremiumHoursNotification.send'
    ) as schedule_mock:
        Script().run()
        assert schedule_mock.call_count == len(business_ids)
