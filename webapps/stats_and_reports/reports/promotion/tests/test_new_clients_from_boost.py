from datetime import date, timedelta
from decimal import Decimal
from typing import List, Union

from django.test import TestCase
from freezegun import freeze_time

from webapps.booking.tests.utils import create_appointment
from webapps.boost.baker_recipes import boosted_business_recipe
from webapps.boost.tests.utils import (
    as_datetime,
    create_boost_finished_visit,
    create_future_boost_visit,
    create_returning_visit_of_boost_client,
    enable_boost_taxes,
)
from webapps.business.baker_recipes import service_recipe, service_variant_recipe
from webapps.stats_and_reports.reports.promotion.new_clients_from_boost import (
    CompletedAppointmentsFromBoostSection,
    FutureAppointmentsFromBoostSection,
    NewClientsFromBoostReport,
    NewClientsFromBoostSummary,
    NewClientsFromBoostSummarySection,
)
from webapps.stats_and_reports.reports.time_data import TimeDataScope, TimeScopeType
from webapps.stats_and_reports.serializers import ReportSerializer


@freeze_time(as_datetime(2022, 1, 7))
class NewClientsFromBoostTests(TestCase):
    @classmethod
    def setUpTestData(cls):
        with freeze_time(as_datetime(2021, 11, 15)):  # boost for this business started earlier
            cls.business = boosted_business_recipe.make()

        cls.service_variant = service_variant_recipe.make(
            service=service_recipe.make(business=cls.business),
            price=Decimal('20.00'),
        )

        cls.date_from = date(2021, 12, 1)
        cls.date_till = date(2021, 12, 31)

        cls.time_scope = TimeDataScope(
            cls.business,
            'en',
            date_from=cls.date_from,
            date_till=cls.date_till,
            time_span=TimeScopeType.MONTH,
        )

    def _create_appointment(self, year, month, day):
        booked_from = as_datetime(year, month, day)
        return create_appointment(
            subbookings=[
                {
                    'booked_from': booked_from,
                    'booked_till': booked_from + timedelta(hours=1),
                    'service_variant': self.service_variant,
                },
            ],
            business=self.business,
        )

    def _create_finished_boost_appointment(self, year, month, day):
        booked_from = as_datetime(year, month, day)
        return create_boost_finished_visit(
            business=self.business,
            subbookings=[{'booked_from': booked_from, 'service_variant': self.service_variant}],
        )

    def _create_returning_appointment_of_boost_client(self, bci, year, month, day):
        booked_from = as_datetime(year, month, day)
        return create_returning_visit_of_boost_client(
            business=self.business,
            bci=bci,
            subbookings=[{'booked_from': booked_from, 'service_variant': self.service_variant}],
        )

    @staticmethod
    def _expected_rows_summary_section(
        expected_values: List[Union[int, Decimal]],
    ) -> List[NewClientsFromBoostSummary.Row]:
        return [
            NewClientsFromBoostSummary.Row(
                entry_label=getattr(NewClientsFromBoostSummarySection, f'R{idx+1}_LABEL'),
                entry_value=expected_values[idx],
            )
            for idx in range(5)
        ]

    def setUp(self):
        super().setUp()
        self.summary_section = NewClientsFromBoostSummarySection(self.time_scope)
        self.completed_boost_section = CompletedAppointmentsFromBoostSection(self.time_scope)
        self.future_boost_section = FutureAppointmentsFromBoostSection(self.time_scope)
        self.report = NewClientsFromBoostReport(self.time_scope)

    def _setup_appointments(self):
        self._create_appointment(2021, 12, 6)  # non-boost visit does not count
        appt = self._create_finished_boost_appointment(2021, 12, 8)
        self._create_finished_boost_appointment(2021, 12, 10)
        self._create_returning_appointment_of_boost_client(appt.booked_for, 2021, 12, 12)
        # ^ returning does not count

        # next two are outside the scope (but not in the future)
        self._create_finished_boost_appointment(2022, 1, 1)
        self._create_appointment(2022, 1, 2)

        # future booking
        create_future_boost_visit(business=self.business)

    def test_empty_table(self):
        summary = self.summary_section.get_data(False)
        assert summary.rows == self._expected_rows_summary_section(
            [0, 0, Decimal(0), Decimal(0), Decimal(0)]
        )

    def test_filled_table(self):
        self._setup_appointments()

        summary = self.summary_section.get_data(False)
        second_section = self.completed_boost_section.get_data(False)
        third_section = self.future_boost_section.get_data(False)

        assert summary.rows == self._expected_rows_summary_section(
            [
                2,  # from Dec 8th and Dec 10th
                2,
                Decimal('40.00'),  # 2 * 20.00
                Decimal('4.00'),  # 2 * 2.00 (tax is 0)
                Decimal('36.00'),  # values above subtracted
            ]
        )
        assert len(second_section.rows) == 2
        assert len(third_section.rows) == 1

    @enable_boost_taxes()
    def test_filled_table_taxed(self):
        self._setup_appointments()

        summary = self.summary_section.get_data(False)
        second_section = self.completed_boost_section.get_data(False)
        third_section = self.future_boost_section.get_data(False)

        assert summary.rows == self._expected_rows_summary_section(
            [
                2,  # from Dec 8th and Dec 10th
                2,
                Decimal('40.00'),  # 2 * 20.00
                Decimal('4.92'),  # 2 * 2.46 (tax is 23%)
                Decimal('35.08'),  # values above subtracted
            ]
        )
        assert len(second_section.rows) == 2
        assert len(third_section.rows) == 1

    def test_serializer(self):
        serializer = ReportSerializer(self.report)
        assert serializer.data
        section_data = serializer.data['sections'][0]
        assert section_data['key'] == 'new_clients_from_boost_summary_section'
