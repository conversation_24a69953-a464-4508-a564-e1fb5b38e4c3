from abc import ABC, abstractmethod
from dataclasses import dataclass

from webapps.stats_and_reports.v2.domain.types import (
    Business,
    Staffer,
)
from webapps.stats_and_reports.v2.domain.periods import Period
from webapps.stats_and_reports.v2.domain.reports.staffer_stats import StafferStats


@dataclass(kw_only=True, frozen=True)
class StafferReportQuery:
    name: str
    business: Business
    staffer: Staffer
    period: Period

    @property
    def identity(self) -> str:
        business = ["business", str(self.business.identity)]
        staffer = ["staffer", str(self.staffer.identity)]
        return "__".join(
            [
                "reports",
                *business,
                *staffer,
                self.period.identity,
                self.name,
                self.period.name.value,
            ]
        )


class StafferStatsRepository(ABC):
    @abstractmethod
    def get(self, query: StafferReportQuery) -> StafferStats | None: ...
