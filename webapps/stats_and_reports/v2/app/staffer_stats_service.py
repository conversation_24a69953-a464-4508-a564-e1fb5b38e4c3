from datetime import date, datetime, timezone
from typing import Never, assert_never, cast

from webapps.business.ports.access_control import AccessControlPort

from webapps.stats_and_reports.v2.domain import periods
from webapps.stats_and_reports.v2.domain.period_type import PeriodType
from webapps.stats_and_reports.v2.domain.range_policy import SinceFixedDateTillToday
from webapps.stats_and_reports.v2.domain.types import Business, Staffer
from webapps.stats_and_reports.v2.domain.reports.staffer_stats import StafferStats

from webapps.stats_and_reports.v2.app.staffer_stats_repository import (
    StafferReportQuery,
    StafferStatsRepository,
)
from webapps.stats_and_reports.v2.app.errors import (
    ReportNotFoundError,
    StafferDoesNotExistError,
)

_oldest_report_date = date(2025, 1, 1)


class StafferStatsService:
    def __init__(
        self,
        staffer_stats_repository: StafferStatsRepository,
        access_control_port: AccessControlPort,
    ):
        self._staffer_stats_repository: StafferStatsRepository = staffer_stats_repository
        self._access_control_port: AccessControlPort = access_control_port

    def _validate_staffer(self, business_id: int, staffer_id: int):
        if not self._access_control_port.user_is_business_staff(
            business_id=business_id, staffer_id=staffer_id
        ):
            raise StafferDoesNotExistError()

    @staticmethod
    def _str_to_date(period_value: str, expected_format: str) -> date:
        return datetime.strptime(period_value, expected_format).date()

    def create_period(
        self,
        period_type: PeriodType,
        period_value: str = "",
    ) -> periods.Period:
        # TODO PET-1914 'since' needs to be dynamic
        range_policy = SinceFixedDateTillToday(since=_oldest_report_date)
        now = datetime.now(timezone.utc)
        match period_type:
            case PeriodType.DAILY:
                period_date = (
                    self._str_to_date(period_value, "%Y-%m-%d") if period_value else now.date()
                )

            case PeriodType.WEEKLY:
                period_date = (
                    self._str_to_date(period_value + "-0", "%Y-%U-%w")
                    if period_value
                    else datetime.strptime(now.strftime("%Y-%U-0"), "%Y-%U-%w").date()
                )

            case PeriodType.MONTHLY:
                period_date = (
                    self._str_to_date(period_value, "%Y-%m")
                    if period_value
                    else now.date().replace(day=1)
                )

            case PeriodType.YEARLY:
                period_date = (
                    self._str_to_date(period_value, "%Y")
                    if period_value
                    else now.date().replace(month=1, day=1)
                )

            case _:
                assert_never(cast(Never, period_type))

        return periods.create_period(
            period_type,
            period_date,
            range_policy=range_policy,
        )

    def get_report(
        self,
        business_id: int,
        staffer_id: int,
        period: periods.Period,
    ) -> StafferStats:
        self._validate_staffer(business_id=business_id, staffer_id=staffer_id)

        staffer_stats = self._staffer_stats_repository.get(
            StafferReportQuery(
                name="staffer_stats",
                business=Business(identity=business_id),
                staffer=Staffer(identity=staffer_id),
                period=period,
            )
        )
        if staffer_stats is None:
            raise ReportNotFoundError()
        return staffer_stats
