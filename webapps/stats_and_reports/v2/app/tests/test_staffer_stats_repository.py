import pytest

from webapps.stats_and_reports.v2.app.staffer_stats_repository import StafferReportQuery
from webapps.stats_and_reports.v2.domain.period_type import PeriodType
from webapps.stats_and_reports.v2.domain.periods import Period
from webapps.stats_and_reports.v2.domain.types import (
    Business,
    Staffer,
)


class TestStafferReportQuery:
    class FakePeriod(Period):
        def __init__(self, period_type: PeriodType):
            self._period_type = period_type

        @property
        def identity(self) -> str:
            return "periodid"

        @property
        def name(self) -> PeriodType:
            return self._period_type

        @property
        def value(self) -> str:
            raise NotImplementedError

        @property
        def next(self) -> Period | None:
            raise NotImplementedError

        @property
        def previous(self) -> Period | None:
            raise NotImplementedError

    @pytest.mark.parametrize(
        "report,identity",
        [
            (
                StafferReportQuery(
                    name="reportname",
                    business=Business(identity=123),
                    staffer=Staffer(identity=456),
                    period=FakePeriod(PeriodType.DAILY),
                ),
                "reports__business__123__staffer__456__periodid__reportname__daily",
            ),
            (
                StafferReportQuery(
                    name="name-with-dashes",
                    business=Business(identity=123),
                    staffer=Staffer(identity=456),
                    period=FakePeriod(PeriodType.WEEKLY),
                ),
                "reports__business__123__staffer__456__periodid__name-with-dashes__weekly",
            ),
            (
                StafferReportQuery(
                    name="name_with_underscores",
                    business=Business(identity=123),
                    staffer=Staffer(identity=456),
                    period=FakePeriod(PeriodType.MONTHLY),
                ),
                "reports__business__123__staffer__456__periodid__name_with_underscores__monthly",
            ),
            (
                StafferReportQuery(
                    name="",
                    business=Business(identity=123),
                    staffer=Staffer(identity=456),
                    period=FakePeriod(PeriodType.YEARLY),
                ),
                "reports__business__123__staffer__456__periodid____yearly",
            ),
        ],
    )
    def test_identity(self, report: StafferReportQuery, identity: str):
        assert report.identity == identity
