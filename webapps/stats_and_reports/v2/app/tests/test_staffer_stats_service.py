from datetime import date, datetime, timezone
from unittest.mock import AN<PERSON>, Mock, patch

import pytest
from freezegun import freeze_time

from webapps.business.ports.access_control import AccessControlPort

from webapps.stats_and_reports.v2.app.errors import StafferDoesNotExistError
from webapps.stats_and_reports.v2.app.staffer_stats_repository import (
    StafferReportQuery,
    StafferStatsRepository,
)
from webapps.stats_and_reports.v2.app.staffer_stats_service import StafferStatsService
from webapps.stats_and_reports.v2.domain.period_type import PeriodType
from webapps.stats_and_reports.v2.domain.periods import Period
from webapps.stats_and_reports.v2.domain.types import (
    Business,
    Staffer,
)


class TestStafferStatsService:
    @pytest.fixture
    @staticmethod
    def mock_staffer_stats_repo() -> Mock:
        return Mock(spec_set=StafferStatsRepository)

    @pytest.fixture
    @staticmethod
    def mock_access_control_port() -> Mock:
        return Mock(spec_set=AccessControlPort)

    @pytest.fixture
    @staticmethod
    def mock_period() -> Mock:
        return Mock(spec_set=Period)

    @pytest.fixture
    @staticmethod
    def sut(
        mock_staffer_stats_repo: Mock,
        mock_access_control_port: Mock,
    ) -> StafferStatsService:
        return StafferStatsService(
            staffer_stats_repository=mock_staffer_stats_repo,
            access_control_port=mock_access_control_port,
        )

    def test_when_user_is_business_staffer_then_gets_data_from_repo(
        self,
        mock_staffer_stats_repo: Mock,
        mock_access_control_port: Mock,
        mock_period: Mock,
        sut: StafferStatsService,
        business_id: int = 1,
        staffer_id: int = 100,
    ):
        mock_access_control_port.user_is_business_staff.return_value = True
        sut.get_report(
            business_id=business_id,
            staffer_id=staffer_id,
            period=mock_period,
        )
        mock_staffer_stats_repo.get.assert_called_once_with(
            StafferReportQuery(
                name="staffer_stats",
                business=ANY,
                staffer=ANY,
                period=ANY,
            )
        )

    def test_when_user_is_not_business_staffer_then_raises(
        self,
        mock_access_control_port: Mock,
        mock_period: Mock,
        sut: StafferStatsService,
        business_id: int = 1,
        staffer_id: int = 100,
    ):
        mock_access_control_port.user_is_business_staff.return_value = False
        with pytest.raises(StafferDoesNotExistError):
            sut.get_report(
                business_id=business_id,
                staffer_id=staffer_id,
                period=mock_period,
            )

    def test_gets_report_for_proper_staffer(
        self,
        mock_staffer_stats_repo,
        sut: StafferStatsService,
        business_id: int = 1,
        staffer_id: int = 100,
    ):
        period_fake = Mock(spec_set=Period)
        sut.get_report(business_id, staffer_id, period_fake)
        mock_staffer_stats_repo.get.assert_called_once_with(
            StafferReportQuery(
                name="staffer_stats",
                business=Business(identity=business_id),
                staffer=Staffer(identity=staffer_id),
                period=ANY,
            )
        )

    def test_gets_report_for_proper_period(
        self,
        mock_staffer_stats_repo: Mock,
        sut: StafferStatsService,
        business_id: int = 1,
        staffer_id: int = 100,
    ):
        period_fake = Mock(spec_set=Period)
        sut.get_report(business_id, staffer_id, period_fake)
        mock_staffer_stats_repo.get.assert_called_once_with(
            StafferReportQuery(
                name="staffer_stats",
                business=ANY,
                staffer=ANY,
                period=period_fake,
            )
        )

    @freeze_time(datetime(2025, 5, 30, 12, 44, tzinfo=timezone.utc))
    @pytest.mark.parametrize(
        "period_type,identity",
        [
            (PeriodType.DAILY, "2025__05__30"),
            (PeriodType.WEEKLY, "2025__21"),
            (PeriodType.MONTHLY, "2025__05"),
            (PeriodType.YEARLY, "2025"),
        ],
    )
    def test_creates_period(
        self,
        sut: StafferStatsService,
        period_type: PeriodType,
        identity: str,
    ):
        period = sut.create_period(period_type)
        assert (period.name, period.identity) == (period_type, identity)

    @freeze_time(datetime(2025, 5, 30, 12, 44, tzinfo=timezone.utc))
    @pytest.mark.parametrize(
        "period_type,period_value,identity",
        [
            (PeriodType.DAILY, "2025-05-30", "2025__05__30"),
            (PeriodType.WEEKLY, "2025-21", "2025__21"),
            (PeriodType.MONTHLY, "2025-05", "2025__05"),
            (PeriodType.YEARLY, "2025", "2025"),
        ],
    )
    def test_creates_period_with_provided_period_value(
        self,
        sut: StafferStatsService,
        period_type: PeriodType,
        period_value: str,
        identity: str,
    ):
        period = sut.create_period(period_type, period_value)
        assert (period.name, period.identity) == (period_type, identity)

    @freeze_time(datetime(2025, 5, 30, 12, 44, tzinfo=timezone.utc))
    @pytest.mark.parametrize(
        "period_type,period_value",
        [
            (PeriodType.DAILY, "2025--05"),
            (PeriodType.DAILY, "2025--5"),
            (PeriodType.DAILY, "2025-05"),
            (PeriodType.DAILY, "2025"),
            (PeriodType.WEEKLY, "2025-021"),
            (PeriodType.WEEKLY, "2025-05-21"),
            (PeriodType.WEEKLY, "2025"),
            (PeriodType.MONTHLY, "2025--05"),
            (PeriodType.MONTHLY, "2025--5"),
            (PeriodType.MONTHLY, "2025-05-21"),
            (PeriodType.MONTHLY, "2025"),
            (PeriodType.YEARLY, "2025-21"),
            (PeriodType.YEARLY, "2025-"),
            (PeriodType.YEARLY, "2025-05-21"),
            (PeriodType.YEARLY, "20252"),
        ],
    )
    def test_creates_period_with_provided_period_value_wrong_formats(
        self,
        sut: StafferStatsService,
        period_type: PeriodType,
        period_value: str,
    ):
        with pytest.raises(ValueError):
            sut.create_period(period_type, period_value)

    def test_creates_weekly_period_near_years_end(
        self,
        sut: StafferStatsService,
        this_year: int = 2025,
    ):
        # ncal
        #     December 2024           January 2025
        # Mo     2  9 16 23 30   Mo     6 13 20 27
        # Tu     3 10 17 24 31   Tu     7 14 21 28
        # We     4 11 18 25      We  1  8 15 22 29
        # Th     5 12 19 26      Th  2  9 16 23 30
        # Fr     6 13 20 27      Fr  3 10 17 24 31
        # Sa     7 14 21 28      Sa  4 11 18 25
        # Su  1  8 15 22 29      Su  5 12 19 26
        #    48 49 50 51 52 53       1  2  3  4  5
        date_in_last_week_prev_year = date(this_year - 1, 12, 30)  # Monday
        date_in_first_week_this_year = date(this_year, 1, 1)  # Wednesday
        date_first_full_week_start_this_year = date(this_year, 1, 5)  # Sunday

        period_ids = list[str]()

        def run(d: date) -> None:
            with (
                freeze_time(d),
                patch(
                    "webapps.stats_and_reports.v2.app.staffer_stats_service"
                    ".SinceFixedDateTillToday"
                ),
            ):
                period_ids.append(sut.create_period(PeriodType.WEEKLY).identity)

        run(date_in_last_week_prev_year)
        run(date_in_first_week_this_year)
        run(date_first_full_week_start_this_year)

        assert period_ids == ["2024__52", "2024__52", "2025__01"]
