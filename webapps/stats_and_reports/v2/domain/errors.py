from datetime import date

from webapps.stats_and_reports.v2.domain.period_type import PeriodType


class Error(Exception):
    pass


class NotFirstDayOfPeriodError(Error):
    def __init__(self, period: PeriodType, d: date):
        super().__init__(f"date '{d.isoformat()}' does not begin a '{period.value}' period")


class OutOfRangeError(Error):
    def __init__(self, d: date, *, lo: date, hi: date):
        super().__init__(
            f"date '{d.isoformat()}' out of supported range ({lo.isoformat()}:{hi.isoformat()})"
        )
