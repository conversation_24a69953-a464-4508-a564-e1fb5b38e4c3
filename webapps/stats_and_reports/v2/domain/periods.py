import abc
from datetime import date, datetime, timedelta
from typing import Never, assert_never, cast

from dateutil.relativedelta import relativedelta

from webapps.stats_and_reports.v2.domain.errors import (
    NotFirstDayOfPeriodError,
    OutOfRangeError,
)
from webapps.stats_and_reports.v2.domain.period_type import PeriodType
from webapps.stats_and_reports.v2.domain.range_policy import (
    FixedRange,
    RangePolicy,
)


class Period(abc.ABC):
    @property
    @abc.abstractmethod
    # 2025__04__19
    # 2025__44
    # 2025__01
    # 2025
    def identity(self) -> str: ...

    @property
    @abc.abstractmethod
    def name(self) -> PeriodType: ...

    @property
    @abc.abstractmethod
    def value(self) -> str: ...

    @property
    @abc.abstractmethod
    def next(self) -> "Period|None": ...

    @property
    @abc.abstractmethod
    def previous(self) -> "Period|None": ...


class PeriodDaily(Period):
    def __init__(
        self,
        d: date,
        *,
        range_policy: RangePolicy | None = None,
    ):
        self._range_policy = range_policy or FixedRange()
        self._range_policy.verify_in_range(d)

        self._date = d

    @property
    def name(self) -> PeriodType:
        return PeriodType.DAILY

    @property
    def identity(self) -> str:
        year = self._date.strftime("%Y")
        month = self._date.strftime("%m")
        day = self._date.strftime("%d")
        return "__".join([year, month, day])

    @property
    def value(self) -> str:
        return self._date.isoformat()

    @property
    def next(self) -> Period | None:
        date_next = self._date + timedelta(days=1)
        try:
            return PeriodDaily(date_next, range_policy=self._range_policy)
        except OutOfRangeError:
            return None

    @property
    def previous(self) -> Period | None:
        date_prev = self._date - timedelta(days=1)
        try:
            return PeriodDaily(date_prev, range_policy=self._range_policy)
        except OutOfRangeError:
            return None


class PeriodWeekly(Period):
    def __init__(
        self,
        d: date,
        *,
        range_policy: RangePolicy | None = None,
    ):
        if d.strftime("%w") != "0":  # Sunday
            raise NotFirstDayOfPeriodError(PeriodType.WEEKLY, d)

        self._range_policy = range_policy or FixedRange()
        self._range_policy.verify_in_range(d)

        self._first_day_of_week = d

    @property
    def name(self) -> PeriodType:
        return PeriodType.WEEKLY

    @property
    def identity(self) -> str:
        return self._str(sep="__")

    @property
    def value(self) -> str:
        return self._str(sep="-")

    @property
    def next(self) -> Period | None:
        next_week = self._first_day_of_week + timedelta(days=7)
        try:
            return PeriodWeekly(next_week, range_policy=self._range_policy)
        except OutOfRangeError:
            return None

    @property
    def previous(self) -> Period | None:
        prev_week = self._first_day_of_week - timedelta(days=7)
        try:
            return PeriodWeekly(prev_week, range_policy=self._range_policy)
        except OutOfRangeError:
            return None

    def _str(self, *, sep: str) -> str:
        year = f"{self._first_day_of_week.year:04d}"
        week = self._first_day_of_week.strftime("%U")
        return sep.join([year, week])


class PeriodMonthly(Period):
    def __init__(
        self,
        d: date,
        *,
        range_policy: RangePolicy | None = None,
    ):
        if d.day != 1:
            raise NotFirstDayOfPeriodError(PeriodType.MONTHLY, d)

        self._range_policy = range_policy or FixedRange()
        self._range_policy.verify_in_range(d)

        self._first_day_of_month = d

    @property
    def name(self) -> PeriodType:
        return PeriodType.MONTHLY

    @property
    def identity(self) -> str:
        return self._str(sep="__")

    @property
    def value(self) -> str:
        return self._str(sep="-")

    @property
    def next(self) -> Period | None:
        next_month = self._first_day_of_month + relativedelta(months=1)
        try:
            return PeriodMonthly(next_month, range_policy=self._range_policy)
        except OutOfRangeError:
            return None

    @property
    def previous(self) -> Period | None:
        prev_month = self._first_day_of_month - relativedelta(months=1)
        try:
            return PeriodMonthly(prev_month, range_policy=self._range_policy)
        except OutOfRangeError:
            return None

    def _str(self, *, sep: str) -> str:
        year = f"{self._first_day_of_month.year:04d}"
        month = f"{self._first_day_of_month.month:02d}"
        return sep.join([year, month])


class PeriodYearly(Period):
    def __init__(
        self,
        d: date,
        *,
        range_policy: RangePolicy | None = None,
    ):
        if (d.month, d.day) != (1, 1):
            raise NotFirstDayOfPeriodError(PeriodType.YEARLY, d)

        self._range_policy = range_policy or FixedRange()
        self._range_policy.verify_in_range(d)

        self._first_day_of_year = d

    @property
    def name(self) -> PeriodType:
        return PeriodType.YEARLY

    @property
    def identity(self) -> str:
        year = f"{self._first_day_of_year.year:04d}"
        return year

    @property
    def value(self) -> str:
        return self.identity

    @property
    def next(self) -> Period | None:
        next_year = self._first_day_of_year + relativedelta(years=1)
        try:
            return PeriodYearly(next_year, range_policy=self._range_policy)
        except OutOfRangeError:
            return None

    @property
    def previous(self) -> Period | None:
        prev_year = self._first_day_of_year - relativedelta(years=1)
        try:
            return PeriodYearly(prev_year, range_policy=self._range_policy)
        except OutOfRangeError:
            return None


def create_period(
    period_type: PeriodType,
    period_date: date,
    *,
    range_policy: RangePolicy,
) -> Period:
    match period_type:
        case PeriodType.DAILY:
            return PeriodDaily(period_date, range_policy=range_policy)

        case PeriodType.WEEKLY:
            return PeriodWeekly(period_date, range_policy=range_policy)

        case PeriodType.MONTHLY:
            return PeriodMonthly(period_date, range_policy=range_policy)

        case PeriodType.YEARLY:
            return PeriodYearly(period_date, range_policy=range_policy)

        case _:
            assert_never(cast(Never, period_type))
