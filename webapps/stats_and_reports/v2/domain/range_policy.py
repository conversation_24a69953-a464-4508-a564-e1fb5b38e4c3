import abc
from datetime import date, datetime, timezone

from webapps.stats_and_reports.v2.domain.errors import OutOfRangeError


class RangePolicy(abc.ABC):
    @abc.abstractmethod
    def verify_in_range(self, d: date) -> None: ...


class FixedRange(RangePolicy):
    def __init__(
        self,
        *,
        since: date | None = None,
        till: date | None = None,
    ):
        self._since = since or date.min
        self._till = till or date.max
        if not (self._since <= self._till):
            raise ValueError("since must come before till")

    def verify_in_range(self, d: date) -> None:
        if not (self._since <= d <= self._till):
            raise OutOfRangeError(d, lo=self._since, hi=self._till)


class SinceFixedDateTillToday(RangePolicy):
    def __init__(self, since: date | None = None):
        self._since = since or date.min

    def verify_in_range(self, d: date) -> None:
        today = datetime.now(timezone.utc).date()
        if not (self._since <= d <= today):
            raise OutOfRangeError(d, lo=self._since, hi=today)
