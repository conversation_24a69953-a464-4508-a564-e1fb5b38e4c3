from dataclasses import dataclass
from decimal import Decimal


@dataclass
class RevenueAndCommissions:
    revenue: Decimal | None = None
    commission: Decimal | None = None


@dataclass
class RevenueAndCommissionsDetails:
    services: RevenueAndCommissions | None
    add_ons: RevenueAndCommissions | None
    products: RevenueAndCommissions | None
    gift_cards: RevenueAndCommissions | None
    packages: RevenueAndCommissions | None
    membership: RevenueAndCommissions | None
    total_revenue: Decimal | None
    total_commission: Decimal | None


@dataclass
class Tips:
    number: int | None = None
    average: Decimal | None = None
    value: Decimal | None = None


@dataclass
class Reviews:
    number: int | None = None
    average: Decimal | None = None


@dataclass
class Stats:
    revenue_and_commissions: RevenueAndCommissionsDetails | None
    tips: Tips | None
    reviews: Reviews | None
    calendar_occupancy: int | None = None


@dataclass
class StafferStats:
    staffer_id: int
    stats: Stats
