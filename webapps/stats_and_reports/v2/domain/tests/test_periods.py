from datetime import date

import pytest

from webapps.stats_and_reports.v2.domain.errors import (
    NotFirstDayOfPeriodError,
    OutOfRangeError,
)
from webapps.stats_and_reports.v2.domain.period_type import PeriodType
from webapps.stats_and_reports.v2.domain.periods import (
    PeriodDaily,
    PeriodMonthly,
    PeriodWeekly,
    PeriodYearly,
)
from webapps.stats_and_reports.v2.domain.range_policy import FixedRange

range_policy = FixedRange(
    since=date(2024, 1, 1),
    till=date(2025, 3, 30),
)


class TestPeriodDaily:
    somedate = date(2025, 3, 30)

    testcases_identity_value = [
        (PeriodDaily(date(2025, 1, 1)), "2025__01__01", "2025-01-01"),
        (PeriodDaily(date(1, 1, 1)), "1__01__01", "0001-01-01"),
    ]
    testcases_next = [
        pytest.param(
            (date(2025, 1, 1),),
            "2025__01__02",
            id="normal day",
        ),
        pytest.param(
            (date(2025, 1, 31),),
            "2025__02__01",
            id="next month",
        ),
        pytest.param(
            (date(2025, 2, 28),),
            "2025__03__01",
            id="february to march",
        ),
        pytest.param(
            (date(2024, 2, 28),),
            "2024__02__29",
            id="february to march (leap year)",
        ),
        pytest.param(
            (date(2024, 12, 31),),
            "2025__01__01",
            id="next year",
        ),
        pytest.param(
            (date(2025, 3, 30),),
            None,
            id="next period in the future",
        ),
    ]
    testcases_previous = [
        pytest.param(
            (date(2025, 1, 2),),
            "2025__01__01",
            id="normal day",
        ),
        pytest.param(
            (date(2025, 2, 1),),
            "2025__01__31",
            id="previous month",
        ),
        pytest.param(
            (date(2025, 3, 1),),
            "2025__02__28",
            id="march to february",
        ),
        pytest.param(
            (date(2024, 3, 1),),
            "2024__02__29",
            id="march to february (leap year)",
        ),
        pytest.param(
            (date(2025, 1, 1),),
            "2024__12__31",
            id="previous year",
        ),
        pytest.param(
            (date(2024, 1, 1),),
            None,
            id="previous period out of range",
        ),
    ]

    def test_constructed(self):
        sut = PeriodDaily(self.somedate)
        assert sut.identity == "2025__03__30"

    @pytest.mark.parametrize(
        "d",
        [
            date(2023, 12, 31),
            date(9999, 12, 31),
        ],
    )
    def test_init_raises_when_date_not_supported(self, d: date):
        with pytest.raises(OutOfRangeError):
            PeriodDaily(d, range_policy=range_policy)

    @pytest.mark.parametrize("period,identity,value", testcases_identity_value)
    def test_constructs_identity(self, period: PeriodDaily, identity: str, value: str):
        assert period.identity == identity

    @pytest.mark.parametrize("period,identity,value", testcases_identity_value)
    def test_constructs_value(self, period: PeriodDaily, identity: str, value: str):
        assert period.value == value

    def test_proper_name(self):
        assert PeriodDaily(date.today()).name == PeriodType.DAILY

    @pytest.mark.parametrize("args,next_id", testcases_next)
    def test_next(self, args: tuple, next_id: str | None):
        period = PeriodDaily(*args, range_policy=range_policy)
        period_next = period.next
        assert (next_id is None and period_next is None) or (
            next_id is not None and period_next is not None and period_next.identity == next_id
        )

    @pytest.mark.parametrize("args,prev_id", testcases_previous)
    def test_previous(self, args, prev_id: str | None):
        period = PeriodDaily(*args, range_policy=range_policy)
        period_prev = period.previous
        assert (prev_id is None and period_prev is None) or (
            prev_id is not None and period_prev is not None and period_prev.identity == prev_id
        )


class TestPeriodWeekly:
    testcases_identity_value = [
        (PeriodWeekly(date(1, 1, 7)), "0001__01", "0001-01"),
        (PeriodWeekly(date(2025, 3, 23)), "2025__12", "2025-12"),
        (PeriodWeekly(date(2025, 1, 5)), "2025__01", "2025-01"),
        (PeriodWeekly(date(2024, 12, 29)), "2024__52", "2024-52"),
    ]
    testcases_next = [
        pytest.param(
            (date(2025, 1, 5),),
            "2025__02",
            id="first full week of a year",
        ),
        pytest.param(
            (date(2024, 12, 22),),
            "2024__52",
            id="before last week of the year",
        ),
        pytest.param(
            (date(2024, 12, 29),),
            "2025__01",
            id="last week of the year",
        ),
        pytest.param(
            (date(2025, 3, 30),),
            None,
            id="next period in the future",
        ),
    ]
    testcases_previous = [
        pytest.param(
            (date(2025, 1, 5),),
            "2024__52",
            id="first full week of a year",
        ),
        pytest.param(
            (date(2024, 12, 29),),
            "2024__51",
            id="before last week of the year",
        ),
        pytest.param(
            (date(2024, 1, 7),),
            None,
            id="previous period out of range",
        ),
    ]

    @pytest.mark.parametrize(
        "d,identity",
        [
            (date(2024, 12, 29), "2024__52"),
            (date(2025, 1, 5), "2025__01"),
            (date(2025, 1, 12), "2025__02"),
        ],
    )
    def test_init(self, d: date, identity: str):
        assert PeriodWeekly(d).identity == identity

    @pytest.mark.parametrize(
        "d",
        [
            date(2024, 12, 30),
            date(2024, 12, 31),
            date(2025, 1, 1),
            date(2025, 1, 2),
            date(2025, 1, 3),
            date(2025, 1, 4),
        ],
    )
    def test_init_raises_when_date_not_first_day_of_week(self, d: date):
        with pytest.raises(NotFirstDayOfPeriodError):
            PeriodWeekly(d)

    @pytest.mark.parametrize(
        "d",
        [
            date(2023, 12, 31),
            date(9999, 12, 26),
        ],
    )
    def test_init_raises_when_date_not_supported(self, d: date):
        with pytest.raises(OutOfRangeError):
            PeriodWeekly(d, range_policy=range_policy)

    @pytest.mark.parametrize("period,identity,value", testcases_identity_value)
    def test_constructs_identity(self, period: PeriodWeekly, identity: str, value: str):
        assert period.identity == identity

    @pytest.mark.parametrize("period,identity,value", testcases_identity_value)
    def test_constructs_value(self, period: PeriodWeekly, identity: str, value: str):
        assert period.value == value

    def test_proper_name(self):
        assert PeriodWeekly(date(2025, 3, 23)).name == PeriodType.WEEKLY

    @pytest.mark.parametrize("args,next_id", testcases_next)
    def test_next(self, args: tuple, next_id: str | None):
        period = PeriodWeekly(*args, range_policy=range_policy)
        period_next = period.next
        assert (next_id is None and period_next is None) or (
            next_id is not None and period_next is not None and period_next.identity == next_id
        )

    @pytest.mark.parametrize("args,prev_id", testcases_previous)
    def test_previous(self, args: tuple, prev_id: str):
        period = PeriodWeekly(*args, range_policy=range_policy)
        period_prev = period.previous
        assert (prev_id is None and period_prev is None) or (
            prev_id is not None and period_prev is not None and period_prev.identity == prev_id
        )


class TestPeriodMonthly:
    testcases_identity_value = [
        (PeriodMonthly(date(1, 5, 1)), "0001__05", "0001-05"),
        (PeriodMonthly(date(2024, 12, 1)), "2024__12", "2024-12"),
        (PeriodMonthly(date(2025, 5, 1)), "2025__05", "2025-05"),
        (PeriodMonthly(date(2025, 1, 1)), "2025__01", "2025-01"),
    ]
    testcases_next = [
        pytest.param(
            (date(2025, 1, 1),),
            "2025__02",
            id="first month of year",
        ),
        pytest.param(
            (date(2024, 12, 1),),
            "2025__01",
            id="last month of year",
        ),
        pytest.param(
            (date(2025, 3, 1),),
            None,
            id="next month in the future",
        ),
    ]
    testcases_previous = [
        pytest.param(
            (date(2025, 1, 1),),
            "2024__12",
            id="first month of year",
        ),
        pytest.param(
            (date(2024, 12, 1),),
            "2024__11",
            id="last month of year",
        ),
        pytest.param(
            (date(2024, 1, 1),),
            None,
            id="previous period out of range",
        ),
    ]

    @pytest.mark.parametrize(
        "d,identity", [(date(2024, month, 1), f"2024__{month:02d}") for month in range(1, 13)]
    )
    def test_init(self, d: date, identity: str):
        assert PeriodMonthly(d).identity == identity

    @pytest.mark.parametrize(
        "d",
        [date(2024, 12, day) for day in range(2, 32)],
        ids=lambda x: x.isoformat(),
    )
    def test_init_raises_when_date_not_first_day_of_month(self, d: date):
        with pytest.raises(NotFirstDayOfPeriodError):
            PeriodMonthly(d)

    @pytest.mark.parametrize(
        "d",
        [
            date(2023, 12, 1),
            date(9999, 12, 1),
        ],
    )
    def test_init_raises_when_date_not_supported(self, d: date):
        with pytest.raises(OutOfRangeError):
            PeriodMonthly(d, range_policy=range_policy)

    @pytest.mark.parametrize("period,identity,value", testcases_identity_value)
    def test_constructs_identity(self, period: PeriodMonthly, identity: str, value: str):
        assert period.identity == identity

    @pytest.mark.parametrize("period,identity,value", testcases_identity_value)
    def test_constructs_value(self, period: PeriodMonthly, identity: str, value: str):
        assert period.value == value

    def test_proper_name(self):
        assert PeriodMonthly(date(2024, 12, 1)).name == PeriodType.MONTHLY

    @pytest.mark.parametrize("args,next_id", testcases_next)
    def text_next(self, args: tuple, next_id: str | None):
        period = PeriodMonthly(*args, range_policy=range_policy)
        period_next = period.next
        assert (next_id is None and period_next is None) or (
            next_id is not None and period_next is not None and period_next.identity == next_id
        )

    @pytest.mark.parametrize("args,prev_id", testcases_previous)
    def test_previous(self, args: tuple, prev_id: str):
        period = PeriodMonthly(*args, range_policy=range_policy)
        period_prev = period.previous
        assert (prev_id is None and period_prev is None) or (
            prev_id is not None and period_prev is not None and period_prev.identity == prev_id
        )


class TestPeriodYearly:
    testcases_identity_value = [
        (PeriodYearly(date(1, 1, 1)), "0001", "0001"),
        (PeriodYearly(date(123, 1, 1)), "0123", "0123"),
        (PeriodYearly(date(1999, 1, 1)), "1999", "1999"),
        (PeriodYearly(date(2025, 1, 1)), "2025", "2025"),
    ]
    testcases_next = [
        pytest.param(
            (date(2024, 1, 1),),
            "2025",
            id="next year",
        ),
        pytest.param(
            (date(2025, 1, 1),),
            None,
            id="next year in the future",
        ),
    ]
    testcases_previous = [
        pytest.param(
            (date(2025, 1, 1),),
            "2024",
            id="previous year",
        ),
        pytest.param(
            (date(2024, 1, 1),),
            None,
            id="previous period out of range",
        ),
    ]

    @pytest.mark.parametrize(
        "d,identity",
        [(date(year, 1, 1), f"{year:04d}") for year in [1, 1000, 2000, 2024, 2025]],
    )
    def test_init(self, d: date, identity: str):
        assert PeriodYearly(d).identity == identity

    @pytest.mark.parametrize(
        "d",
        [date(2025, 1, day) for day in range(2, 32)],
        ids=lambda x: x.isoformat(),
    )
    def test_init_raises_when_date_not_first_day_of_year(self, d: date):
        with pytest.raises(NotFirstDayOfPeriodError):
            PeriodYearly(d)

    @pytest.mark.parametrize(
        "d",
        [
            date(2023, 1, 1),
            date(9999, 1, 1),
        ],
    )
    def test_init_raises_when_date_not_supported(self, d: date):
        with pytest.raises(OutOfRangeError):
            PeriodYearly(d, range_policy=range_policy)

    @pytest.mark.parametrize("period,identity,value", testcases_identity_value)
    def test_constructs_identity(self, period: PeriodYearly, identity: str, value: str):
        assert period.identity == identity

    @pytest.mark.parametrize("period,identity,value", testcases_identity_value)
    def test_constructs_value(self, period: PeriodYearly, identity: str, value: str):
        assert period.value == value

    def test_proper_name(self):
        assert PeriodYearly(date(2025, 1, 1)).name == PeriodType.YEARLY

    @pytest.mark.parametrize("args,next_id", testcases_next)
    def test_next(self, args: tuple, next_id: str | None):
        period = PeriodYearly(*args, range_policy=range_policy)
        period_next = period.next
        assert (next_id is None and period_next is None) or (
            next_id is not None and period_next is not None and period_next.identity == next_id
        )

    @pytest.mark.parametrize("args,prev_id", testcases_previous)
    def test_previous(self, args: tuple, prev_id: str):
        period = PeriodYearly(*args, range_policy=range_policy)
        period_prev = period.previous
        assert (prev_id is None and period_prev is None) or (
            prev_id is not None and period_prev is not None and period_prev.identity == prev_id
        )
