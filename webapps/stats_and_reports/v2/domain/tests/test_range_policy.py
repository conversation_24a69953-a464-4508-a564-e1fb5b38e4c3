from datetime import date, datetime, timedelta, timezone

import pytest
from freezegun import freeze_time

from webapps.stats_and_reports.v2.domain.errors import OutOfRangeError
from webapps.stats_and_reports.v2.domain.range_policy import (
    FixedRange,
    SinceFixedDateTillToday,
)

now = datetime(2025, 5, 24, 7, 17, tzinfo=timezone.utc)


class TestFixedRange:
    since = date(2024, 1, 1)
    till = date(2024, 12, 31)

    def test_init_with_defaults_then_no_minmax(self):
        sut = FixedRange()
        sut.verify_in_range(date(1, 1, 1))
        sut.verify_in_range(date(9999, 12, 31))

    def test_init_raises_when_since_after_till(self):
        with pytest.raises(ValueError):
            FixedRange(since=date(2024, 1, 2), till=date(2024, 1, 1))

    @pytest.mark.parametrize(
        "d",
        [
            since,
            since + timedelta(days=1),
            date(2024, 6, 1),
            date(2024, 6, 15),
            till - timedelta(days=1),
            till,
        ],
    )
    def test_verify_in_range_passes(self, d: date):
        sut = FixedRange(since=self.since, till=self.till)
        sut.verify_in_range(d)

    @pytest.mark.parametrize(
        "d",
        [
            date(1, 1, 1),
            since - timedelta(days=100),
            since - timedelta(days=1),
            till + timedelta(days=1),
            till + timedelta(days=100),
            date(9999, 1, 1),
            date(9999, 12, 31),
        ],
    )
    def test_verify_in_range_raises(self, d: date):
        sut = FixedRange(since=self.since, till=self.till)
        with pytest.raises(OutOfRangeError):
            sut.verify_in_range(d)


@freeze_time(now)
class TestSinceFixedDateTillToday:
    since = date(2024, 1, 1)

    def test_init_with_defaults_then_no_min(self):
        sut = SinceFixedDateTillToday()
        sut.verify_in_range(date(1, 1, 1))
        with pytest.raises(OutOfRangeError):
            sut.verify_in_range(date(9999, 12, 31))

    @pytest.mark.parametrize(
        "d",
        [
            since,
            since + timedelta(days=1),
            date(2024, 6, 1),
            date(2024, 6, 15),
            date(2024, 12, 31),
            now.date() - timedelta(days=1),
            now.date(),
        ],
    )
    def test_verify_in_range_passes(self, d: date):
        sut = SinceFixedDateTillToday(since=self.since)
        sut.verify_in_range(d)

    @pytest.mark.parametrize(
        "d",
        [
            since - timedelta(days=100),
            since - timedelta(days=1),
            now.date() + timedelta(days=1),
            now.date() + timedelta(days=100),
        ],
    )
    def test_verify_in_range_raises(self, d: date):
        sut = SinceFixedDateTillToday(since=self.since)
        with pytest.raises(OutOfRangeError):
            sut.verify_in_range(d)
