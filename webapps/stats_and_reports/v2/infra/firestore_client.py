import logging
from abc import ABC, abstractmethod
from functools import lru_cache
from typing import Any

from google.cloud import firestore


class FirestoreClient(ABC):
    @abstractmethod
    def get_report(self, key: str) -> dict[str, Any] | None: ...


@lru_cache
class FirestoreClientImpl(FirestoreClient):
    def __init__(self, project_id: str, collection_name: str):
        self._logger = logging.getLogger(self.__class__.__name__)
        self._project_id: str = project_id
        self._collection_name: str = collection_name
        self._firestore_client = firestore.Client(project=self._project_id)
        self._logger.info(
            "Initializing firestore client with project id = %s and collection name = %s",
            project_id,
            collection_name,
        )

    def get_report(self, key: str) -> dict[str, Any] | None:
        document_ref = self._firestore_client.collection(self._collection_name).document(key)
        document = document_ref.get()
        if document.exists:
            return document.to_dict()
        self._logger.warning("Document with %s not found!", key)
        return None
