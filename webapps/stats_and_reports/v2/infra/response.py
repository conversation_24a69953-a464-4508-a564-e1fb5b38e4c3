import dataclasses
from decimal import Decimal
from functools import singledispatchmethod
from typing import Any, Callable, TypeVar, cast

from rest_framework.settings import api_settings

T = TypeVar("T")


class DataclassResponse:
    """A typed replacement for DRF's Serializer (at last!)

    Problems aimed to solve:
      1. Serializer validates or accepts input data as-is depending whether it was
         passed by `instance=` or `data=`.

         This made it hard to express what we actually want in the response and
         allowed passing guano without validation. If one used validation, then
         a ValidationError exception might have been raised which could be result
         in 'HTTP 400 Bad Request' due to **response** validation.

      2. Serialization & deserialization in one object serving often for request
         and response representation.

      3. Having a "data" field in the response.

    These are solved as followed
      - Responses are expected to be represented as a dataclass allowing devs
        to see what exactly is inside.

      - Responses may only be serialized. In fact, this is done by converting to
        simpler types with `to_dict` method. Then, any pythonic interface may be
        used to output the data to YAML, JSON, or whatever.

    So how does this work? Let's take a look at the example, shall we?

    First of all, you must define a dataclass representing the response

    >>> from dataclasses import dataclass
    >>> @dataclass
    ... class Response(DataclassResponse):
    ...     ...  # here go fields

    And as long as fields inside are simple Python types, that's it!
    You can serialize this response to Python simple types by calling `to_dict()`

    >>> resp = Response()
    >>> resp.to_dict()
    {}

    In fact, we are not restricted to simple Python types. By default, serialization
    is actually returning the passed object. For instance,

    >>> from dataclasses import dataclass
    >>> from datetime import date
    ...
    >>> @dataclass
    ... class Response(DataclassResponse):
    ...     today: date
    ...
    >>> Response(today=date(2025, 6, 4)).to_dict()
    {'today': datetime.date(2025, 6, 4)}

    Now, as long as the HTTP framework you are using supports `datetime.date` objects
    then this is fine. It even converts nested dataclasses into dictionaries, like this

    >>> from dataclasses import dataclass
    ...
    >>> @dataclass
    ... class Nested:
    ...     name: str
    ...
    >>> @dataclass
    ... class Response(DataclassResponse):
    ...     nested: Nested
    ...
    >>> Response(nested=Nested(name="Bob")).to_dict()
    {'nested': {'name': 'Bob'}}

    However, there are situations, when you would like to modify this behavior and
    return an even simpler object, such as a `str`. This is possible by a few methods:

      1. For all implementations of the `DataclassResponse`.

         This should be used only for types that have a chance of appearing in every
         response and in a way that makes sense in each occurance. For instance,
         representing a `date` as a ISO8601 formatted string or a `Decimal` as a string.

         This is done by defining the `Serializer.serialize` method defined in this file.

      2. For a specific implementation of the `DataclassResponse`.

         This should be used most often, for types that appear in a given response.
         Serialized representation must make sense only for this particular case.

         This is done by specyfing a `Meta.serializer_cls` on a dataclass that gets their
         fields serialized.

         >>> from dataclasses import dataclass
         >>> from datetime import date
         >>> from functools import singledispatchmethod
         ...
         >>> class CustomSerializer(Serializer):
         ...     @singledispatchmethod
         ...     def serialize(self, value: Any) -> Any:
         ...         if isinstance(value, date):
         ...             return value.isoformat()
         ...         return super().serialize(value)
         ...
         >>> @dataclass
         ... class Response(DataclassResponse):
         ...     class Meta:
         ...         serializer_cls = CustomSerializer
         ...
         ...     today: date
         ...
         >>> Response(today=date(2025, 6, 4)).to_dict()
         {'today': '2025-06-04'}

         Note that the `Meta` object is tied to a specific `dataclass`. Thus, in case of nested
         dataclasses, it must be specified on a level of the field you wish to serialize.

         >>> from dataclasses import asdict, dataclass, is_dataclass
         >>> from datetime import date
         >>> from functools import singledispatchmethod
         ...
         >>> class CustomSerializer(Serializer):
         ...     @singledispatchmethod
         ...     def serialize(self, value: Any) -> Any:
         ...         if isinstance(value, date):
         ...             return value.isoformat()
         ...         if is_dataclass(value):
         ...             return asdict(value)
         ...         return super().serialize(value)
         ...
         >>> @dataclass
         ... class Nested:
         ...     d: date
         ...
         >>> @dataclass
         ... class Response(DataclassResponse):
         ...     class Meta:
         ...         serializer_cls = CustomSerializer
         ...
         ...     d: date
         ...     nested: Nested
         ...
         >>> Response(d=date(2025, 6, 4), nested=Nested(d=date(2025, 6, 4))).to_dict()
         {'d': '2025-06-04', 'nested': {'d': datetime.date(2025, 6, 4)}}

      3. For a specific field of a given implementation of the `DataclassResponse`.

         This is meant to be used for simple cases that don't need to be reused between
         different instances.

         >>> from dataclasses import dataclass
         >>> from datetime import date
         ...
         >>> @dataclass
         ... class Response(DataclassResponse):
         ...     today: date
         ...
         ...     def _serialize_today(self, value: date) -> str:
         ...         return value.isoformat()
         ...
         >>> Response(today=date(2025, 6, 4)).to_dict()
         {'today': '2025-06-04'}

    Apart from specyfing "how" to serialize, you may also control "when" to serialize.

    This can be done for a specific field of a given implementation of the `DataclassResponse`,
    in a similar way to what you've already seen

    >>> from dataclasses import dataclass
    >>> from datetime import date, timedelta
    ...
    >>> @dataclass
    ... class Response(DataclassResponse):
    ...     d: date
    ...
    ...     def _is_serializable_d(self, value: date) -> bool:
    ...         return abs(value - date(2025, 12, 24)) <= timedelta(days=10)
    ...
    >>> Response(d=date(2025, 6, 4)).to_dict()
    {}
    >>> Response(d=date(2025, 12, 23)).to_dict()
    {'d': datetime.date(2025, 12, 23)}

    It's common not to serialize `None` values, so there's a `Meta.serialize_nones` option for this
    which is enabled by default

    >>> from dataclasses import dataclass
    >>> from datetime import date
    ...
    >>> @dataclass
    ... class Response(DataclassResponse):
    ...     class Meta:
    ...         serialize_nones = False
    ...
    ...     d: date | None
    ...
    >>> Response(d=None).to_dict()
    {}
    >>> Response(d=date(2025, 12, 23)).to_dict()
    {'d': datetime.date(2025, 12, 23)}
    """

    def to_dict(self) -> dict:
        def _process(stack: list):
            while stack:
                target, obj = stack.pop()
                for field in dataclasses.fields(obj):
                    value = getattr(obj, field.name)
                    if not self._is_serializable(value, parent=obj, field=field):
                        continue

                    if serializer := self._get_custom_serializer(parent=obj, field=field):
                        target[field.name] = serializer(value)
                        continue

                    if dataclasses.is_dataclass(value):
                        target[field.name] = target_field = {}
                        stack.append((target_field, value))
                        continue

                    target[field.name] = self._serialize(value)

        assert dataclasses.is_dataclass(self)
        _process([(serialized := {}, self)])
        return serialized

    def _get_custom_serializer(
        self,
        *,
        parent: Any,
        field: dataclasses.Field,
    ) -> Callable[[Any], Any] | None:
        if method_serializer := getattr(parent, f"_serialize_{field.name}", None):
            return method_serializer

        if serializer_cls := self._meta(parent, "serializer_cls"):
            return serializer_cls().serialize

        return None

    def _is_serializable(self, value: Any, *, parent: Any, field: dataclasses.Field) -> bool:
        if predicate := getattr(parent, f"_is_serializable_{field.name}", None):
            return predicate(value)

        serialize_nones = self._meta(parent, "serialize_nones", default=True)
        if not serialize_nones and value is None:
            return False

        return True

    @staticmethod
    def _serialize(value: Any) -> Any:
        return Serializer().serialize(value)

    def _meta(self, parent: Any, name: str, *, default: T | None = None) -> T | None:
        return getattr(getattr(parent, "Meta", None), name, default)


class Serializer:
    @singledispatchmethod
    def serialize(self, value: Any) -> Any:
        return value

    @serialize.register
    def _(self, value: Decimal) -> str | Decimal:
        quantized = value.quantize(Decimal(".01"))

        # To preserve serializers' behavior, use DRF's settings.
        coerce_to_string = cast(bool, getattr(api_settings, "COERCE_DECIMAL_TO_STRING", True))
        if not coerce_to_string:
            return quantized

        return "{:f}".format(quantized)
