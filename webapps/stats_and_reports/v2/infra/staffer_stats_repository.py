import logging
from decimal import Decimal
from typing import Any

from settings import local

from webapps.stats_and_reports.v2.app.staffer_stats_repository import (
    StafferReportQuery,
    StafferStatsRepository,
)
from webapps.stats_and_reports.v2.domain.reports.staffer_stats import (
    StafferStats,
    RevenueAndCommissions,
    RevenueAndCommissionsDetails,
    Tips,
    Reviews,
    Stats,
)
from webapps.stats_and_reports.v2.infra.errors import (
    FirestoreClientIsNotInitializedError,
    FirestoreDocumentCannotBeDeserializedError,
)
from webapps.stats_and_reports.v2.infra.firestore_client import FirestoreClientImpl


def _convert_raw_document_to_staffer_stats(raw_document: dict[str, Any]) -> StafferStats:
    if 'staffer_id' not in raw_document or raw_document['staffer_id'] is None:
        raise FirestoreDocumentCannotBeDeserializedError()
    stats: Stats = Stats(
        revenue_and_commissions=RevenueAndCommissionsDetails(
            services=RevenueAndCommissions(
                revenue=(
                    Decimal(str(service_gross_revenue))
                    if (service_gross_revenue := raw_document.get('service_gross_revenue'))
                    is not None
                    else None
                ),
                commission=(
                    Decimal(str(service_commission))
                    if (service_commission := raw_document.get('service_commission')) is not None
                    else None
                ),
            ),
            add_ons=RevenueAndCommissions(
                revenue=(
                    Decimal(str(addon_gross_revenue))
                    if (addon_gross_revenue := raw_document.get('addon_gross_revenue')) is not None
                    else None
                ),
                commission=(
                    Decimal(str(addon_commission))
                    if (addon_commission := raw_document.get('addon_commission')) is not None
                    else None
                ),
            ),
            products=RevenueAndCommissions(
                revenue=(
                    Decimal(str(product_gross_revenue))
                    if (product_gross_revenue := raw_document.get('product_gross_revenue'))
                    is not None
                    else None
                ),
                commission=(
                    Decimal(str(product_commission))
                    if (product_commission := raw_document.get('product_commission')) is not None
                    else None
                ),
            ),
            gift_cards=RevenueAndCommissions(
                revenue=(
                    Decimal(str(gift_card_gross_revenue))
                    if (gift_card_gross_revenue := raw_document.get('gift_card_gross_revenue'))
                    is not None
                    else None
                ),
                commission=(
                    Decimal(str(gift_card_commission))
                    if (gift_card_commission := raw_document.get('gift_card_commission'))
                    is not None
                    else None
                ),
            ),
            packages=RevenueAndCommissions(
                revenue=(
                    Decimal(str(package_gross_revenue))
                    if (package_gross_revenue := raw_document.get('package_gross_revenue'))
                    is not None
                    else None
                ),
                commission=(
                    Decimal(str(package_commission))
                    if (package_commission := raw_document.get('package_commission')) is not None
                    else None
                ),
            ),
            membership=RevenueAndCommissions(
                revenue=(
                    Decimal(str(membership_gross_revenue))
                    if (membership_gross_revenue := raw_document.get('membership_gross_revenue'))
                    is not None
                    else None
                ),
                commission=(
                    Decimal(str(membership_commission))
                    if (membership_commission := raw_document.get('membership_commission'))
                    is not None
                    else None
                ),
            ),
            total_revenue=(
                Decimal(str(total_gross_revenue))
                if (total_gross_revenue := raw_document.get('total_gross_revenue')) is not None
                else None
            ),
            total_commission=(
                Decimal(str(total_commission))
                if (total_commission := raw_document.get('total_commission')) is not None
                else None
            ),
        ),
        tips=Tips(
            number=(
                int(tips_count)
                if (tips_count := raw_document.get('tips_count')) is not None
                else None
            ),
            average=(
                int(tips_average)
                if (tips_average := raw_document.get('tips_average')) is not None
                else None
            ),
            value=(
                Decimal(str(tips_amount))
                if (tips_amount := raw_document.get('tips_amount')) is not None
                else None
            ),
        ),
        reviews=Reviews(
            number=(
                int(reviews_count)
                if (reviews_count := raw_document.get('reviews_count')) is not None
                else None
            ),
            average=(
                Decimal(str(reviews_rank_average))
                if (reviews_rank_average := raw_document.get('reviews_rank_average')) is not None
                else None
            ),
        ),
        calendar_occupancy=(
            int(calendar_occupancy)
            if (calendar_occupancy := raw_document.get('calendar_occupancy')) is not None
            else None
        ),
    )
    return StafferStats(staffer_id=int(raw_document['staffer_id']), stats=stats)


class FirestoreStafferStatsRepository(StafferStatsRepository):
    def __init__(self):
        self._logger = logging.getLogger(self.__class__.__name__)
        self._firestore_client = None
        if local.STATS_AND_REPORTS_V2_GCP_FIRESTORE_PROJECT_ID is None:
            self._logger.warning("Make sure that you passed firestore project ID via settings.")

        if local.STATS_AND_REPORTS_V2_GCP_FIRESTORE_COLLECTION_NAME is None:
            self._logger.warning(
                "Make sure that you passed firestore collection name via settings."
            )

        if (
            local.STATS_AND_REPORTS_V2_GCP_FIRESTORE_PROJECT_ID is not None
            and local.STATS_AND_REPORTS_V2_GCP_FIRESTORE_COLLECTION_NAME is not None
        ):
            self._firestore_client = FirestoreClientImpl(
                project_id=local.STATS_AND_REPORTS_V2_GCP_FIRESTORE_PROJECT_ID,
                collection_name=local.STATS_AND_REPORTS_V2_GCP_FIRESTORE_COLLECTION_NAME,
            )

    def get(self, query: StafferReportQuery) -> StafferStats | None:
        if self._firestore_client is None:
            raise FirestoreClientIsNotInitializedError()

        raw_document = self._firestore_client.get_report(key=query.identity)
        if raw_document is None:
            return None

        staffer_stats = _convert_raw_document_to_staffer_stats(raw_document)
        return staffer_stats
