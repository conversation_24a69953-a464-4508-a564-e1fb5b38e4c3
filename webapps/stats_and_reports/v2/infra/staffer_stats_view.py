from dataclasses import dataclass
from functools import singledispatchmethod
from typing import Any

from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from drf_api.service.business.validators.access import get_business_manager_validator
from drf_api.base_views import BaseBooksySessionAPIView
from lib.feature_flag.feature.stats_and_reports_v2 import StatsAndReportsV2StafferStats
from webapps.business.adapters import AccessControlAdapter

from webapps.stats_and_reports.v2.app.errors import (
    ReportNotFoundError,
    StafferDoesNotExistError,
)
from webapps.stats_and_reports.v2.app.staffer_stats_service import StafferStatsService
from webapps.stats_and_reports.v2.domain.period_type import PeriodType
from webapps.stats_and_reports.v2.domain.periods import Period
from webapps.stats_and_reports.v2.domain.reports.staffer_stats import StafferStats
from webapps.stats_and_reports.v2.infra.errors import (
    FirestoreClientIsNotInitializedError,
)
from webapps.stats_and_reports.v2.infra.get_staffer_stats_url_param_serializer import (
    GetStafferStatsUrlParamSerializer,
)
from webapps.stats_and_reports.v2.infra.response import DataclassResponse, Serializer
from webapps.stats_and_reports.v2.infra.staffer_stats_repository import (
    FirestoreStafferStatsRepository,
)


class MetadataSerializer(Serializer):
    @singledispatchmethod
    def serialize(self, value: Any) -> Any:
        if isinstance(value, Period):
            return value.value
        return super().serialize(value)


@dataclass(kw_only=True)
class GetResponse(DataclassResponse):
    class Meta:
        serialize_nones = False

    @dataclass(kw_only=True)
    class Metadata:
        class Meta:
            serializer_cls = MetadataSerializer
            serialize_nones = False

        current: Period
        prev: Period | None = None
        next: Period | None = None

    data: StafferStats
    metadata: Metadata | None = None


class StafferStatsView(BaseBooksySessionAPIView):
    booksy_teams = BooksyTeams.PROVIDER_ENGAGEMENT
    permission_classes = (IsAuthenticated,)

    def get(self, request: Request, business_pk: int, staffer_pk: int):
        if not StatsAndReportsV2StafferStats():
            return Response(status=status.HTTP_501_NOT_IMPLEMENTED)

        business_validator = get_business_manager_validator(
            request=request,
            business=business_pk,
            user=request.user,
        )
        business_validator.validate()

        url_param_serializer = GetStafferStatsUrlParamSerializer(data=request.query_params)
        url_param_serializer.is_valid(raise_exception=True)
        period_type = PeriodType(url_param_serializer.validated_data['period_type'])

        service = StafferStatsService(
            staffer_stats_repository=FirestoreStafferStatsRepository(),
            access_control_port=AccessControlAdapter(),
        )
        try:
            period = service.create_period(
                period_type,
                url_param_serializer.validated_data.get("period", ""),
            )
        except ValueError:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        try:
            staffer_stats = service.get_report(
                business_pk,
                staffer_pk,
                period=period,
            )
        except (StafferDoesNotExistError, ReportNotFoundError):
            return Response(status=status.HTTP_404_NOT_FOUND)
        except FirestoreClientIsNotInitializedError:
            return Response(status=status.HTTP_503_SERVICE_UNAVAILABLE)

        return Response(
            data=GetResponse(
                data=staffer_stats,
                metadata=GetResponse.Metadata(
                    current=period,
                    next=period.next,
                    prev=period.previous,
                ),
            ).to_dict(),
            status=status.HTTP_200_OK,
        )
