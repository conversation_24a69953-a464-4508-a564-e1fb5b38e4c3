from dataclasses import dataclass, field, make_dataclass
from decimal import Decimal
from typing import Any

import pytest
from django.test.utils import override_settings

from webapps.stats_and_reports.v2.infra.response import DataclassResponse


class TestDataclassResponse:
    @dataclass
    class SomeDataClass:
        foo: str = "bar"

    def test_refuses_to_serialize_when_not_dataclass(self):
        class Fake(DataclassResponse):
            pass

        sut = Fake()
        with pytest.raises(AssertionError):
            sut.to_dict()

    @override_settings(REST_FRAMEWORK=dict(COERCE_DECIMAL_TO_STRING=False))
    @pytest.mark.parametrize(
        "fields,kwargs,expected",
        [
            pytest.param(
                [],
                {},
                {},
                id="empty",
            ),
            pytest.param(
                [('name', str)],
                {'name': '<PERSON>'},
                {'name': '<PERSON>'},
                id="str",
            ),
            pytest.param(
                [('age', int)],
                {'age': 33},
                {'age': 33},
                id="int",
            ),
            pytest.param(
                [('avg', Decimal)], {'avg': Decimal('1.23')}, {'avg': Decimal('1.23')}, id="decimal"
            ),
            pytest.param(
                [('scores', list[int])],
                {'scores': [1, 2, 3]},
                {'scores': [1, 2, 3]},
                id="list[int]",
            ),
            pytest.param(
                [('obj', SomeDataClass)],
                {'obj': SomeDataClass()},
                {'obj': {"foo": "bar"}},
                id="nested dataclass",
            ),
        ],
    )
    def test_serializes_dataclass(self, fields: list, kwargs: dict, expected: dict):
        cls = make_dataclass("Fake", fields, bases=(DataclassResponse,))
        sut = cls(**kwargs)
        assert sut.to_dict() == expected

    @override_settings(REST_FRAMEWORK=dict(COERCE_DECIMAL_TO_STRING=True))
    def test_serializes_dataclass_decimal_as_str(self):
        @dataclass
        class Fake(DataclassResponse):
            field: Decimal

        assert Fake(field=Decimal("1")).to_dict() == {"field": "1.00"}

    def test_serializes_default_value(self):
        @dataclass
        class Fake(DataclassResponse):
            field: str = "default"

        assert Fake().to_dict() == {"field": "default"}

    def test_uses_custom_serializer_method(
        self,
        default=123,
        serialized="serialized-number",
    ):
        @dataclass
        class Fake(DataclassResponse):
            number_custom: int = default
            number: int = default

            @staticmethod
            def _serialize_number_custom(value: int) -> bool:
                return "serialized-number"

        assert Fake().to_dict() == {"number_custom": serialized, "number": default}

    def test_uses_custom_serializer_method_nested_and_root(self):
        @dataclass
        class Fake(DataclassResponse):
            @dataclass
            class Nested:
                data: str

                @staticmethod
                def _serialize_data(value: str) -> str:
                    return "123"

            data: Nested
            other: Nested

            @staticmethod
            def _serialize_data(value: Nested) -> dict:
                return {}

        fake = Fake(data=Fake.Nested(data="data"), other=Fake.Nested(data="other"))
        assert fake.to_dict() == {
            "data": {},
            "other": {"data": "123"},
        }

    def test_uses_custom_serializer_meta(
        self,
        default=123,
        serialized="serialized-number",
    ):
        class Serializer:
            def serialize(self, _: Any) -> str:
                return serialized

        @dataclass
        class Fake(DataclassResponse):
            class Meta:
                serializer_cls = Serializer

            number_custom: int = default
            number: int = default

        assert Fake().to_dict() == {"number_custom": serialized, "number": serialized}

    def test_uses_custom_serializer_meta_nested(self):
        class Serializer:
            def serialize(self, _: Any) -> str:
                return "ok"

        @dataclass
        class Fake(DataclassResponse):
            @dataclass
            class Nested:
                class Meta:
                    serializer_cls = Serializer

                data: str

            data: Nested
            other: Nested

        fake = Fake(data=Fake.Nested(data="data"), other=Fake.Nested(data="other"))
        assert fake.to_dict() == {
            "data": {"data": "ok"},
            "other": {"data": "ok"},
        }

    def test_uses_custom_serializer_meta_nested_and_root(self):
        class Serializer:
            def serialize(self, _: Any) -> str:
                return "root"

        class SerializerNested:
            def serialize(self, _: Any) -> str:
                return "nested"

        @dataclass
        class Fake(DataclassResponse):
            class Meta:
                serializer_cls = Serializer

            @dataclass
            class Nested:
                class Meta:
                    serializer_cls = SerializerNested

                data: str

            data: Nested
            other: Nested

        fake = Fake(data=Fake.Nested(data="data"), other=Fake.Nested(data="other"))
        assert fake.to_dict() == {
            "data": "root",
            "other": "root",
        }

    @pytest.mark.parametrize(
        "kwargs,expected",
        [
            ({'text': "skip", 'number': 1}, {}),
            ({'text': "skip", 'number': 2}, {"number": 2}),
            ({'text': "pass", 'number': 1}, {"text": "pass"}),
            ({'text': "pass", 'number': 2}, {"text": "pass", "number": 2}),
        ],
    )
    def test_skips_field(self, kwargs: dict, expected: dict):
        @dataclass
        class Fake(DataclassResponse):
            text: str
            number: int

            @staticmethod
            def _is_serializable_text(value: Any) -> bool:
                assert isinstance(value, str)
                return value != "skip"

            @staticmethod
            def _is_serializable_number(value: Any) -> bool:
                assert isinstance(value, int)
                return value % 2 == 0

        assert Fake(**kwargs).to_dict() == expected

    def test_skips_field_nested_object(self):
        @dataclass
        class Fake(DataclassResponse):
            @dataclass
            class Nested:
                data: str

                @staticmethod
                def _is_serializable_data(value: Any) -> bool:
                    return False

            data: Nested

            @staticmethod
            def _is_serializable_data(value: Any) -> bool:
                return True

        assert Fake(data=Fake.Nested(data="str")).to_dict() == {"data": {}}

    def test_runs_user_defined_validation(self, message="user-defined validation"):
        @dataclass
        class Fake(DataclassResponse):
            def __post_init__(self):
                raise ValueError(message)

        with pytest.raises(ValueError, match=message):
            Fake()

    @pytest.mark.parametrize(
        "kwargs",
        [
            pytest.param({}, id="nones"),
            pytest.param({"text": "str"}, id="text"),
            pytest.param({"number": 123}, id="number"),
        ],
    )
    def test_skips_nones(self, kwargs: dict):
        @dataclass
        class Fake(DataclassResponse):
            class Meta:
                serialize_nones = False

            text: str | None = None
            number: int | None = None

        expected = kwargs.copy()
        assert Fake(**kwargs).to_dict() == expected


@dataclass(kw_only=True)
class Member:
    name: str


@dataclass(kw_only=True)
class MemberList:
    members: list[Member] = field(default_factory=list)


@dataclass(kw_only=True)
class Paging:
    current: int = 1
    total: int = 1
    next: int | None = None
    prev: int | None = None


@dataclass(kw_only=True)
class FakeResponse(DataclassResponse):
    data: MemberList = field(default_factory=MemberList)
    paging: Paging = field(default_factory=Paging)


class TestDataclassResponseExample:
    def test_creating_response_with_wrong_argument_fails(self):
        with pytest.raises(TypeError):
            FakeResponse(payload={"average": "1.23"})

    @pytest.mark.xfail(reason="no runtime typechecking", strict=True)
    def test_creating_response_with_wrong_argument_type_fails(self):
        # TODO validate types during init and assignment
        # This needs extra implementation as dataclasses DO NOT perform
        # type checking. This relies on a type checker which we don't have
        # in Core. However, we can implement runtime type checking our own.
        class OtherMemberList:
            pass

        with pytest.raises(TypeError):
            FakeResponse(data=OtherMemberList())
