from collections.abc import Generator
from datetime import date, datetime, timezone
from decimal import Decimal
from unittest.mock import patch

import pytest
from freezegun import freeze_time
from mock.mock import Mock

import settings
from webapps.stats_and_reports.v2.app.staffer_stats_repository import StafferReportQuery
from webapps.stats_and_reports.v2.domain.periods import (
    Period,
    PeriodDaily,
    PeriodWeekly,
    PeriodMonthly,
    PeriodYearly,
)
from webapps.stats_and_reports.v2.domain.types import (
    Business,
    Staffer,
)
from webapps.stats_and_reports.v2.domain.reports.staffer_stats import (
    StafferStats,
    Stats,
    RevenueAndCommissionsDetails,
    Reviews,
    RevenueAndCommissions,
    Tips,
)
from webapps.stats_and_reports.v2.infra.errors import (
    FirestoreClientIsNotInitializedError,
    FirestoreDocumentCannotBeDeserializedError,
)
from webapps.stats_and_reports.v2.infra.staffer_stats_repository import (
    FirestoreStafferStatsRepository,
)


class TestStafferStatsRepository:
    @staticmethod
    @pytest.fixture
    def mock_firestore_factory() -> Generator[Mock]:
        modpath = "webapps.stats_and_reports.v2.infra.staffer_stats_repository"
        with patch(f"{modpath}.FirestoreClientImpl") as mock:
            yield mock

    @staticmethod
    @pytest.fixture
    def fake_settings(monkeypatch: pytest.MonkeyPatch) -> None:
        for setting, value in (
            ("STATS_AND_REPORTS_V2_GCP_FIRESTORE_PROJECT_ID", "proj-id"),
            ("STATS_AND_REPORTS_V2_GCP_FIRESTORE_COLLECTION_NAME", "collection"),
        ):
            monkeypatch.setattr(settings.local, setting, value)

    @staticmethod
    @pytest.fixture
    def repo(
        fake_settings,
        mock_firestore_factory: Mock,
    ) -> FirestoreStafferStatsRepository:
        return FirestoreStafferStatsRepository()

    @freeze_time(datetime(2023, 2, 3, 10, tzinfo=timezone.utc))
    def test_repository_returns_staffer_stats_document(self, monkeypatch):
        firestore_client_mock = Mock()
        staffer_stats_repository: FirestoreStafferStatsRepository = (
            FirestoreStafferStatsRepository()
        )
        monkeypatch.setattr(staffer_stats_repository, "_firestore_client", firestore_client_mock)

        staffer_id: int = 42
        business_id: int = 1

        firestore_client_mock.get_report.return_value = {
            "staffer_id": staffer_id,
            "service_gross_revenue": 1.0,
            "service_commission": 2.0,
            "addon_gross_revenue": 3.0,
            "addon_commission": 4.0,
            "product_gross_revenue": 5.0,
            "product_commission": 6.0,
            "gift_card_gross_revenue": 7.0,
            "gift_card_commission": 8.0,
            "package_gross_revenue": 9.0,
            "package_commission": 10.0,
            "membership_gross_revenue": 11.0,
            "membership_commission": 12.0,
            "total_commission": 13.0,
            "total_gross_revenue": 14.0,
            "tips_count": None,
            "tips_average": None,
            "tips_amount": 15.0,
            "reviews_count": 16,
            "reviews_rank_average": 17.0,
            "calendar_occupancy": 18,
        }

        staffer_stats = staffer_stats_repository.get(
            StafferReportQuery(
                name="staffer_stats",
                business=Business(identity=business_id),
                staffer=Staffer(identity=staffer_id),
                period=PeriodDaily(datetime.now(timezone.utc).date()),
            )
        )
        assert isinstance(staffer_stats, StafferStats) and staffer_stats == StafferStats(
            staffer_id=42,
            stats=Stats(
                revenue_and_commissions=RevenueAndCommissionsDetails(
                    services=RevenueAndCommissions(
                        revenue=Decimal("1.0"), commission=Decimal("2.0")
                    ),
                    add_ons=RevenueAndCommissions(
                        revenue=Decimal("3.0"), commission=Decimal("4.0")
                    ),
                    products=RevenueAndCommissions(
                        revenue=Decimal("5.0"), commission=Decimal("6.0")
                    ),
                    gift_cards=RevenueAndCommissions(
                        revenue=Decimal("7.0"), commission=Decimal("8.0")
                    ),
                    packages=RevenueAndCommissions(
                        revenue=Decimal("9.0"), commission=Decimal("10.0")
                    ),
                    membership=RevenueAndCommissions(
                        revenue=Decimal("11.0"), commission=Decimal("12.0")
                    ),
                    total_commission=Decimal('13.00'),
                    total_revenue=Decimal('14.00'),
                ),
                tips=Tips(number=None, average=None, value=Decimal("15.00")),
                reviews=Reviews(number=16, average=Decimal('17.0')),
                calendar_occupancy=18,
            ),
        )

    @freeze_time(datetime(2023, 2, 3, 10, tzinfo=timezone.utc))
    def test_repository_returns_staffer_stats_document_with_zeros(self, monkeypatch):
        firestore_client_mock = Mock()
        staffer_stats_repository: FirestoreStafferStatsRepository = (
            FirestoreStafferStatsRepository()
        )
        monkeypatch.setattr(staffer_stats_repository, "_firestore_client", firestore_client_mock)

        staffer_id: int = 42
        business_id: int = 1

        firestore_client_mock.get_report.return_value = {
            "staffer_id": staffer_id,
            "service_gross_revenue": 0.0,
            "service_commission": 0.0,
            "addon_gross_revenue": 0.0,
            "addon_commission": 0.0,
            "product_gross_revenue": 0.0,
            "product_commission": 0.0,
            "gift_card_gross_revenue": 0.0,
            "gift_card_commission": 0.0,
            "package_gross_revenue": 0.0,
            "package_commission": 0.0,
            "membership_gross_revenue": 0.0,
            "membership_commission": 0.0,
            "total_commission": 0.0,
            "total_gross_revenue": 0.0,
            "tips_count": 0.0,
            "tips_average": 0.0,
            "tips_amount": 0.0,
            "reviews_count": 0,
            "reviews_rank_average": 0.0,
            "calendar_occupancy": 0,
        }

        staffer_stats = staffer_stats_repository.get(
            StafferReportQuery(
                name="staffer_stats",
                business=Business(identity=business_id),
                staffer=Staffer(identity=staffer_id),
                period=PeriodDaily(datetime.now(timezone.utc).date()),
            )
        )

        assert isinstance(staffer_stats, StafferStats) and staffer_stats == StafferStats(
            staffer_id=42,
            stats=Stats(
                revenue_and_commissions=RevenueAndCommissionsDetails(
                    services=RevenueAndCommissions(
                        revenue=Decimal("0.0"), commission=Decimal("0.0")
                    ),
                    add_ons=RevenueAndCommissions(
                        revenue=Decimal("0.0"), commission=Decimal("0.0")
                    ),
                    products=RevenueAndCommissions(
                        revenue=Decimal("0.0"), commission=Decimal("0.0")
                    ),
                    gift_cards=RevenueAndCommissions(
                        revenue=Decimal("0.0"), commission=Decimal("0.0")
                    ),
                    packages=RevenueAndCommissions(
                        revenue=Decimal("0.0"), commission=Decimal("0.0")
                    ),
                    membership=RevenueAndCommissions(
                        revenue=Decimal("0.0"), commission=Decimal("0.0")
                    ),
                    total_commission=Decimal('0.00'),
                    total_revenue=Decimal('0.00'),
                ),
                tips=Tips(number=0, average=Decimal("0.0"), value=Decimal("0.00")),
                reviews=Reviews(number=0, average=Decimal('0.0')),
                calendar_occupancy=0,
            ),
        )

    @freeze_time(datetime(2023, 2, 3, 10, tzinfo=timezone.utc))
    def test_repository_returns_raises_an_exception_for_empty_document(self, monkeypatch):
        staffer_stats_repository: FirestoreStafferStatsRepository = (
            FirestoreStafferStatsRepository()
        )
        firestore_client_mock = Mock()
        monkeypatch.setattr(staffer_stats_repository, "_firestore_client", firestore_client_mock)

        staffer_id: int = 42
        business_id: int = 1

        firestore_client_mock.get_report.return_value = {}

        with pytest.raises(FirestoreDocumentCannotBeDeserializedError):
            staffer_stats_repository.get(
                StafferReportQuery(
                    name="staffer_stats",
                    business=Business(identity=business_id),
                    staffer=Staffer(identity=staffer_id),
                    period=PeriodDaily(datetime.now(timezone.utc).date()),
                )
            )

    @freeze_time(datetime(2023, 2, 3, 10, tzinfo=timezone.utc))
    def test_repository_returns_raises_an_exception_for_document_without_stats(self, monkeypatch):
        firestore_client_mock = Mock()
        staffer_stats_repository: FirestoreStafferStatsRepository = (
            FirestoreStafferStatsRepository()
        )
        monkeypatch.setattr(staffer_stats_repository, "_firestore_client", firestore_client_mock)

        staffer_id: int = 42
        business_id: int = 1

        firestore_client_mock.get_report.return_value = {
            "staffer_id": staffer_id,
        }

        staffer_stats = staffer_stats_repository.get(
            StafferReportQuery(
                name="staffer_stats",
                business=Business(identity=business_id),
                staffer=Staffer(identity=staffer_id),
                period=PeriodDaily(datetime.now(timezone.utc).date()),
            )
        )
        assert staffer_stats == StafferStats(
            staffer_id=staffer_id,
            stats=Stats(
                revenue_and_commissions=RevenueAndCommissionsDetails(
                    services=RevenueAndCommissions(),
                    add_ons=RevenueAndCommissions(),
                    products=RevenueAndCommissions(),
                    gift_cards=RevenueAndCommissions(),
                    packages=RevenueAndCommissions(),
                    membership=RevenueAndCommissions(),
                    total_commission=None,
                    total_revenue=None,
                ),
                tips=Tips(),
                reviews=Reviews(),
                calendar_occupancy=None,
            ),
        )

    def test_repository_not_create_firestore_client_when_project_id_not_provided(self, monkeypatch):
        monkeypatch.setattr(settings.local, "STATS_AND_REPORTS_V2_GCP_FIRESTORE_PROJECT_ID", None)
        assert (
            FirestoreStafferStatsRepository()._firestore_client  # pylint: disable=protected-access
            is None
        )

    def test_repository_not_create_firestore_client_when_collection_name_not_provided(
        self, monkeypatch
    ):
        monkeypatch.setattr(
            settings.local, "STATS_AND_REPORTS_V2_GCP_FIRESTORE_PROJECT_ID", 'some-project-id'
        )
        monkeypatch.setattr(
            settings.local, "STATS_AND_REPORTS_V2_GCP_FIRESTORE_COLLECTION_NAME", None
        )

        assert (
            FirestoreStafferStatsRepository()._firestore_client  # pylint: disable=protected-access
            is None
        )

    def test_staffer_stats_repository_returns_none_for_non_existing_document(self, monkeypatch):
        staffer_stats_repository = FirestoreStafferStatsRepository()
        firestore_client_mock = Mock()
        monkeypatch.setattr(staffer_stats_repository, "_firestore_client", firestore_client_mock)
        firestore_client_mock.get_report.return_value = None

        staffer_stats = staffer_stats_repository.get(
            StafferReportQuery(
                name="staffer_stats",
                business=Business(identity=1),
                staffer=Staffer(identity=32),
                period=PeriodDaily(date(2025, 1, 1)),
            )
        )
        assert staffer_stats is None

    def test_repository_get_raise_exception_when_firestore_client_is_not_set(self, monkeypatch):
        monkeypatch.setattr(settings.local, "STATS_AND_REPORTS_V2_GCP_FIRESTORE_PROJECT_ID", None)
        monkeypatch.setattr(
            settings.local, "STATS_AND_REPORTS_V2_GCP_FIRESTORE_COLLECTION_NAME", None
        )
        staffer_stats_repository = FirestoreStafferStatsRepository()
        with pytest.raises(FirestoreClientIsNotInitializedError):
            staffer_stats_repository.get(
                StafferReportQuery(
                    name="staffer_stats",
                    business=Business(identity=1),
                    staffer=Staffer(identity=42),
                    period=PeriodDaily(datetime.now(timezone.utc).date()),
                )
            )

    @pytest.mark.parametrize(
        "period,report_key",
        [
            (
                PeriodDaily(date(2025, 4, 17)),
                "reports__business__413261__staffer__305516__2025__04__17__staffer_stats__daily",
            ),
            (
                PeriodWeekly(date(2025, 4, 13)),
                "reports__business__413261__staffer__305516__2025__15__staffer_stats__weekly",
            ),
            (
                PeriodMonthly(date(2025, 4, 1)),
                "reports__business__413261__staffer__305516__2025__04__staffer_stats__monthly",
            ),
            (
                PeriodYearly(date(2025, 1, 1)),
                "reports__business__413261__staffer__305516__2025__staffer_stats__yearly",
            ),
        ],
    )
    def test_gets_staffer_stats_by_proper_key(
        self,
        repo: FirestoreStafferStatsRepository,
        mock_firestore_factory: Mock,
        period: Period,
        report_key: str,
        business_id: int = 413261,
        staffer_id: int = 305516,
        current_time: datetime = datetime(2025, 4, 17, 15, 32, tzinfo=timezone.utc),
    ):
        with (
            freeze_time(current_time),
            pytest.raises(FirestoreDocumentCannotBeDeserializedError),
        ):
            repo.get(
                StafferReportQuery(
                    name="staffer_stats",
                    business=Business(identity=business_id),
                    staffer=Staffer(identity=staffer_id),
                    period=period,
                )
            )

        mock_firestore_factory().get_report.assert_called_once_with(key=report_key)
