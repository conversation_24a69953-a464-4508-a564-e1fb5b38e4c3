import json
from datetime import date, datetime, timezone
from decimal import Decimal
from unittest.mock import ANY, MagicMock
from unittest import mock
from urllib.parse import urlencode

import pytest
from django.urls import reverse
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from lib.feature_flag.feature.stats_and_reports_v2 import StatsAndReportsV2StafferStats
from lib.tests.utils import override_eppo_feature_flag
from webapps.business.adapters import AccessControlAdapter
from webapps.business.models import Resource
from webapps.stats_and_reports.v2.app.staffer_stats_service import StafferStatsService
from webapps.stats_and_reports.v2.domain.period_type import PeriodType
from webapps.stats_and_reports.v2.domain.periods import (
    Period,
    PeriodDaily,
    PeriodWeekly,
    PeriodMonthly,
    PeriodYearly,
)
from webapps.stats_and_reports.v2.domain.range_policy import FixedRange
from webapps.stats_and_reports.v2.domain.reports.staffer_stats import (
    Stats,
    RevenueAndCommissionsDetails,
    Reviews,
    RevenueAndCommissions,
    StafferStats,
    Tips,
)
from webapps.stats_and_reports.v2.infra.errors import (
    FirestoreClientIsNotInitializedError,
)
from webapps.stats_and_reports.v2.infra.staffer_stats_view import GetResponse

from webapps.user.models import User

staffer_stats: StafferStats = StafferStats(
    staffer_id=42,
    stats=Stats(
        revenue_and_commissions=RevenueAndCommissionsDetails(
            services=RevenueAndCommissions(),
            add_ons=RevenueAndCommissions(),
            products=RevenueAndCommissions(),
            gift_cards=RevenueAndCommissions(),
            packages=RevenueAndCommissions(),
            membership=RevenueAndCommissions(),
            total_revenue=Decimal('42.42'),
            total_commission=Decimal('4242.4242'),
        ),
        tips=Tips(
            number=42,
            average=Decimal('1.1'),
            value=Decimal('4.4'),
        ),
        reviews=Reviews(),
        calendar_occupancy=0,
    ),
)


class TestGetResponse:
    @pytest.fixture
    @staticmethod
    def staffer_stats_full() -> StafferStats:
        revenue_and_commissions_details = RevenueAndCommissionsDetails(
            services=RevenueAndCommissions(revenue=Decimal('0.1'), commission=Decimal('20.00')),
            add_ons=RevenueAndCommissions(revenue=Decimal('50.00'), commission=Decimal('10.00')),
            products=RevenueAndCommissions(revenue=Decimal('200.00'), commission=Decimal('30.00')),
            gift_cards=RevenueAndCommissions(revenue=Decimal('25.00'), commission=Decimal('2.50')),
            packages=RevenueAndCommissions(revenue=Decimal('75.00'), commission=Decimal('15.00')),
            membership=RevenueAndCommissions(
                revenue=Decimal('150.00'), commission=Decimal('22.50')
            ),
            total_revenue=Decimal('600.00'),
            total_commission=Decimal(100.00),
        )
        tips = Tips(number=10, average=Decimal("15.00"))
        reviews = Reviews(number=5, average=Decimal("4.5"))
        stats = Stats(
            calendar_occupancy=50,
            revenue_and_commissions=revenue_and_commissions_details,
            tips=tips,
            reviews=reviews,
        )
        return StafferStats(staffer_id=123, stats=stats)

    def test_serialize_staffer_stats(self, staffer_stats_full: StafferStats):
        sut = GetResponse(data=staffer_stats_full)
        assert sut.to_dict() == {
            "data": {
                "staffer_id": 123,
                "stats": {
                    "calendar_occupancy": 50,
                    "revenue_and_commissions": {
                        "services": {
                            "revenue": "0.10",
                            "commission": "20.00",
                        },
                        "add_ons": {
                            "revenue": "50.00",
                            "commission": "10.00",
                        },
                        "products": {
                            "revenue": "200.00",
                            "commission": "30.00",
                        },
                        "gift_cards": {
                            "revenue": "25.00",
                            "commission": "2.50",
                        },
                        "packages": {
                            "revenue": "75.00",
                            "commission": "15.00",
                        },
                        "membership": {
                            "revenue": "150.00",
                            "commission": "22.50",
                        },
                        "total_revenue": "600.00",
                        "total_commission": "100.00",
                    },
                    "tips": {
                        "number": 10,
                        "average": "15.00",
                        "value": None,
                    },
                    "reviews": {
                        "number": 5,
                        "average": "4.50",
                    },
                },
            },
        }

    def test_serialize_staffer_stats_with_nones(self):
        stats = Stats(revenue_and_commissions=None, tips=None, reviews=None, calendar_occupancy=10)
        staffer_stats = StafferStats(staffer_id=123, stats=stats)
        sut = GetResponse(data=staffer_stats)
        assert sut.to_dict() == {
            "data": {
                "staffer_id": 123,
                "stats": {
                    "calendar_occupancy": 10,
                    "tips": None,
                    "reviews": None,
                    "revenue_and_commissions": None,
                },
            },
        }

    def test_serialize_staffer_stats_with_defaults(self):
        revenue_and_commissions_details = RevenueAndCommissionsDetails(
            services=RevenueAndCommissions(),
            add_ons=None,
            products=None,
            gift_cards=None,
            packages=None,
            membership=None,
            total_revenue=Decimal(42.0),
            total_commission=None,
        )
        stats = Stats(
            revenue_and_commissions=revenue_and_commissions_details,
            tips=None,
            reviews=None,
            calendar_occupancy=24,
        )
        staffer_stats = StafferStats(staffer_id=123, stats=stats)
        sut = GetResponse(data=staffer_stats)
        assert sut.to_dict() == {
            "data": {
                "staffer_id": 123,
                "stats": {
                    "calendar_occupancy": 24,
                    "revenue_and_commissions": {
                        "services": {
                            "revenue": None,
                            "commission": None,
                        },
                        "add_ons": None,
                        "products": None,
                        "gift_cards": None,
                        "packages": None,
                        "membership": None,
                        "total_revenue": "42.00",
                        "total_commission": None,
                    },
                    "tips": None,
                    "reviews": None,
                },
            },
        }

    def test_includes_metadata(self, staffer_stats_full: StafferStats):
        period = mock.Mock(spec_set=Period, value=(current := "period-value"))
        mock.seal(period)

        sut = GetResponse(
            data=staffer_stats_full,
            metadata=GetResponse.Metadata(current=period),
        )

        assert sut.to_dict() == {"data": ANY, "metadata": {"current": current}}

    def test_includes_metadata_with_next(self, staffer_stats_full: StafferStats):
        period_current = mock.Mock(spec_set=Period, value=(current := "period-current"))
        period_next = mock.Mock(spec_set=Period, value=(next := "period-next"))
        for m in (period_current, period_next):
            mock.seal(m)

        sut = GetResponse(
            data=staffer_stats_full,
            metadata=GetResponse.Metadata(current=period_current, next=period_next),
        )

        assert sut.to_dict() == {
            "data": ANY,
            "metadata": {"current": current, "next": next},
        }

    def test_includes_metadata_with_prev(self, staffer_stats_full: StafferStats):
        period_current = mock.Mock(spec_set=Period, value=(current := "period-current"))
        period_prev = mock.Mock(spec_set=Period, value=(prev := "period-prev"))
        for m in (period_current, period_prev):
            mock.seal(m)

        sut = GetResponse(
            data=staffer_stats_full,
            metadata=GetResponse.Metadata(current=period_current, prev=period_prev),
        )

        assert sut.to_dict() == {
            "data": ANY,
            "metadata": {"current": current, "prev": prev},
        }

    def test_metadata_requires_current(self, staffer_stats_full: StafferStats):
        with pytest.raises(TypeError):
            GetResponse(
                data=staffer_stats_full,
                metadata=GetResponse.Metadata(),
            )


@override_eppo_feature_flag({StatsAndReportsV2StafferStats.flag_name: True})
@pytest.mark.django_db
class TestStafferStatsView(BaseBusinessApiTestCase):
    time_current = datetime(2025, 5, 30, 7, 29)
    time_current_day = time_current.date()
    time_current_week = date(2025, 5, 25)
    time_current_month = date(2025, 5, 1)
    time_current_year = date(2025, 1, 1)

    range_policy_no_next = FixedRange(till=time_current_day)
    range_policy_no_prev = FixedRange(since=time_current_year)

    @classmethod
    def setUpTestData(cls):
        cls.business = baker.make_recipe(
            'webapps.business.business_recipe',
            time_zone_name="America/New_York",
        )
        staffer_user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='987654321',
        )
        cls.staffer = baker.make(
            Resource,
            business=cls.business,
            staff_user=staffer_user,
            staff_email='<EMAIL>',
            active=True,
            visible=True,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
        )
        cls.appliance = baker.make(
            Resource,
            business=cls.business,
            type=Resource.APPLIANCE,
        )
        cls.user = cls.business.owner

        cls.staffer_stats_repository_mock = MagicMock()
        cls.staffer_stats_repository_mock.get.return_value = staffer_stats

        cls.staffer_stats_service_mock = StafferStatsService(
            staffer_stats_repository=cls.staffer_stats_repository_mock,
            access_control_port=AccessControlAdapter(),
        )

    def setUp(self):
        super().setUp()
        self.enterContext(
            mock.patch(
                target=(
                    'webapps.stats_and_reports.v2.infra.staffer_stats_view' '.StafferStatsService'
                ),
                return_value=self.staffer_stats_service_mock,
            )
        )

    @parameterized.expand(
        [
            # TODO PET-1952 add test cases with different "period" values
            (None, "2025-05-29", "2025-05-30", None),
            (PeriodType.DAILY, "2025-05-29", "2025-05-30", None),
            (PeriodType.WEEKLY, "2025-20", "2025-21", None),
            (PeriodType.MONTHLY, "2025-04", "2025-05", None),
            (PeriodType.YEARLY, None, "2025", None),
        ],
        name_func=lambda func, no, param: "_".join(
            [func.__name__, str(no), getattr(param.args[0], "value", "none")]
        ),
    )
    def test_staffer_stats_view_returns_dummy_stats(
        self,
        period_type: PeriodType | None,
        metadata_prev: str | None,
        metadata_current: str,
        metadata_next: str | None,
    ):
        url = reverse(
            'staffer_stats',
            kwargs={'business_pk': self.business.id, "staffer_pk": self.staffer.id},
        )
        params = "?" + urlencode({"period_type": period_type.value}) if period_type else ""

        with freeze_time(self.time_current):
            resp = self.client.get(f"{url}{params}")

        assert resp.status_code == 200 and json.loads(resp.content) == {
            "data": {
                "staffer_id": 42,
                "stats": {
                    "revenue_and_commissions": {
                        "services": {"revenue": None, "commission": None},
                        "add_ons": {"revenue": None, "commission": None},
                        "products": {"revenue": None, "commission": None},
                        "gift_cards": {"revenue": None, "commission": None},
                        "packages": {"revenue": None, "commission": None},
                        "membership": {"revenue": None, "commission": None},
                        "total_commission": "4242.42",
                        "total_revenue": "42.42",
                    },
                    "tips": {"number": 42, "average": "1.10", "value": '4.40'},
                    "reviews": {"number": None, "average": None},
                    "calendar_occupancy": 0,
                },
            },
            "metadata": {
                "current": metadata_current,
                **({"next": metadata_next} if metadata_next is not None else {}),
                **({"prev": metadata_prev} if metadata_prev is not None else {}),
            },
        }

    def test_staffer_stats_view_returns_not_found_for_non_existing_staffer(self):
        url = reverse(
            'staffer_stats', kwargs={'business_pk': self.business.id, "staffer_pk": 432523}
        )
        response = self.client.get(url)
        assert response.status_code == 404

    def test_staffer_stats_view_returns_not_found_for_appliance_resource(self):
        url = reverse(
            'staffer_stats',
            kwargs={'business_pk': self.business.id, "staffer_pk": self.appliance.id},
        )
        response = self.client.get(url)
        assert response.status_code == 404

    def test_staffer_stats_view_returns_service_unavailable_when_firestore_client_is_set_to_none(
        self,
    ):
        self.staffer_stats_repository_mock.get.side_effect = FirestoreClientIsNotInitializedError(
            "test"
        )
        url = reverse(
            'staffer_stats',
            kwargs={'business_pk': self.business.id, "staffer_pk": self.staffer.id},
        )
        response = self.client.get(url)
        assert response.status_code == 503

    @override_eppo_feature_flag({StatsAndReportsV2StafferStats.flag_name: False})
    def test_staffer_stats_view_returns_not_implemented_when_feature_flag_off(self):
        url = reverse(
            'staffer_stats',
            kwargs={'business_pk': self.business.id, "staffer_pk": self.staffer.id},
        )
        response = self.client.get(url)
        assert response.status_code == 501

    @parameterized.expand(
        [
            (PeriodType.DAILY, "2024__12__31"),
            (PeriodType.WEEKLY, "2024__52"),
            (PeriodType.MONTHLY, "2024__12"),
            (PeriodType.YEARLY, "2024"),
        ]
    )
    @pytest.mark.xfail(msg="broken")  # TODO
    def test_selects_period_accounting_for_business_timezone(
        self,
        period_type: PeriodType,
        period_id: str,
    ):
        time_few_minutes_in_new_year_utc = datetime(2025, 1, 1, 0, 2, 21, tzinfo=timezone.utc)
        url = reverse(
            'staffer_stats',
            kwargs={
                'business_pk': self.business.id,
                "staffer_pk": self.staffer.id,
            },
        )
        with freeze_time(time_few_minutes_in_new_year_utc):
            self.client.get(f"{url}?period_type={period_type.value}")
        assert self.staffer_stats_repository_mock.get.call_args[0][0].period.identity == period_id

    def test_selects_period_by_query_param_wrong_format(
        self,
    ):
        url = reverse(
            'staffer_stats',
            kwargs={
                'business_pk': self.business.id,
                "staffer_pk": self.staffer.id,
            },
        )
        response = self.client.get(
            f"{url}?period_type={PeriodType.MONTHLY.value}&period=2025-10-10"
        )
        assert response.status_code == 400

    @parameterized.expand(
        [
            PeriodDaily(time_current_day, range_policy=range_policy_no_next),
            PeriodWeekly(time_current_week, range_policy=range_policy_no_next),
            PeriodMonthly(time_current_month, range_policy=range_policy_no_next),
            PeriodYearly(time_current_year, range_policy=range_policy_no_next),
        ]
    )
    def test_no_next_period_when_next_period_in_the_future(self, period: Period):
        url = reverse(
            'staffer_stats',
            kwargs={'business_pk': self.business.id, "staffer_pk": self.staffer.id},
        )
        with mock.patch(
            "webapps.stats_and_reports.v2.infra.staffer_stats_view.StafferStatsService",
            spec_set=StafferStatsService,
        ) as mock_service:
            mock_service().create_period.return_value = period
            mock_service().get_report.return_value = staffer_stats
            resp = self.client.get(f"{url}?period_type={period.name.value}")

        assert resp.status_code == 200 and json.loads(resp.content) == {
            "data": ANY,
            "metadata": {"current": ANY, "prev": ANY},
        }

    @parameterized.expand(
        [
            PeriodDaily(time_current_year, range_policy=range_policy_no_prev),
            PeriodWeekly(time_current_year.replace(day=5), range_policy=range_policy_no_prev),
            PeriodMonthly(time_current_year, range_policy=range_policy_no_prev),
            PeriodYearly(time_current_year, range_policy=range_policy_no_prev),
        ]
    )
    def test_no_previous_period_when_previous_period_out_of_range(self, period: Period):
        url = reverse(
            'staffer_stats',
            kwargs={'business_pk': self.business.id, "staffer_pk": self.staffer.id},
        )
        with mock.patch(
            "webapps.stats_and_reports.v2.infra.staffer_stats_view.StafferStatsService",
            spec_set=StafferStatsService,
        ) as mock_service:
            mock_service().create_period.return_value = period
            mock_service().get_report.return_value = staffer_stats
            resp = self.client.get(f"{url}?period_type={period.name.value}")

        assert resp.status_code == 200 and json.loads(resp.content) == {
            "data": ANY,
            "metadata": {"current": ANY, "next": ANY},
        }


@override_eppo_feature_flag({StatsAndReportsV2StafferStats.flag_name: True})
@pytest.mark.django_db
class TestStafferStatsViewAccessControlStaffer(BaseBusinessApiTestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = baker.make_recipe('webapps.business.business_recipe')
        staffer_user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='987654321',
        )
        cls.staffer = baker.make(
            Resource,
            business=cls.business,
            staff_user=staffer_user,
            staff_email='<EMAIL>',
            active=True,
            visible=True,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
        )
        cls.user = staffer_user
        staffer_stats_repository_mock = MagicMock()
        staffer_stats_repository_mock.get.return_value = staffer_stats

        cls.staffer_stats_service_mock = StafferStatsService(
            staffer_stats_repository=staffer_stats_repository_mock,
            access_control_port=AccessControlAdapter(),
        )

    def setUp(self):
        super().setUp()
        self.enterContext(
            mock.patch(
                target=(
                    'webapps.stats_and_reports.v2.infra.staffer_stats_view' '.StafferStatsService'
                ),
                return_value=self.staffer_stats_service_mock,
            )
        )

    def test_staffer_stats_view_access_control_returns_not_found_for_access_staffer(self):
        url = reverse(
            'staffer_stats',
            kwargs={'business_pk': self.business.id, "staffer_pk": self.staffer.id},
        )
        response = self.client.get(url)
        assert response.status_code == 404


@override_eppo_feature_flag({StatsAndReportsV2StafferStats.flag_name: True})
@pytest.mark.django_db
class TestStafferStatsViewAccessControlManager(BaseBusinessApiTestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = baker.make_recipe('webapps.business.business_recipe')
        staffer_user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='987654321',
        )
        manager_user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='987654322',
        )
        cls.staffer = baker.make(
            Resource,
            business=cls.business,
            staff_user=staffer_user,
            staff_email='<EMAIL>',
            active=True,
            visible=True,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
        )
        baker.make(
            Resource,
            business=cls.business,
            staff_user=manager_user,
            staff_email='<EMAIL>',
            active=True,
            visible=True,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )
        cls.user = manager_user
        cls.staffer_stats_repository_mock = MagicMock()

        cls.url = reverse(
            'staffer_stats',
            kwargs={'business_pk': cls.business.id, "staffer_pk": cls.staffer.id},
        )

    def setUp(self):
        super().setUp()

        self.staffer_stats_service_mock = StafferStatsService(
            staffer_stats_repository=self.staffer_stats_repository_mock,
            access_control_port=AccessControlAdapter(),
        )
        self.staffer_stats_repository_mock.get.return_value = staffer_stats
        self.enterContext(
            mock.patch(
                target=(
                    'webapps.stats_and_reports.v2.infra.staffer_stats_view' '.StafferStatsService'
                ),
                return_value=self.staffer_stats_service_mock,
            )
        )

    def test_staffer_stats_view_access_control_returns_ok_for_manager_staffer(self):
        response = self.client.get(self.url)
        assert response.status_code == 200

    def test_staffer_stats_view_returns_not_found_for_non_existing_document(self):
        self.staffer_stats_repository_mock.get.return_value = None
        response = self.client.get(self.url)
        assert response.status_code == 404

    @parameterized.expand(
        [
            ("", 200),
            ("   ", 400),
            ("dummy", 400),
            ("biweekly", 400),
            *[(period.value.lower(), 200) for period in PeriodType],
            *[(period.value.upper(), 400) for period in PeriodType],
        ]
    )
    def test_staffer_stats_view_requires_valid_period(self, period: str, resp_status_code: int):
        params = urlencode({"period_type": period})
        response = self.client.get(f"{self.url}?{params}")
        assert response.status_code == resp_status_code


@override_eppo_feature_flag({StatsAndReportsV2StafferStats.flag_name: True})
@pytest.mark.django_db
class TestStafferStatsViewAccessControlOwnerOtherBusiness(BaseBusinessApiTestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = baker.make_recipe('webapps.business.business_recipe')
        other_business = baker.make_recipe('webapps.business.business_recipe')
        owner_other_business = other_business.owner
        staffer_user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='987654321',
        )
        cls.staffer = baker.make(
            Resource,
            business=cls.business,
            staff_user=staffer_user,
            staff_email='<EMAIL>',
            active=True,
            visible=True,
            type=Resource.STAFF,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
        )
        cls.user = owner_other_business
        staffer_stats_repository_mock = MagicMock()
        staffer_stats_repository_mock.get.return_value = staffer_stats

        cls.staffer_stats_service_mock = StafferStatsService(
            staffer_stats_repository=staffer_stats_repository_mock,
            access_control_port=AccessControlAdapter(),
        )

    def test_staffer_stats_view_access_control_returns_not_found_for_owner_other_business(self):
        with mock.patch(
            target=('webapps.stats_and_reports.v2.infra.staffer_stats_view' '.StafferStatsService'),
            return_value=self.staffer_stats_service_mock,
        ):
            url = reverse(
                'staffer_stats',
                kwargs={'business_pk': self.business.id, "staffer_pk": self.staffer.id},
            )
            response = self.client.get(url)
            assert response.status_code == 404
