from typing import Union

from lib.feature_flag.feature import CancellationFeeFullAmountAuthorizationFlag
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.entities import BasketPaymentEntity
from lib.point_of_sale.enums import BasketPaymentType, PaymentMethodType
from webapps.point_of_sale.models import BasketPayment, CancellationFeeAuth
from webapps.stripe_integration.enums import StripePayoutStatus, StripeWebhookEvents
from webapps.stripe_integration.events import stripe_payout_failed_event, stripe_payout_paid_event


class SynchronizeService:
    @staticmethod
    def synchronize_stripe_payment_intent(
        basket_payment: Union['BasketPayment', 'BasketPaymentEntity'],
    ):
        """
        :param basket_payment: BasketPayment(deprecated) or entity_object
        """

        from webapps.business.models import Business
        from webapps.point_of_sale.models import Basket
        from webapps.payment_providers.models import Payment
        from webapps.stripe_integration.models import StripePaymentIntent as StripePaymentIntentV1
        from webapps.stripe_integration.provider import StripeProvider
        from webapps.payment_gateway.models import BalanceTransaction
        from webapps.pos.models import PaymentRow

        if (
            basket_payment.payment_method not in PaymentMethodType.basket_online_payments()
            or basket_payment.payment_provider_code != PaymentProviderCode.STRIPE
            or basket_payment.type != BasketPaymentType.PAYMENT
        ):
            return

        basket = Basket.objects.get(id=basket_payment.basket_id)
        business = Business.objects.get(id=basket.business_id)
        pos = business.pos

        bt = BalanceTransaction.objects.get(id=basket_payment.balance_transaction_id)
        payment = Payment.objects.get(id=bt.external_id)
        payment_row = PaymentRow.objects.filter(basket_payment_id=basket_payment.id).last()

        intent_object, created = StripePaymentIntentV1.objects.get_or_create(
            external_id=payment.stripe_payment.external_id,
            account=pos.stripe_account,
            defaults={
                'terminal_identifier': None,  # Will be set in StripePaymentIntentHandler
                'terminal_device_type': None,  # Will be set in StripePaymentIntentHandler
            },
        )
        if created:
            intent_object.payment_rows.set([payment_row])
        splits = StripeProvider.calculate_splits(payment_row, pos.stripe_account)
        payment_row.payment_splits = splits
        payment_row.save(update_fields=['payment_splits'])

    @staticmethod
    def synchronize_stripe_payment_intent_from_cf(cf_auth: CancellationFeeAuth):
        from webapps.business.models import Business
        from webapps.payment_gateway.models import BalanceTransaction
        from webapps.payment_providers.models import Payment
        from webapps.pos.models import Transaction
        from webapps.stripe_integration.models import StripePaymentIntent as StripePaymentIntentV1

        business = Business.objects.get(id=cf_auth.business_id)
        pos = business.pos

        if (
            cf_auth.payment_provider_code != PaymentProviderCode.STRIPE
            or not CancellationFeeFullAmountAuthorizationFlag()
        ):
            return

        bt = BalanceTransaction.objects.get(id=cf_auth.balance_transaction_id)
        payment = Payment.objects.get(id=bt.external_id)

        txn = Transaction.objects.filter(cancellation_fee_auth_id=cf_auth.id).last()
        payment_row = txn.latest_receipt.payment_rows.get()

        intent_object, created = StripePaymentIntentV1.objects.get_or_create(
            external_id=payment.stripe_payment.external_id,
            account=pos.stripe_account,
            defaults={
                'terminal_identifier': None,  # Will be set in StripePaymentIntentHandler
                'terminal_device_type': None,  # Will be set in StripePaymentIntentHandler
            },
        )
        if created:
            intent_object.payment_rows.set([payment_row])

    @staticmethod
    def synchronize_stripe_payout(event_body: dict):
        from webapps.stripe_integration.provider import StripeProvider
        from webapps.stripe_integration.models import StripeAccount

        payout_data = event_body['data']['object']
        account_external_id = event_body['account']
        event_timestamp = event_body['created']
        event_type = event_body['type']
        stripe_account = StripeAccount.objects.get(external_id=account_external_id)

        payout_obj = StripeProvider.create_or_update_payout(
            payout_object=payout_data,
            stripe_account=stripe_account,
            event_created_timestamp=event_timestamp,
        )

        if (
            payout_data['status'] == StripePayoutStatus.FAILED
            and event_type == StripeWebhookEvents.PAYOUT_FAILED
        ):
            stripe_payout_failed_event.send(payout_obj)

        if (
            payout_data['status'] == StripePayoutStatus.PAID
            and event_type == StripeWebhookEvents.PAYOUT_PAID
        ):
            stripe_payout_paid_event.send(payout_obj)
