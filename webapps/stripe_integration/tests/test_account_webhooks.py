import json
from unittest.mock import patch

import mock
import responses
from django.urls import reverse
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status
from rest_framework.test import APITestCase
from rest_framework.views import Response

from webapps.business.models import Business
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import POS, PaymentRow, PaymentType, Receipt, Transaction
from webapps.payment_providers.models import Payment
from webapps.payment_providers.models.stripe import StripePaymentIntent as StripePaymentIntentV2
from webapps.stripe_integration.models import StripeAccount, StripePaymentIntent
from webapps.stripe_integration.tests.mocks import (
    mock_stripe_location_create,
    mock_stripe_webhook_construct_event,
)
from lib.feature_flag.feature.payment import KIPMOTOWebhookFlag
from lib.payments.enums import PaymentProviderCode
from lib.tests.utils import override_eppo_feature_flag


@mock_stripe_location_create
@mock_stripe_webhook_construct_event
class StripeAccountNotificationsHandlerTests(APITestCase):
    @property
    def url(self):
        return reverse("stripe_integration_webhook_account")

    @staticmethod
    def get_payment_initial_data(
        external_id,
        payment_intent,
        notification_type,
        last_payment_error=None,
        metadata=None,
        payment_method=None,
    ):
        if metadata is None:
            metadata = {}
        return {
            "id": external_id,
            "object": "event",
            "api_version": "2020-08-27",
            "created": **********,
            "data": {
                "object": {
                    "id": payment_intent,
                    "object": "payment_intent",
                    "amount": 500,
                    "amount_capturable": 0,
                    "amount_details": {"tip": {}},
                    "amount_received": 500,
                    "application": None,
                    "application_fee_amount": 23,
                    "automatic_payment_methods": None,
                    "canceled_at": None,
                    "cancellation_reason": None,
                    "capture_method": "manual",
                    "charges": {
                        "object": "list",
                        "data": [
                            {
                                "id": "ch_3LA40fH0H9FNnrB70hqwTGni",
                                "object": "charge",
                                "amount": 500,
                                "amount_captured": 500,
                                "amount_refunded": 0,
                                "application": None,
                                "application_fee": "test",
                                "application_fee_amount": 23,
                                "authorization_code": "837294",
                                "balance_transaction": "test",
                                "billing_details": {
                                    "address": {
                                        "city": None,
                                        "country": None,
                                        "line1": None,
                                        "line2": None,
                                        "postal_code": None,
                                        "state": None,
                                    },
                                    "email": None,
                                    "name": None,
                                    "phone": None,
                                },
                                "calculated_statement_descriptor": "TEST NAME",
                                "captured": True,
                                "created": 1655091182,
                                "currency": "usd",
                                "customer": "test",
                                "description": None,
                                "destination": "test",
                                "dispute": None,
                                "disputed": False,
                                "failure_balance_transaction": None,
                                "failure_code": None,
                                "failure_message": None,
                                "fraud_details": {},
                                "invoice": None,
                                "livemode": False,
                                "metadata": {},
                                "on_behalf_of": "test",
                                "order": None,
                                "outcome": {
                                    "network_status": "approved_by_network",
                                    "reason": None,
                                    "risk_level": "not_assessed",
                                    "seller_message": "Payment complete.",
                                    "type": "authorized",
                                },
                                "paid": True,
                                "payment_intent": payment_intent,
                                "payment_method": payment_method,
                                "payment_method_details": {
                                    "card_present": {
                                        "amount_authorized": 500,
                                        "brand": "visa",
                                        "capture_before": 1655263982,
                                        "cardholder_name": "TEST NAME",
                                        "country": "US",
                                        "emv_auth_data": "test",
                                        "exp_month": 3,
                                        "exp_year": 2026,
                                        "fingerprint": "test",
                                        "funding": "debit",
                                        "generated_card": "test",
                                        "incremental_authorization_supported": False,
                                        "last4": "4384",
                                        "network": "visa",
                                        "overcapture_supported": False,
                                        "read_method": "contact_emv",
                                        "receipt": {
                                            "account_type": "checking",
                                            "application_cryptogram": "test",
                                            "application_preferred_name": "VISA DEBIT",
                                            "authorization_code": "test",
                                            "authorization_response_code": "test",
                                            "cardholder_verification_method": "signature",
                                            "dedicated_file_name": "test",
                                            "terminal_verification_results": "test",
                                            "transaction_status_information": "test",
                                        },
                                    },
                                    "type": "card_present",
                                },
                                "receipt_email": None,
                                "receipt_number": None,
                                "receipt_url": "test_url",
                                "refunded": False,
                                "refunds": {
                                    "object": "list",
                                    "data": [],
                                    "has_more": False,
                                    "total_count": 0,
                                    "url": "test_url",
                                },
                                "review": None,
                                "shipping": None,
                                "source": None,
                                "source_transfer": None,
                                "statement_descriptor": None,
                                "statement_descriptor_suffix": None,
                                "status": "succeeded",
                                "transfer": "transfer",
                                "transfer_data": {
                                    "amount": None,
                                    "destination": "destination",
                                },
                                "transfer_group": "transfer_group",
                            }
                        ],
                        "has_more": False,
                        "total_count": 1,
                        "url": "test_url",
                    },
                    "client_secret": "client_secret",
                    "confirmation_method": "automatic",
                    "created": 1655091133,
                    "currency": "usd",
                    "customer": "test_customer",
                    "description": None,
                    "invoice": None,
                    "last_payment_error": last_payment_error,
                    "livemode": False,
                    "metadata": metadata,
                    "next_action": None,
                    "on_behalf_of": "acct_1L6hntQmqXxkB32h",
                    "payment_method": "pm_1LA41SH0H9FNnrB7sKPgxRxd",
                    "payment_method_options": {
                        "card_present": {
                            "request_extended_authorization": False,
                            "request_incremental_authorization_support": False,
                        }
                    },
                    "payment_method_types": ["card_present"],
                    "processing": None,
                    "receipt_email": None,
                    "review": None,
                    "setup_future_usage": None,
                    "shipping": None,
                    "source": None,
                    "statement_descriptor": None,
                    "statement_descriptor_suffix": None,
                    "status": "succeeded",
                    "total_details": {"amount_tax": 0, "amount_tip": None},
                    "transfer_data": {"destination": "acct_1L6hntQmqXxkB32h"},
                    "transfer_group": "group_pi_3LA40fH0H9FNnrB70SCpyTDB",
                }
            },
            "livemode": False,
            "pending_webhooks": 2,
            "request": {
                "id": "req_IX7UG1RBmne5At",
                "idempotency_key": "543e0597-a2d2-4a3c-8a51-076ca86bac05",
            },
            "type": notification_type,
        }

    def setUp(self, session_access_level=None):
        super().setUp()
        amount = 500
        self.business = baker.make(Business)
        self.pos = baker.make(POS, business=self.business)
        self.stripe_account = baker.make(StripeAccount, pos=self.pos)

        self.stripe = baker.make(
            PaymentType,
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self.pos,
        )
        self.trn = baker.make(Transaction, total=amount, pos=self.pos)
        self.receipt = baker.make(Receipt, transaction=self.trn)
        self.trn.latest_receipt = self.receipt
        self.trn.save()
        self.pr = baker.make(
            PaymentRow,
            amount=amount,
            payment_type=self.stripe,
            receipt=self.receipt,
            status=receipt_status.PENDING,
        )
        self.payment_intent = baker.make(
            StripePaymentIntent,
            external_id='pi_420',
            payment_rows=(self.pr,),
            account=self.stripe_account,
        )

    def test_payment_intent_succeeded_handler(
        self,
        _connection_token_mock,
        _location_mock,
    ):
        data = self.get_payment_initial_data(
            external_id='evt_0000',
            notification_type='payment_intent.succeeded',
            payment_intent='pi_420',
            metadata={'terminal_payment': 'True'},
        )
        data_with_terminal_order = self.get_payment_initial_data(
            external_id='wrong_evt',
            notification_type='payment_intent.succeeded',
            payment_intent='wrong_intent',
            metadata={'order_id': '1111', 'order_payment_id': '2222'},
        )

        resp = self.client.post(
            self.url,
            data=json.dumps(data_with_terminal_order),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        assert self.pr.status == receipt_status.PENDING
        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        assert self.payment_intent.payment_row.status == receipt_status.PAYMENT_SUCCESS

    @responses.activate
    @mock.patch(
        'webapps.stripe_integration.tools.stripe_payment_intent_idempotent_cancel',
        return_value=True,
    )
    def test_payment_intent_failed_handler(
        self,
        _connection_token_mock,
        _location_mock,
        _idempotent_cancel_mock,
    ):
        responses.add(
            responses.POST,
            'https://api.stripe.com/v1/payment_intents/pi_420/cancel',
            status=status.HTTP_200_OK,
            json={
                'status': 'canceled',
            },
        )
        data = self.get_payment_initial_data(
            external_id='evt_0000',
            notification_type='payment_intent.payment_failed',
            payment_intent='pi_420',
            metadata={'terminal_payment': 'True'},
            last_payment_error={
                "code": "card_declined",
                "decline_code": "do_not_honor",
            },
        )
        data_with_terminal_order = self.get_payment_initial_data(
            external_id='wrong_evt',
            notification_type='payment_intent.payment_failed',
            payment_intent='wrong_intent',
            metadata={'order_id': '1111', 'order_payment_id': '2222'},
        )

        resp = self.client.post(
            self.url,
            data=json.dumps(data_with_terminal_order),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        assert self.pr.status == receipt_status.PENDING
        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        assert self.payment_intent.payment_row.status == receipt_status.PAYMENT_FAILED

    @responses.activate
    @mock.patch(
        'webapps.stripe_integration.tools.stripe_payment_intent_idempotent_cancel',
        return_value=True,
    )
    def test_payment_intent_failed_handler_without_decline_code(
        self,
        _connection_token_mock,
        _location_mock,
        _idempotent_cancel_mock,
    ):
        responses.add(
            responses.POST,
            'https://api.stripe.com/v1/payment_intents/pi_420/cancel',
            status=status.HTTP_200_OK,
            json={
                'status': 'canceled',
            },
        )
        data = self.get_payment_initial_data(
            external_id='evt_0000',
            notification_type='payment_intent.payment_failed',
            payment_intent='pi_420',
            metadata={'terminal_payment': 'True'},
            last_payment_error={
                "code": "incorrect_number",
            },
        )
        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        assert self.payment_intent.payment_row.status == receipt_status.PAYMENT_FAILED

    @parameterized.expand(
        [
            (receipt_status.PENDING, 1),
            (receipt_status.PAYMENT_SUCCESS, 0),
            (receipt_status.PAYMENT_CANCELED, 0),
        ]
    )
    @mock.patch(
        'webapps.stripe_integration.webhooks_account.StripeProvider.refresh_tip_fom_payment_intent'
    )
    @mock.patch('webapps.stripe_integration.webhooks_account.StripeProvider.capture_payment_intent')
    def test_amount_capturable_updated_handler(
        self,
        initial_pr_status,
        expected_capture_call_count,
        capture_mock,
        _refresh_tip_mock,
        _connection_token_mock,
        _location_mock,
    ):
        self.pr.status = initial_pr_status
        self.pr.save()
        data = self.get_payment_initial_data(
            external_id='evt_0000',
            notification_type='payment_intent.amount_capturable_updated',
            payment_intent='pi_420',
            metadata={'terminal_payment': 'True'},
        )

        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        assert capture_mock.call_count == expected_capture_call_count

    @override_eppo_feature_flag({KIPMOTOWebhookFlag.flag_name: True})
    @responses.activate
    @mock.patch(
        'webapps.stripe_integration.tools.stripe_payment_intent_idempotent_cancel',
        return_value=True,
    )
    def test_payment_failed_handler_with_kip_flag(
        self,
        _connection_token_mock,
        _location_mock,
        _idempotent_cancel_mock,
    ):
        responses.add(
            responses.POST,
            'https://api.stripe.com/v1/payment_intents/pi_420/cancel',
            status=status.HTTP_200_OK,
            json={
                'status': 'canceled',
            },
        )
        data = self.get_payment_initial_data(
            external_id='evt_0000',
            notification_type='payment_intent.payment_failed',
            payment_intent='pi_420',
            metadata={'terminal_payment': 'True'},
            last_payment_error={
                "code": "card_declined",
                "decline_code": "do_not_honor",
            },
        )
        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        assert self.payment_intent.payment_row.status == receipt_status.PAYMENT_FAILED

    @override_eppo_feature_flag({KIPMOTOWebhookFlag.flag_name: True})
    @responses.activate
    @mock.patch(
        'webapps.stripe_integration.tools.stripe_payment_intent_idempotent_cancel',
        return_value=True,
    )
    def test_payment_failed_handler_with_kip_flag_and_moto(
        self,
        _connection_token_mock,
        _location_mock,
        _idempotent_cancel_mock,
    ):
        responses.add(
            responses.POST,
            'https://api.stripe.com/v1/payment_intents/pi_420/cancel',
            status=status.HTTP_200_OK,
            json={'status': 'canceled'},
        )
        data = self.get_payment_initial_data(
            external_id='evt_0000',
            notification_type='payment_intent.payment_failed',
            payment_intent='pi_420',
            metadata={'terminal_payment': 'True'},
            last_payment_error={
                "code": "card_declined",
                "decline_code": "do_not_honor",
            },
        )
        data['data']['object']['payment_method_options'] = {"card": {"moto": True}}
        resp = self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert resp.status_code == status.HTTP_200_OK
        assert self.payment_intent.payment_row.status == receipt_status.PAYMENT_FAILED

    @patch('webapps.payment_providers.views.webhooks.stripe.StripeWebhookAccountView.as_view')
    def test_calls_stripe_webhook_account_view_for_new_payment_intent_structure(
        self, mock_stripe_account_view
    ):
        payment = baker.make(
            Payment,
            provider_code=PaymentProviderCode.STRIPE,
        )
        baker.make(
            StripePaymentIntentV2,
            payment=payment,
            external_id='pi_420',
        )

        data = self.get_payment_initial_data(
            external_id='evt_0000',
            notification_type='payment_intent.succeeded',
            payment_intent='pi_420',
            metadata={'terminal_payment': 'True'},
        )

        mock_response = Response(status=status.HTTP_200_OK)
        mock_stripe_account_view.return_value = lambda request: mock_response

        self.client.post(
            self.url,
            data=json.dumps(data),
            content_type="application/json",
            **{"HTTP_stripe-signature": "CORRECT"},
        )
        assert mock_stripe_account_view.called
