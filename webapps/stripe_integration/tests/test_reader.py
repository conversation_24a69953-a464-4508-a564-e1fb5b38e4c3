import pytest
from django.test import override_settings
from django.utils.crypto import get_random_string
from model_bakery import baker
from rest_framework import status

from service.tests import BaseAsyncHTTPTest
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import AccountHolder, StripeAccountHolder
from webapps.pos.enums import receipt_status
from webapps.pos.models import (
    PaymentRow,
    Receipt,
    POS,
)
from webapps.stripe_integration.enums import StripeAccountStatus, StripeTerminalAction
from webapps.stripe_integration.exceptions import StripeAccountNotVerified
from webapps.stripe_integration.models import StripeAccount, StripePaymentIntent
from webapps.stripe_integration.provider import StripeProvider
from webapps.stripe_integration.tests.mocks import (
    mock_stripe_connection_token_create,
    mock_stripe_location_create,
    mock_stripe_reader_create,
    mock_stripe_reader_list,
)


@mock_stripe_reader_create
@mock_stripe_location_create
@pytest.mark.django_db
@pytest.mark.skip(reason='external api request')
class StripeReaderHandlerTests(BaseAsyncHTTPTest):
    url = "/business_api/me/businesses/{business_id}/stripe/reader/"

    def setUp(self):
        super().setUp()
        self.pos = baker.make(POS, business=self.business)
        self.stripe_account = baker.make(
            StripeAccount,
            pos=self.pos,
            status=StripeAccountStatus.VERIFIED,
            charges_enabled=False,
            payouts_enabled=False,
        )
        StripeProvider.get_or_create_location(self.stripe_account)

    @override_settings(POS__STRIPE_TERMINAL=True)
    def test_post_internet_reader(self, location_mock, reader_mock):
        url = self.url.format(business_id=self.business.id)

        body = {
            "label": get_random_string(12),
            "registration_code": get_random_string(12),
        }

        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == status.HTTP_200_OK
        assert resp.json['reader']

    @override_settings(POS__STRIPE_TERMINAL=True)
    def test_post_internet_reader_invalid(self, location_mock, reader_mock):
        url = self.url.format(business_id=self.business.id)

        body = {
            "label": get_random_string(12),
        }
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['field'] == 'registration_code'

        body = {
            "registration_code": get_random_string(12),
        }
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['field'] == 'label'

        body = {
            "registration_code": "INVALID",
            "label": get_random_string(12),
        }

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST


@mock_stripe_reader_list
@mock_stripe_location_create
@pytest.mark.django_db
class StripeReadersHandlerTests(BaseAsyncHTTPTest):
    url = "/business_api/me/businesses/{business_id}/stripe/readers/"

    @override_settings(POS__STRIPE_TERMINAL=True)
    def test_get(self, location_mock, reader_mock):
        pos = baker.make(POS, business=self.business)
        baker.make(
            AccountHolder,
        )
        business_wallet = PaymentGatewayPort.get_or_create_business_wallet(
            self.business.id,
            statement_name=self.business.name,
        )[0]
        baker.make(
            StripeAccountHolder,
            account_holder_id=business_wallet.account_holder_id,
            external_id='test_intent',
        )
        stripe_account = baker.make(
            StripeAccount,
            pos=pos,
            status=StripeAccountStatus.VERIFICATION_PENDING,
            charges_enabled=False,
            payouts_enabled=False,
            external_id='test_intent',
        )
        payment_intent = baker.make(
            StripePaymentIntent,
            external_id='test_intent',
            account=pos.stripe_account,
        )
        receipt = baker.make(
            Receipt,
            status_code=receipt_status.PAYMENT_CANCELED,
        )
        baker.make(
            PaymentRow,
            amount=10,
            receipt=receipt,
            status=receipt_status.PAYMENT_CANCELED,
            intents=(payment_intent,),
        )

        assert stripe_account.locations.count() == 0

        url = self.url.format(business_id=self.business.id)

        self.assertRaises(
            StripeAccountNotVerified,
            StripeProvider.get_or_create_location,
            stripe_account,
        )

        stripe_account.status = StripeAccountStatus.VERIFIED
        stripe_account.save()
        stripe_location = StripeProvider.get_or_create_location(stripe_account)

        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert stripe_account.locations.count() == 1
        readers = resp.json['readers']
        assert len(readers) == 2
        assert readers[0]['location'] == stripe_location.external_id
        assert readers[0]['action']['status'] == StripeTerminalAction.SUCCESS
        assert readers[1]['location'] == stripe_location.external_id
        assert readers[1]['action']['status'] == StripeTerminalAction.FAIL


@mock_stripe_location_create
@mock_stripe_connection_token_create
@pytest.mark.django_db
class StripeConnectionTokenHandlerTests(BaseAsyncHTTPTest):
    url = "/business_api/me/businesses/{business_id}/stripe/connection_token/"

    @override_settings(POS__STRIPE_TERMINAL=True)
    def test_get(self, connection_token_mock, location_mock):
        pos = baker.make(POS, business=self.business)
        baker.make(
            StripeAccount,
            pos=pos,
            status=StripeAccountStatus.VERIFIED,
            charges_enabled=True,
            payouts_enabled=True,
        )

        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
