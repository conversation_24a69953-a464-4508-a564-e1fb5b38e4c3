import datetime
from collections import namedtuple
from typing import Iterable, List, Optional

import pytz
import stripe

from lib.datetime_utils import get_week_day
from lib.serializers import safe_get
from lib.tools import format_currency, major_unit
from webapps.business.models import Business
from webapps.stripe_integration.enums import (
    StripeAccountDashboardStatus,
    StripePaymentIntentMetadata,
    StripePaymentIntentStatus,
)
from webapps.stripe_integration.exceptions import CancelPaymentIntentNotPossible
from webapps.stripe_integration.stripe_exceptions import (
    CancelingAlreadyCanceledPaymentIntentException,
    CancelingAlreadyFinishedPaymentIntentException,
)


def iterable_to_dict(l: Iterable, key: List[str]) -> dict:
    """
    example key: ['source'] or ['source', 'billing_details', 'address', 'state']
    """

    result = {}

    for elem in l:
        val = safe_get(elem, key)

        if val is None:
            raise Exception('Incorrect key')  # pylint: disable=broad-exception-raised

        result[val] = elem

    return result


def format_stripe_amount(amount: Optional[int]) -> Optional[str]:
    if amount is None:
        return None
    return format_currency(major_unit(amount))


def stripe_payment_intent_idempotent_cancel(stripe_pi_id: str) -> bool:
    """
    Returns True if after this action status of a given payment intent == cancel,
    otherwise (if something went wrong, shouldn't happen), returns False
    """
    try:
        intent = stripe.PaymentIntent.cancel(stripe_pi_id)
    except stripe.error.InvalidRequestError as e:
        if CancelingAlreadyFinishedPaymentIntentException.is_equal(e):
            raise CancelPaymentIntentNotPossible from e  # pylint: disable=raising-bad-type
        if not CancelingAlreadyCanceledPaymentIntentException.is_equal(e):
            raise e
        return True

    return intent.status == StripePaymentIntentStatus.CANCELED


DashboardStatus = namedtuple('DashboardStatus', ['name', 'reasons'])


def get_stripe_account_dashboard_status(account: stripe.Account) -> DashboardStatus:
    # according to https://stripe.com/docs/connect/dashboard#status-badges
    requirements = account.requirements

    if disabled_reason := requirements.disabled_reason:
        if disabled_reason == 'requirements.pending_verification':
            name = StripeAccountDashboardStatus.PENDING
            reasons = requirements.pending_verification
        elif disabled_reason == 'requirements.past_due':
            name = StripeAccountDashboardStatus.RESTRICTED
            reasons = requirements.past_due
        else:
            name = StripeAccountDashboardStatus.REJECTED
            reasons = [disabled_reason]
    else:
        if account.charges_enabled and account.payouts_enabled and requirements.currently_due:
            name = StripeAccountDashboardStatus.RESTRICTED_SOON
            reasons = requirements.currently_due
        elif (
            account.charges_enabled
            and account.payouts_enabled
            and requirements.eventually_due
            and not requirements.current_deadline
        ):
            name = StripeAccountDashboardStatus.ENABLED
            reasons = requirements.eventually_due
        else:
            name = StripeAccountDashboardStatus.COMPLETED
            reasons = []

    return DashboardStatus(name, reasons)


def payment_type_from_intent(
    stripe_payment_intent: stripe.PaymentIntent,
) -> StripePaymentIntentMetadata:
    for payment_type in StripePaymentIntentMetadata.values():
        if stripe_payment_intent['metadata'].get(payment_type):
            return payment_type


def is_kip_moto_from_intent(stripe_payment_intent: stripe.PaymentIntent) -> bool:
    # MOTO: check w charges -> data -> 0 -> payment_method_details -> card -> moto
    try:
        charges = stripe_payment_intent.get('charges', {}).get('data', [])
        if charges:
            card_details = charges[0].get('payment_method_details', {}).get('card', {})
            return card_details.get('moto') is True
    except (ValueError, IndexError) as ex:
        raise ex
    return False


def calculate_stripe_payout_due_date(business: Business):
    date = business.tznow
    next_day_gmt = date.astimezone(pytz.UTC) + datetime.timedelta(days=1)
    # Payouts are issued by Stripe at 00:00 GMT on business days (Mon-Fri)
    payout_time_gmt = datetime.datetime(
        year=next_day_gmt.year,
        month=next_day_gmt.month,
        day=next_day_gmt.day,
        hour=0,
        second=0,
        tzinfo=pytz.UTC,
    )
    # if calculated payout day is Saturday/Sunday -> get next closest Monday
    if get_week_day(payout_time_gmt) in [6, 0]:
        payout_time_gmt = payout_time_gmt + datetime.timedelta(
            days=(get_week_day(payout_time_gmt) + 6) / 6
        )
    return payout_time_gmt


def get_fees_from_fee_settings(fee_settings) -> tuple[float, float]:
    """
    Helper method to convert WalletFeeSettings
    to provision and fixed fee in old format - floats
    """
    plan_provision = fee_settings.fast_payout_provision_percentage / 100
    plan_fixed_fee = fee_settings.fast_payout_provision_fee / 100

    return plan_provision, plan_fixed_fee


def synchronize_stripe_account_with_stripe_account_holder(stripe_account_holder):
    # !!! currently, this updates only 1 field !!! add others if necessary
    # won't send a signal StripeAccount -> StripeAccountHolder
    from webapps.payment_providers.models import StripeAccountHolder
    from webapps.stripe_integration.models import StripeAccount

    acc: StripeAccountHolder = stripe_account_holder
    qs = StripeAccount.objects.filter(external_id=acc.external_id)
    qs.update(
        default_payout_method_for_fast_payout=acc.default_payout_method_for_fast_payout,
    )
