from django.apps import AppConfig


class UserConfig(AppConfig):
    name = 'webapps.user'

    def ready(self):
        import webapps.user.signals  # pylint: disable=unused-import

        from webapps.user.v2.containers import container
        from webapps.user.v2.infrastructure.repositories.user import (
            AbstractUserRepository,
            UserRepository,
        )

        from webapps.user.v2.application.services.validators import (
            AbstractBusinessUserEmailValidator,
            BusinessUserEmailValidator,
        )
        from webapps.user.v2.application.services.user_details import (
            AbstractUserDetailsService,
            UserDetailsService,
        )
        from webapps.user.v2.domain.services.validators import (
            AbstractUserEmailValidator,
            UserEmailValidator,
        )

        container[AbstractUserRepository] = UserRepository
        container[AbstractUserDetailsService] = UserDetailsService
        container[AbstractUserEmailValidator] = UserEmailValidator
        container[AbstractBusinessUserEmailValidator] = BusinessUserEmailValidator
