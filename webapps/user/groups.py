from lib.enums import StrEnum


class GroupName(StrEnum):
    """Custom groups editable in Admin"""

    ADD_SUBSCRIPTION_LISTING = 'add_subscription_listing'
    ADYEN_PAYMENTS_LOGS = 'adyen_payments_logs'
    BILLING_ADMIN = 'billing_admin'
    BILLING_ADVANCED_USER = 'billing_advanced_user'
    BILLING_MANAGER = 'billing_manager'
    BILLING_MISMATCHED_REPORT = 'billing_mismatched_report'
    BILLING_SUPERUSER = 'billing_superuser'
    BILLING_USER = 'billing_user'
    BOOKING_REMOVE = 'booking_remove'
    BULK_UPDATE_SUBSCRIPTION_BUYERS = 'bulk_update_subscription_buyers'
    CUSTOMER_IMPORT_CLEANER = 'customer_import_cleaner'
    CUSTOM_PUSH_ALLOWED = 'custom_push_allowed'
    SECURITY_ADMIN = 'security_admin'
    DISABLE_NOTIFICATIONS_EDITOR = 'disable_notifications_edit'
    GROWTH = 'growth'
    MARKET_PAY_MANUAL_ACTIONS = 'market_pay_manual_actions'
    NAVISION_ADMIN = 'navision_admin'
    NAVISION_OPERATIONS = 'navision_operations'
    PAYMENT_ROW_ISSUE_REFUND = 'payment_row_issue_refund'
    PAYOUT_METHOD_CHANGE = 'payout_method_change'
    PAYOUTS_BANNER_IN_CALENDAR = 'payouts_banner_in_calendar'
    POS_SPECIAL_ACTIONS = 'pos_special_actions'
    LIMITED_QUICK_REPORTS_ACCESS = 'limited_quick_reports_access'
    REPORT_RETRIEVE_OLD_PRODUCT = 'report_retrieve_old_product'
    STRIPE_MANUAL_ACTIONS = 'stripe_manual_actions'
    SUBDOMAIN_EDITOR = 'subdomain_editor'
    SUBSCRIPTION_AUTOMATIC_REPORTS = 'subscription_automatic_reports'
    SUBSCRIPTION_EDITOR = 'subscription_editor'
    SUBSCRIPTION_REPORTS = 'subscription_reports'
    MASS_STATUS_EDITOR = 'mass_status_editor'
    ZIP_CODE_EDITOR = 'zip_code_editor'
    REMOVAL_CANDIDATE = 'removal_candidate'


class IntranetGroupName(StrEnum):
    BUSINESS_CATEGORY_MANAGER = 'business_category_manager'
