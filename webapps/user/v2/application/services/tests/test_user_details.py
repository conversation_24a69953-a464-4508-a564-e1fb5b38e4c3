from unittest import TestCase

from webapps.user.v2.application.services.user_details import UserDetailsService
from webapps.user.v2.domain.enums import UserProfileType
from webapps.user.v2.domain.services.tests.test_validators import FakeUserRepository


class TestUserProfileType(TestCase):
    def setUp(self):
        self.service = UserDetailsService(user_repository=FakeUserRepository())

    def test_cust_profile_type(self):
        result = self.service.user_profile_type('<EMAIL>')

        assert result == [UserProfileType.CUSTOMER]

    def test_biz_profile_type(self):
        result = self.service.user_profile_type('<EMAIL>')

        assert result == [UserProfileType.BUSINESS]

    def test_biz_cus_profile_type(self):
        result = self.service.user_profile_type('<EMAIL>')

        assert result == [UserProfileType.BUSINESS, UserProfileType.CUSTOMER]
