from abc import ABC, abstractmethod

from webapps.user.v2.domain.enums import UserProfileType
from webapps.user.v2.infrastructure.repositories.user import AbstractUserRepository


class AbstractUserDetailsService(ABC):
    @abstractmethod
    def user_profile_type(self, email: str) -> list[UserProfileType] | list: ...


class UserDetailsService:
    def __init__(self, user_repository: AbstractUserRepository):
        self.user_repository = user_repository

    def user_profile_type(self, email: str) -> list[UserProfileType] | list:
        return self.user_repository.get_profile_type(email)
