from unittest import TestCase

import mock
from parameterized import parameterized
import pytest

from lib.exceptions import BooksyValidationError
from webapps.user.v2.domain.enums import UserProfileType

from webapps.user.v2.domain.services.validators import UserEmailValidator
from webapps.user.v2.infrastructure.repositories.user import AbstractUserRepository


class FakeUserRepository(AbstractUserRepository):

    def exists(self, email: str) -> bool:
        return email in {'<EMAIL>'}

    def get_profile_type(self, email: str) -> list[UserProfileType]:
        if 'biz_cus' in email:
            return [UserProfileType.BUSINESS, UserProfileType.CUSTOMER]
        if 'cus' in email:
            return [UserProfileType.CUSTOMER]
        if 'biz' in email:
            return [UserProfileType.BUSINESS]


@mock.patch(
    'webapps.user.v2.domain.services.validators.EMAIL_DOMAINS', ["invalid.com", "disposable.pl"]
)
class TestAccountExists(TestCase):

    def setUp(self):
        self.service = UserEmailValidator(user_repository=FakeUserRepository())

    def test_email_exists(self):
        result = self.service.user_exists('<EMAIL>', verify_domain_disposable=True)

        assert result is True

    def test_email_doesnt_exist(self):
        result = self.service.user_exists('<EMAIL>', verify_domain_disposable=True)

        assert result is False

    def test_email_domain_disposable(self):
        with pytest.raises(BooksyValidationError) as exception_info:
            self.service.user_exists('<EMAIL>', verify_domain_disposable=True)

        assert exception_info.value.code == 'invalid'
        assert exception_info.value.field == 'email'
        assert (
            exception_info.value.description
            == 'Email not accepted. Please use a different email address.'
        )

    def test_email_domain_not_disposable(self):
        result = self.service.user_exists('<EMAIL>', verify_domain_disposable=True)

        assert result is False

    def test_email_domain_disposable_verification_off(self):
        result = self.service.user_exists('<EMAIL>', verify_domain_disposable=False)

        assert result is False

    def test_email_invalid_format(self):
        result = self.service.user_exists('not.email', verify_domain_disposable=True)

        assert result is False

    def test_sayan_prefix_striped(self):
        result = self.service.user_exists('+<EMAIL>', verify_domain_disposable=True)

        assert result is True

    @parameterized.expand(['<EMAIL>', '<EMAIL>'])
    def test_no_sayan_prefix(self, email: str):
        result = self.service.user_exists(email, verify_domain_disposable=True)

        assert result is False
